// utils/storyComprehensionData.js
// 故事理解游戏题目数据

/**
 * 故事理解游戏题目数据
 * 适合3-6岁儿童的听觉理解训练
 */
const storyComprehensionQuestions = [
  // 家庭场景题目 (8题)
  {
    id: 'story_001',
    category: 'family',
    difficulty: 'simple',
    story: '周末，妈妈在厨房做饭，爸爸在客厅看电视，哥哥在书房看书，妹妹在客房练琴。',
    questions: [
      {
        text: '妈妈在做什么？',
        options: ['做饭', '看电视', '看书', '练琴'],
        answer: 0
      },
      {
        text: '爸爸在哪里？',
        options: ['厨房', '客厅', '书房', '客房'],
        answer: 1
      },
      {
        text: '哥哥在做什么？',
        options: ['做饭', '看电视', '看书', '练琴'],
        answer: 2
      },
      {
        text: '妹妹在哪里？',
        options: ['厨房', '客厅', '书房', '客房'],
        answer: 3
      }
    ]
  },

  {
    id: 'story_002',
    category: 'family',
    difficulty: 'simple',
    story: '晚上，爷爷在阳台浇花，奶奶在卧室整理衣服，小明在客厅写作业，小红在厨房帮忙洗碗。',
    questions: [
      {
        text: '爷爷在哪里？',
        options: ['阳台', '卧室', '客厅', '厨房'],
        answer: 0
      },
      {
        text: '奶奶在做什么？',
        options: ['浇花', '整理衣服', '写作业', '洗碗'],
        answer: 1
      },
      {
        text: '小明在做什么？',
        options: ['浇花', '整理衣服', '写作业', '洗碗'],
        answer: 2
      },
      {
        text: '小红在哪里？',
        options: ['阳台', '卧室', '客厅', '厨房'],
        answer: 3
      }
    ]
  },

  {
    id: 'story_003',
    category: 'family',
    difficulty: 'medium',
    story: '早上，妈妈在厨房准备早餐，爸爸在卫生间刷牙，姐姐在卧室穿衣服，弟弟在客厅看动画片。',
    questions: [
      {
        text: '妈妈在做什么？',
        options: ['准备早餐', '刷牙', '穿衣服', '看动画片'],
        answer: 0
      },
      {
        text: '爸爸在哪里？',
        options: ['厨房', '卫生间', '卧室', '客厅'],
        answer: 1
      },
      {
        text: '姐姐在做什么？',
        options: ['准备早餐', '刷牙', '穿衣服', '看动画片'],
        answer: 2
      },
      {
        text: '弟弟在哪里？',
        options: ['厨房', '卫生间', '卧室', '客厅'],
        answer: 3
      }
    ]
  },

  {
    id: 'story_004',
    category: 'family',
    difficulty: 'medium',
    story: '下午，外公在花园里种菜，外婆在厨房煮汤，小华在书房画画，小丽在阳台晒衣服。',
    questions: [
      {
        text: '外公在做什么？',
        options: ['种菜', '煮汤', '画画', '晒衣服'],
        answer: 0
      },
      {
        text: '外婆在哪里？',
        options: ['花园', '厨房', '书房', '阳台'],
        answer: 1
      },
      {
        text: '小华在做什么？',
        options: ['种菜', '煮汤', '画画', '晒衣服'],
        answer: 2
      },
      {
        text: '小丽在哪里？',
        options: ['花园', '厨房', '书房', '阳台'],
        answer: 3
      }
    ]
  },

  {
    id: 'story_005',
    category: 'family',
    difficulty: 'medium',
    story: '傍晚，爸爸在车库修车，妈妈在客厅拖地，哥哥在阳台给花浇水，妹妹在卧室整理玩具。',
    questions: [
      {
        text: '爸爸在哪里？',
        options: ['车库', '客厅', '阳台', '卧室'],
        answer: 0
      },
      {
        text: '妈妈在做什么？',
        options: ['修车', '拖地', '浇水', '整理玩具'],
        answer: 1
      },
      {
        text: '哥哥在做什么？',
        options: ['修车', '拖地', '浇水', '整理玩具'],
        answer: 2
      },
      {
        text: '妹妹在哪里？',
        options: ['车库', '客厅', '阳台', '卧室'],
        answer: 3
      }
    ]
  },

  {
    id: 'story_006',
    category: 'family',
    difficulty: 'complex',
    story: '周六，奶奶在厨房包饺子，爷爷在客厅看报纸，叔叔在书房用电脑工作，阿姨在阳台晾被子。',
    questions: [
      {
        text: '奶奶在做什么？',
        options: ['包饺子', '看报纸', '用电脑', '晾被子'],
        answer: 0
      },
      {
        text: '爷爷在哪里？',
        options: ['厨房', '客厅', '书房', '阳台'],
        answer: 1
      },
      {
        text: '叔叔在做什么？',
        options: ['包饺子', '看报纸', '用电脑', '晾被子'],
        answer: 2
      },
      {
        text: '阿姨在哪里？',
        options: ['厨房', '客厅', '书房', '阳台'],
        answer: 3
      }
    ]
  },

  {
    id: 'story_007',
    category: 'family',
    difficulty: 'complex',
    story: '雨天，妈妈在卧室叠衣服，爸爸在厨房洗碗，小明在客厅搭积木，小红在书房练字。',
    questions: [
      {
        text: '妈妈在哪里？',
        options: ['卧室', '厨房', '客厅', '书房'],
        answer: 0
      },
      {
        text: '爸爸在做什么？',
        options: ['叠衣服', '洗碗', '搭积木', '练字'],
        answer: 1
      },
      {
        text: '小明在做什么？',
        options: ['叠衣服', '洗碗', '搭积木', '练字'],
        answer: 2
      },
      {
        text: '小红在哪里？',
        options: ['卧室', '厨房', '客厅', '书房'],
        answer: 3
      }
    ]
  },

  {
    id: 'story_008',
    category: 'family',
    difficulty: 'complex',
    story: '节假日，外公在花园修剪花草，外婆在客厅织毛衣，表哥在阳台放风筝，表妹在卧室听音乐。',
    questions: [
      {
        text: '外公在做什么？',
        options: ['修剪花草', '织毛衣', '放风筝', '听音乐'],
        answer: 0
      },
      {
        text: '外婆在哪里？',
        options: ['花园', '客厅', '阳台', '卧室'],
        answer: 1
      },
      {
        text: '表哥在做什么？',
        options: ['修剪花草', '织毛衣', '放风筝', '听音乐'],
        answer: 2
      },
      {
        text: '表妹在哪里？',
        options: ['花园', '客厅', '阳台', '卧室'],
        answer: 3
      }
    ]
  },

  // 学校场景题目 (4题)
  {
    id: 'story_009',
    category: 'school',
    difficulty: 'simple',
    story: '上课时间，老师在讲台上讲课，小明在座位上听课，小红在黑板前回答问题，小华在后面整理书包。',
    questions: [
      {
        text: '老师在哪里？',
        options: ['讲台上', '座位上', '黑板前', '后面'],
        answer: 0
      },
      {
        text: '小明在做什么？',
        options: ['讲课', '听课', '回答问题', '整理书包'],
        answer: 1
      },
      {
        text: '小红在哪里？',
        options: ['讲台上', '座位上', '黑板前', '后面'],
        answer: 2
      },
      {
        text: '小华在做什么？',
        options: ['讲课', '听课', '回答问题', '整理书包'],
        answer: 3
      }
    ]
  },

  {
    id: 'story_010',
    category: 'school',
    difficulty: 'medium',
    story: '课间休息，小丽在图书馆看书，小强在操场踢球，小美在教室画画，小刚在走廊和同学聊天。',
    questions: [
      {
        text: '小丽在哪里？',
        options: ['图书馆', '操场', '教室', '走廊'],
        answer: 0
      },
      {
        text: '小强在做什么？',
        options: ['看书', '踢球', '画画', '聊天'],
        answer: 1
      },
      {
        text: '小美在哪里？',
        options: ['图书馆', '操场', '教室', '走廊'],
        answer: 2
      },
      {
        text: '小刚在做什么？',
        options: ['看书', '踢球', '画画', '聊天'],
        answer: 3
      }
    ]
  },

  {
    id: 'story_011',
    category: 'school',
    difficulty: 'medium',
    story: '午餐时间，同学们在食堂吃饭，小王在窗口排队打饭，小李在座位上吃饭，小张在收拾餐具，小赵在洗手池洗手。',
    questions: [
      {
        text: '小王在做什么？',
        options: ['排队打饭', '吃饭', '收拾餐具', '洗手'],
        answer: 0
      },
      {
        text: '小李在哪里？',
        options: ['窗口', '座位上', '餐具区', '洗手池'],
        answer: 1
      },
      {
        text: '小张在做什么？',
        options: ['排队打饭', '吃饭', '收拾餐具', '洗手'],
        answer: 2
      },
      {
        text: '小赵在哪里？',
        options: ['窗口', '座位上', '餐具区', '洗手池'],
        answer: 3
      }
    ]
  },

  {
    id: 'story_012',
    category: 'school',
    difficulty: 'complex',
    story: '体育课上，体育老师在操场中央指导，小东在跑道上跑步，小西在沙坑练跳远，小南在器材室拿球，小北在看台上休息。',
    questions: [
      {
        text: '体育老师在哪里？',
        options: ['操场中央', '跑道上', '沙坑', '器材室'],
        answer: 0
      },
      {
        text: '小东在做什么？',
        options: ['指导', '跑步', '跳远', '拿球'],
        answer: 1
      },
      {
        text: '小西在哪里？',
        options: ['操场中央', '跑道上', '沙坑', '器材室'],
        answer: 2
      },
      {
        text: '小南在做什么？',
        options: ['指导', '跑步', '跳远', '拿球'],
        answer: 3
      }
    ]
  },

  // 户外场景题目 (4题)
  {
    id: 'story_013',
    category: 'outdoor',
    difficulty: 'simple',
    story: '在公园里，妈妈在长椅上看书，爸爸在草地上放风筝，小明在湖边喂鱼，小红在花园里拍照。',
    questions: [
      {
        text: '妈妈在哪里？',
        options: ['长椅上', '草地上', '湖边', '花园里'],
        answer: 0
      },
      {
        text: '爸爸在做什么？',
        options: ['看书', '放风筝', '喂鱼', '拍照'],
        answer: 1
      },
      {
        text: '小明在哪里？',
        options: ['长椅上', '草地上', '湖边', '花园里'],
        answer: 2
      },
      {
        text: '小红在做什么？',
        options: ['看书', '放风筝', '喂鱼', '拍照'],
        answer: 3
      }
    ]
  },

  {
    id: 'story_014',
    category: 'outdoor',
    difficulty: 'medium',
    story: '在超市里，妈妈在蔬菜区挑菜，爸爸在收银台排队，哥哥在玩具区看玩具，妹妹在推购物车。',
    questions: [
      {
        text: '妈妈在做什么？',
        options: ['挑菜', '排队', '看玩具', '推购物车'],
        answer: 0
      },
      {
        text: '爸爸在哪里？',
        options: ['蔬菜区', '收银台', '玩具区', '推车旁'],
        answer: 1
      },
      {
        text: '哥哥在哪里？',
        options: ['蔬菜区', '收银台', '玩具区', '推车旁'],
        answer: 2
      },
      {
        text: '妹妹在做什么？',
        options: ['挑菜', '排队', '看玩具', '推购物车'],
        answer: 3
      }
    ]
  },

  {
    id: 'story_015',
    category: 'outdoor',
    difficulty: 'medium',
    story: '在游乐场，小华在滑滑梯上玩耍，小丽在秋千上荡秋千，小强在沙池里堆沙堡，小美在跷跷板上玩。',
    questions: [
      {
        text: '小华在哪里？',
        options: ['滑滑梯上', '秋千上', '沙池里', '跷跷板上'],
        answer: 0
      },
      {
        text: '小丽在做什么？',
        options: ['玩滑梯', '荡秋千', '堆沙堡', '玩跷跷板'],
        answer: 1
      },
      {
        text: '小强在哪里？',
        options: ['滑滑梯上', '秋千上', '沙池里', '跷跷板上'],
        answer: 2
      },
      {
        text: '小美在做什么？',
        options: ['玩滑梯', '荡秋千', '堆沙堡', '玩跷跷板'],
        answer: 3
      }
    ]
  },

  {
    id: 'story_016',
    category: 'outdoor',
    difficulty: 'complex',
    story: '在医院里，医生在诊室给病人看病，护士在走廊发药，小明在候诊区等待，妈妈在挂号处排队。',
    questions: [
      {
        text: '医生在做什么？',
        options: ['看病', '发药', '等待', '排队'],
        answer: 0
      },
      {
        text: '护士在哪里？',
        options: ['诊室', '走廊', '候诊区', '挂号处'],
        answer: 1
      },
      {
        text: '小明在哪里？',
        options: ['诊室', '走廊', '候诊区', '挂号处'],
        answer: 2
      },
      {
        text: '妈妈在做什么？',
        options: ['看病', '发药', '等待', '排队'],
        answer: 3
      }
    ]
  },

  // 节日特殊场景题目 (4题)
  {
    id: 'story_017',
    category: 'festival',
    difficulty: 'simple',
    story: '生日聚会上，小寿星在客厅吹蜡烛，妈妈在厨房切蛋糕，爸爸在阳台挂彩带，朋友们在沙发上唱生日歌。',
    questions: [
      {
        text: '小寿星在做什么？',
        options: ['吹蜡烛', '切蛋糕', '挂彩带', '唱歌'],
        answer: 0
      },
      {
        text: '妈妈在哪里？',
        options: ['客厅', '厨房', '阳台', '沙发上'],
        answer: 1
      },
      {
        text: '爸爸在做什么？',
        options: ['吹蜡烛', '切蛋糕', '挂彩带', '唱歌'],
        answer: 2
      },
      {
        text: '朋友们在哪里？',
        options: ['客厅', '厨房', '阳台', '沙发上'],
        answer: 3
      }
    ]
  },

  {
    id: 'story_018',
    category: 'festival',
    difficulty: 'medium',
    story: '春节期间，爷爷在门口贴春联，奶奶在厨房包饺子，爸爸在客厅看春晚，孩子们在院子里放鞭炮。',
    questions: [
      {
        text: '爷爷在做什么？',
        options: ['贴春联', '包饺子', '看春晚', '放鞭炮'],
        answer: 0
      },
      {
        text: '奶奶在哪里？',
        options: ['门口', '厨房', '客厅', '院子里'],
        answer: 1
      },
      {
        text: '爸爸在做什么？',
        options: ['贴春联', '包饺子', '看春晚', '放鞭炮'],
        answer: 2
      },
      {
        text: '孩子们在哪里？',
        options: ['门口', '厨房', '客厅', '院子里'],
        answer: 3
      }
    ]
  },

  {
    id: 'story_019',
    category: 'festival',
    difficulty: 'complex',
    story: '中秋节晚上，全家在阳台赏月，爷爷在讲嫦娥的故事，奶奶在分月饼，小明在用望远镜看月亮，小红在画月亮。',
    questions: [
      {
        text: '爷爷在做什么？',
        options: ['讲故事', '分月饼', '看月亮', '画月亮'],
        answer: 0
      },
      {
        text: '奶奶在做什么？',
        options: ['讲故事', '分月饼', '看月亮', '画月亮'],
        answer: 1
      },
      {
        text: '小明在做什么？',
        options: ['讲故事', '分月饼', '看月亮', '画月亮'],
        answer: 2
      },
      {
        text: '小红在做什么？',
        options: ['讲故事', '分月饼', '看月亮', '画月亮'],
        answer: 3
      }
    ]
  },

  {
    id: 'story_020',
    category: 'festival',
    difficulty: 'complex',
    story: '六一儿童节，学校举办活动，小演员在舞台上表演，老师在台下拍照，家长们在观众席鼓掌，其他同学在后台准备节目。',
    questions: [
      {
        text: '小演员在哪里？',
        options: ['舞台上', '台下', '观众席', '后台'],
        answer: 0
      },
      {
        text: '老师在做什么？',
        options: ['表演', '拍照', '鼓掌', '准备节目'],
        answer: 1
      },
      {
        text: '家长们在哪里？',
        options: ['舞台上', '台下', '观众席', '后台'],
        answer: 2
      },
      {
        text: '其他同学在做什么？',
        options: ['表演', '拍照', '鼓掌', '准备节目'],
        answer: 3
      }
    ]
  }
];

/**
 * 获取随机题目
 * @param {number} count 题目数量
 * @param {string} category 题目分类 (可选)
 * @param {string} difficulty 难度等级 (可选)
 * @returns {Array} 题目数组
 */
function getRandomQuestions(count = 5, category = null, difficulty = null) {
  let filteredQuestions = storyComprehensionQuestions;

  if (category) {
    filteredQuestions = filteredQuestions.filter(q => q.category === category);
  }

  if (difficulty) {
    filteredQuestions = filteredQuestions.filter(q => q.difficulty === difficulty);
  }

  // 随机打乱并取指定数量
  const shuffled = filteredQuestions.sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

/**
 * 根据年龄组获取适合的题目
 * @param {string} ageGroup 年龄组 ('3-4', '4-5', '5-6')
 * @param {number} count 题目数量
 * @returns {Array} 题目数组
 */
function getQuestionsByAge(ageGroup, count = 5) {
  let difficulty;
  switch (ageGroup) {
    case '3-4':
      difficulty = 'simple';
      break;
    case '4-5':
      difficulty = 'medium';
      break;
    case '5-6':
      difficulty = 'complex';
      break;
    default:
      difficulty = 'medium';
  }

  return getRandomQuestions(count, null, difficulty);
}

module.exports = {
  storyComprehensionQuestions,
  getRandomQuestions,
  getQuestionsByAge
};
