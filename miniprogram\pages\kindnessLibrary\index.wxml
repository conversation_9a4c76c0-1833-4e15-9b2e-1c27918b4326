<!--善意行为库主页面-->
<view class="kindness-library-container">
  
  <!-- 背景装饰 -->
  <view class="library-background">
    <view class="floating-star star-1">⭐</view>
    <view class="floating-star star-2">✨</view>
    <view class="floating-star star-3">🌟</view>
    <view class="floating-star star-4">💫</view>
  </view>

  <!-- 页面标题 -->
  <view class="library-header">
    <view class="header-icon">🌟</view>
    <view class="header-title">善意行为库</view>
    <view class="header-subtitle">学习善意，传播温暖</view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-box">
      <view class="search-icon">🔍</view>
      <input class="search-input" 
             placeholder="搜索善意行为..." 
             value="{{searchKeyword}}"
             bindinput="onSearchInput"
             bindconfirm="onSearchConfirm" />
      <view class="search-clear" wx:if="{{searchKeyword}}" bindtap="onClearSearch">✕</view>
    </view>
  </view>

  <!-- 筛选标签 -->
  <view class="filter-section">
    <view class="filter-title">难度筛选：</view>
    <view class="filter-tags">
      <view class="filter-tag {{selectedDifficulty === 0 ? 'active' : ''}}" 
            bindtap="onSelectDifficulty" data-difficulty="0">
        全部
      </view>
      <view class="filter-tag {{selectedDifficulty === 1 ? 'active' : ''}}" 
            bindtap="onSelectDifficulty" data-difficulty="1">
        🌱 简单
      </view>
      <view class="filter-tag {{selectedDifficulty === 2 ? 'active' : ''}}" 
            bindtap="onSelectDifficulty" data-difficulty="2">
        🌸 中等
      </view>
      <view class="filter-tag {{selectedDifficulty === 3 ? 'active' : ''}}" 
            bindtap="onSelectDifficulty" data-difficulty="3">
        🌳 困难
      </view>
    </view>
  </view>

  <!-- 分类网格 -->
  <view class="categories-section" wx:if="{{!searchMode}}">
    <view class="section-title">
      <view class="title-icon">📚</view>
      <view class="title-text">善意分类</view>
    </view>
    
    <view class="categories-grid">
      <view class="category-card" 
            wx:for="{{categories}}" 
            wx:key="id"
            bindtap="onSelectCategory" 
            data-category="{{item.id}}">
        <view class="category-icon" style="color: {{item.color}};">{{item.icon}}</view>
        <view class="category-name">{{item.name}}</view>
        <view class="category-description">{{item.description}}</view>
        <view class="category-count">{{item.actionCount}}个行为</view>
        <view class="category-glow" style="background: {{item.color}};"></view>
      </view>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view class="search-results-section" wx:if="{{searchMode}}">
    <view class="section-title">
      <view class="title-icon">🔍</view>
      <view class="title-text">搜索结果 ({{searchResults.length}})</view>
    </view>
    
    <view class="actions-list" wx:if="{{searchResults.length > 0}}">
      <view class="action-item" 
            wx:for="{{searchResults}}" 
            wx:key="id"
            bindtap="onSelectAction" 
            data-action="{{item.id}}">
        <view class="action-icon">{{item.icon}}</view>
        <view class="action-info">
          <view class="action-title">{{item.title}}</view>
          <view class="action-subtitle">{{item.subtitle}}</view>
          <view class="action-meta">
            <view class="action-difficulty">
              <text wx:if="{{item.difficulty === 1}}">🌱 简单</text>
              <text wx:if="{{item.difficulty === 2}}">🌸 中等</text>
              <text wx:if="{{item.difficulty === 3}}">🌳 困难</text>
            </view>
            <view class="action-points">{{item.points}}积分</view>
          </view>
        </view>
        <view class="action-arrow">→</view>
      </view>
    </view>
    
    <view class="no-results" wx:else>
      <view class="no-results-icon">😔</view>
      <view class="no-results-text">没有找到相关的善意行为</view>
      <view class="no-results-tip">试试其他关键词吧</view>
    </view>
  </view>

  <!-- 推荐行为 -->
  <view class="recommended-section" wx:if="{{!searchMode}}">
    <view class="section-title">
      <view class="title-icon">💡</view>
      <view class="title-text">今日推荐</view>
    </view>
    
    <view class="recommended-actions">
      <view class="recommended-item" 
            wx:for="{{recommendedActions}}" 
            wx:key="id"
            bindtap="onSelectAction" 
            data-action="{{item.id}}">
        <view class="recommended-icon">{{item.icon}}</view>
        <view class="recommended-info">
          <view class="recommended-title">{{item.title}}</view>
          <view class="recommended-reason">{{item.recommendReason}}</view>
        </view>
        <view class="recommended-badge">推荐</view>
      </view>
    </view>
  </view>

  <!-- 我的进度 -->
  <view class="progress-section" wx:if="{{!searchMode}}">
    <view class="section-title">
      <view class="title-icon">📊</view>
      <view class="title-text">我的进度</view>
    </view>
    
    <view class="progress-stats">
      <view class="stat-item">
        <view class="stat-number">{{userProgress.completedActions}}</view>
        <view class="stat-label">已完成</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{userProgress.totalPoints}}</view>
        <view class="stat-label">总积分</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{userProgress.streak}}</view>
        <view class="stat-label">连续天数</view>
      </view>
    </view>
    
    <view class="progress-chart">
      <view class="chart-title">本周完成情况</view>
      <view class="chart-bars">
        <view class="chart-bar" 
              wx:for="{{weeklyProgress}}" 
              wx:key="day">
          <view class="bar-fill" style="height: {{item.percentage}}%;"></view>
          <view class="bar-label">{{item.day}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-spinner">
      <view class="spinner-icon">🌟</view>
      <view class="loading-text">加载中...</view>
    </view>
  </view>

</view>
