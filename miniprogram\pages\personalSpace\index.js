// 《能量星球》船长私人舱室 - 全息个人空间
const personalSystem = require('../../utils/personalSystem.js');
const wishSystem = require('../../utils/wishSystem.js');
const friendSystem = require('../../utils/friendSystem.js');

Page({
  data: {
    // 船长基础信息
    captainLevel: 5,
    captainTitle: '星际探索者',
    wisdomEnergy: 150,
    loveEnergy: 200,

    // 舰长信息HUD
    captainInfo: {
      rank: '中级舰长',
      title: '星际探索者'
    },
    
    // 愿望合成器数据
    wishCount: 3,
    wishEnergyCost: 50,
    
    // 成就展示数据
    achievementCount: 12,
    latestAchievement: '善意传播者',
    
    // 好友系统数据
    friendCount: 5,
    onlineFriends: [
      { id: 1, avatar: '👦', name: '小明' },
      { id: 2, avatar: '👧', name: '小红' },
      { id: 3, avatar: '🧒', name: '小李' }
    ],
    
    // 个人设置数据
    currentTheme: '深空紫',
    
    // 动画状态
    isActivated: false,
    animationPhase: 0
  },

  onLoad: function(options) {
    console.log('船长舱室页面加载');
    this.initPersonalSpace();
    this.startActivationSequence();
  },

  onShow: function() {
    console.log('船长舱室页面显示');
    this.refreshData();
  },

  // 初始化个人空间
  initPersonalSpace: function() {
    try {
      // 加载个人数据
      const personalData = personalSystem.getPersonalData();
      const energyData = this.getEnergyData();
      
      this.setData({
        captainLevel: personalData.level || 5,
        captainTitle: personalData.title || '星际探索者',
        wisdomEnergy: energyData.wisdom || 150,
        loveEnergy: energyData.love || 200
      });
      
      // 加载愿望数据
      this.loadWishData();
      
      // 加载成就数据
      this.loadAchievementData();
      
      // 加载好友数据
      this.loadFriendData();
      
    } catch (error) {
      console.error('初始化个人空间失败:', error);
    }
  },

  // 启动激活序列动画
  startActivationSequence: function() {
    console.log('启动全息舱室激活序列');
    
    // 阶段1：扫描线激活 (0.5s)
    setTimeout(() => {
      this.setData({ animationPhase: 1 });
    }, 100);
    
    // 阶段2：网格激活 (1.0s)
    setTimeout(() => {
      this.setData({ animationPhase: 2 });
    }, 600);
    
    // 阶段3：功能区激活 (1.5s)
    setTimeout(() => {
      this.setData({ animationPhase: 3 });
    }, 1100);
    
    // 阶段4：中央投影激活 (2.0s)
    setTimeout(() => {
      this.setData({ animationPhase: 4 });
    }, 1600);
    
    // 阶段5：能量流线连接 (2.5s)
    setTimeout(() => {
      this.setData({ 
        animationPhase: 5,
        isActivated: true 
      });
    }, 2100);
  },

  // 获取能量数据
  getEnergyData: function() {
    try {
      const app = getApp();
      return {
        wisdom: app.globalData.wisdomEnergy || 150,
        love: app.globalData.loveEnergy || 200
      };
    } catch (error) {
      console.error('获取能量数据失败:', error);
      return { wisdom: 150, love: 200 };
    }
  },

  // 加载愿望数据
  loadWishData: function() {
    try {
      const wishData = wishSystem.getWishSummary();
      this.setData({
        wishCount: wishData.activeCount || 3,
        wishEnergyCost: wishData.nextCost || 50
      });
    } catch (error) {
      console.error('加载愿望数据失败:', error);
    }
  },

  // 加载成就数据
  loadAchievementData: function() {
    try {
      const achievementData = personalSystem.getAchievementSummary();
      this.setData({
        achievementCount: achievementData.totalCount || 12,
        latestAchievement: achievementData.latest || '善意传播者'
      });
    } catch (error) {
      console.error('加载成就数据失败:', error);
    }
  },

  // 加载好友数据
  loadFriendData: function() {
    try {
      const friendData = friendSystem.getFriendSummary();
      this.setData({
        friendCount: friendData.totalCount || 5,
        onlineFriends: friendData.onlineList || []
      });
    } catch (error) {
      console.error('加载好友数据失败:', error);
    }
  },

  // 刷新数据
  refreshData: function() {
    this.loadWishData();
    this.loadAchievementData();
    this.loadFriendData();
    
    // 更新能量数据
    const energyData = this.getEnergyData();
    this.setData({
      wisdomEnergy: energyData.wisdom,
      loveEnergy: energyData.love
    });
  },

  // 编辑船长形象
  onEditCaptain: function() {
    console.log('编辑船长形象');
    wx.navigateTo({
      url: '/pages/personalSpace/profile/index'
    });
  },

  // 打开愿望合成器
  onOpenWishSynthesizer: function() {
    console.log('打开愿望合成器');
    wx.navigateTo({
      url: '/pages/personalSpace/wishSynthesizer/index'
    });
  },

  // 打开成就展示馆
  onOpenAchievementHall: function() {
    console.log('打开成就展示馆');
    wx.navigateTo({
      url: '/pages/personalSpace/achievementHall/index'
    });
  },

  // 打开好友星际站
  onOpenFriendStation: function() {
    console.log('打开好友星际站');
    wx.navigateTo({
      url: '/pages/personalSpace/friendStation/index'
    });
  },

  // 打开个人设置中心
  onOpenSettingsCenter: function() {
    console.log('打开个人设置中心');
    wx.navigateTo({
      url: '/pages/personalSpace/settings/index'
    });
  },

  // 页面卸载
  onUnload: function() {
    console.log('船长舱室页面卸载');
  }
});
