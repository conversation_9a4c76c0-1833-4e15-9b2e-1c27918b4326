// pages/exploration/cognitiveTraining/game/index.js
const app = getApp();

// 导入题目数据
const questionData = require('./questionData.js');

Page({
  // 计时器变量
  timer: null,

  /**
   * 页面的初始数据
   */
  data: {
    // 游戏基本信息
    trainingType: '',
    trainingName: '',
    
    // 题目相关
    questions: [],
    currentQuestionIndex: 0,
    currentQuestion: {},
    userAnswer: '',
    selectedOption: -1,
    
    // 游戏状态
    gameStarted: true,
    gameCompleted: false,
    score: 0,
    correctAnswers: 0,
    totalQuestions: 0,
    accuracyRate: 0,
    loveEnergyReward: 0,
    
    // 计时器
    timeLeft: 30,
    
    // 反馈状态
    showFeedback: false,
    feedbackType: '', // 'correct' | 'wrong'
    feedbackMessage: '',
    
    // 动画
    questionAnimation: {},
    
    // 训练类型配置
    trainingConfig: {
      language: { color: '#FF9800', icon: '📚', timeLimit: 30 },
      attention: { color: '#2196F3', icon: '🎯', timeLimit: 20 },
      visual: { color: '#4CAF50', icon: '👁️', timeLimit: 25 },
      auditory: { color: '#9C27B0', icon: '🎵', timeLimit: 35 }
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { type, name } = options;
    console.log('加载认知训练游戏:', type, name);
    
    this.setData({
      trainingType: type,
      trainingName: name || '认知训练'
    });
    
    this.initializeGame();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    this.clearTimer();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    this.clearTimer();
  },

  /**
   * 初始化游戏
   */
  initializeGame() {
    const { trainingType } = this.data;
    
    // 获取对应类型的题目
    const questions = questionData.getQuestions(trainingType);
    
    if (!questions || questions.length === 0) {
      wx.showModal({
        title: '提示',
        content: '暂无可用题目',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }
    
    // 随机打乱题目顺序
    const shuffledQuestions = this.shuffleArray([...questions]);
    
    this.setData({
      questions: shuffledQuestions,
      totalQuestions: shuffledQuestions.length,
      currentQuestion: shuffledQuestions[0] || {}
    });

    // 自动开始游戏
    this.startGameDirectly();
  },

  /**
   * 开始游戏
   */
  startGame() {
    this.setData({
      gameStarted: true,
      currentQuestionIndex: 0,
      score: 0,
      correctAnswers: 0
    });

    this.loadQuestion();
    this.startTimer();
  },

  /**
   * 直接开始游戏（无需用户点击）
   */
  startGameDirectly() {
    this.setData({
      currentQuestionIndex: 0,
      score: 0,
      correctAnswers: 0
    });

    this.loadQuestion();
    this.startTimer();
  },

  /**
   * 加载当前题目
   */
  loadQuestion() {
    const { questions, currentQuestionIndex } = this.data;
    
    if (currentQuestionIndex >= questions.length) {
      this.completeGame();
      return;
    }
    
    const currentQuestion = questions[currentQuestionIndex];
    
    // 重置状态
    this.setData({
      currentQuestion,
      userAnswer: '',
      selectedOption: -1,
      showFeedback: false
    });
    
    // 重置计时器
    this.resetTimer();
    
    // 题目切换动画
    this.animateQuestionTransition();
  },

  /**
   * 题目切换动画
   */
  animateQuestionTransition() {
    const animation = wx.createAnimation({
      duration: 300,
      timingFunction: 'ease-out'
    });
    
    animation.translateX(-50).opacity(0).step();
    animation.translateX(0).opacity(1).step();
    
    this.setData({
      questionAnimation: animation.export()
    });
  },

  /**
   * 开始计时器
   */
  startTimer() {
    // 先清除可能存在的计时器
    this.clearTimer();

    const { trainingType, trainingConfig } = this.data;
    const timeLimit = trainingConfig[trainingType]?.timeLimit || 30;

    this.setData({ timeLeft: timeLimit });

    this.timer = setInterval(() => {
      const timeLeft = this.data.timeLeft - 1;
      this.setData({ timeLeft });

      if (timeLeft <= 0) {
        this.handleTimeout();
      }
    }, 1000);
  },

  /**
   * 重置计时器
   */
  resetTimer() {
    this.clearTimer();
    this.startTimer();
  },

  /**
   * 清除计时器
   */
  clearTimer() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },

  /**
   * 处理超时
   */
  handleTimeout() {
    this.clearTimer();
    this.showFeedback('wrong', '时间到了！');
    
    setTimeout(() => {
      this.nextQuestion();
    }, 2000);
  },

  /**
   * 选择选项
   */
  selectOption(e) {
    const optionIndex = e.currentTarget.dataset.index;
    this.setData({ selectedOption: optionIndex });
  },

  /**
   * 输入答案
   */
  onAnswerInput(e) {
    this.setData({ userAnswer: e.detail.value });
  },

  /**
   * 提交答案
   */
  submitAnswer() {
    const { currentQuestion, selectedOption, userAnswer } = this.data;
    
    if (!currentQuestion) return;
    
    let isCorrect = false;
    let userResponse = '';
    
    // 根据题目类型判断答案
    if (currentQuestion.type === 'multiple_choice') {
      userResponse = selectedOption;
      isCorrect = selectedOption === currentQuestion.correctAnswer;
    } else if (currentQuestion.type === 'text_input') {
      userResponse = userAnswer.trim();
      isCorrect = this.checkTextAnswer(userResponse, currentQuestion.correctAnswer);
    }
    
    // 更新分数
    if (isCorrect) {
      const newScore = this.data.score + 10;
      const newCorrectAnswers = this.data.correctAnswers + 1;
      this.setData({
        score: newScore,
        correctAnswers: newCorrectAnswers
      });
    }
    
    // 显示反馈
    const feedbackMessage = isCorrect ? 
      currentQuestion.feedback?.correct || '回答正确！' :
      currentQuestion.feedback?.wrong || '回答错误，再想想看！';
    
    this.showFeedback(isCorrect ? 'correct' : 'wrong', feedbackMessage);
    
    // 清除计时器
    this.clearTimer();
    
    // 延迟进入下一题
    setTimeout(() => {
      this.nextQuestion();
    }, 2000);
  },

  /**
   * 检查文本答案
   */
  checkTextAnswer(userAnswer, correctAnswer) {
    if (Array.isArray(correctAnswer)) {
      return correctAnswer.some(answer => 
        userAnswer.toLowerCase().includes(answer.toLowerCase())
      );
    }
    return userAnswer.toLowerCase().includes(correctAnswer.toLowerCase());
  },

  /**
   * 显示反馈
   */
  showFeedback(type, message) {
    this.setData({
      showFeedback: true,
      feedbackType: type,
      feedbackMessage: message
    });
  },

  /**
   * 下一题
   */
  nextQuestion() {
    const nextIndex = this.data.currentQuestionIndex + 1;
    this.setData({
      currentQuestionIndex: nextIndex,
      showFeedback: false
    });
    
    this.loadQuestion();
  },

  /**
   * 完成游戏
   */
  completeGame() {
    this.clearTimer();

    // 计算正确率和奖励
    const { correctAnswers, totalQuestions } = this.data;
    const accuracyRate = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;
    const loveEnergyReward = Math.floor(correctAnswers / 2);

    this.setData({
      gameCompleted: true,
      accuracyRate: accuracyRate,
      loveEnergyReward: loveEnergyReward
    });

    // 保存进度
    this.saveProgress();

    // 奖励能量
    this.rewardEnergy();
  },

  /**
   * 保存进度
   */
  saveProgress() {
    try {
      const { trainingType, correctAnswers, totalQuestions } = this.data;
      const progressData = wx.getStorageSync('cognitiveTrainingProgress') || {};
      
      if (!progressData[trainingType]) {
        progressData[trainingType] = { completed: 0, unlocked: true };
      }
      
      progressData[trainingType].completed = Math.max(
        progressData[trainingType].completed,
        correctAnswers
      );
      
      wx.setStorageSync('cognitiveTrainingProgress', progressData);
    } catch (error) {
      console.error('保存进度失败:', error);
    }
  },

  /**
   * 奖励能量
   */
  rewardEnergy() {
    const { correctAnswers } = this.data;
    const wisdomEnergy = correctAnswers * 2;
    const loveEnergy = Math.floor(correctAnswers / 2);
    
    // 这里应该调用全局的能量系统
    console.log('奖励能量:', { wisdomEnergy, loveEnergy });
  },

  /**
   * 重新开始
   */
  restartGame() {
    // 先清除计时器
    this.clearTimer();

    this.setData({
      gameStarted: true,
      gameCompleted: false,
      currentQuestionIndex: 0,
      score: 0,
      correctAnswers: 0,
      showFeedback: false
    });

    this.initializeGame();
  },

  /**
   * 返回训练中心
   */
  backToTrainingCenter() {
    wx.navigateBack();
  },

  /**
   * 工具函数：打乱数组
   */
  shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
  }
});
