<!--《能量星球》船长私人舱室 - 全息个人空间-->
<view class="page holographic-quarters">
  <!-- 全息扫描激活层 -->
  <view class="hologram-activation">
    <view class="scan-line"></view>
    <view class="activation-grid">
      <view class="grid-line horizontal h1"></view>
      <view class="grid-line horizontal h2"></view>
      <view class="grid-line horizontal h3"></view>
      <view class="grid-line vertical v1"></view>
      <view class="grid-line vertical v2"></view>
      <view class="grid-line vertical v3"></view>
    </view>
  </view>

  <!-- 深空背景层 -->
  <view class="deep-space-background">
    <!-- 星云层 -->
    <view class="nebula-layer">
      <view class="nebula nebula-1"></view>
      <view class="nebula nebula-2"></view>
      <view class="nebula nebula-3"></view>
    </view>
    
    <!-- 星空层 -->
    <view class="starfield-layer">
      <view class="star star-1"></view>
      <view class="star star-2"></view>
      <view class="star star-3"></view>
      <view class="star star-4"></view>
      <view class="star star-5"></view>
      <view class="star star-6"></view>
      <view class="star star-7"></view>
      <view class="star star-8"></view>
    </view>
  </view>

  <!-- 舰长信息HUD -->
  <view class="captain-hud">
    <view class="hud-left">
      <view class="captain-rank">
        <text class="rank-icon">⭐</text>
        <text class="rank-text">{{captainInfo.rank}}</text>
      </view>
      <view class="captain-title">{{captainInfo.title}}</view>
    </view>
    <view class="hud-center">
      <view class="ship-status">
        <text class="status-text">舰船状态</text>
        <view class="status-indicator active"></view>
      </view>
    </view>
    <view class="hud-right">
      <view class="energy-status">
        <view class="energy-item">
          <text class="energy-icon">🔷</text>
          <text class="energy-value">{{wisdomEnergy}}</text>
        </view>
        <view class="energy-item">
          <text class="energy-icon">❤️</text>
          <text class="energy-value">{{loveEnergy}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 装饰性全息元素 -->
  <view class="hologram-decorations">
    <!-- 全息几何体 -->
    <view class="holo-geometry holo-1">
      <view class="geo-core"></view>
      <view class="geo-ring ring-1"></view>
      <view class="geo-ring ring-2"></view>
      <view class="geo-particle particle-1"></view>
      <view class="geo-particle particle-2"></view>
      <view class="geo-particle particle-3"></view>
    </view>

    <view class="holo-geometry holo-2">
      <view class="geo-core"></view>
      <view class="geo-ring ring-1"></view>
      <view class="geo-ring ring-2"></view>
      <view class="geo-particle particle-1"></view>
      <view class="geo-particle particle-2"></view>
      <view class="geo-particle particle-3"></view>
    </view>

    <view class="holo-geometry holo-3">
      <view class="geo-core"></view>
      <view class="geo-ring ring-1"></view>
      <view class="geo-ring ring-2"></view>
      <view class="geo-particle particle-1"></view>
      <view class="geo-particle particle-2"></view>
      <view class="geo-particle particle-3"></view>
    </view>

    <view class="holo-geometry holo-4">
      <view class="geo-core"></view>
      <view class="geo-ring ring-1"></view>
      <view class="geo-ring ring-2"></view>
      <view class="geo-particle particle-1"></view>
      <view class="geo-particle particle-2"></view>
      <view class="geo-particle particle-3"></view>
    </view>

    <!-- 数据流线 -->
    <view class="data-streams">
      <view class="stream stream-1"></view>
      <view class="stream stream-2"></view>
      <view class="stream stream-3"></view>
    </view>
  </view>

  <!-- 中央全息投影台 -->
  <view class="hologram-center">
    <view class="projection-platform">
      <!-- 投影台基座 -->
      <view class="platform-base">
        <view class="base-ring ring-1"></view>
        <view class="base-ring ring-2"></view>
        <view class="base-ring ring-3"></view>
      </view>
      
      <!-- 船长全息投影 -->
      <view class="captain-hologram" bindtap="onEditCaptain">
        <view class="hologram-avatar">
          <text class="avatar-icon">🧑‍🚀</text>
          <view class="hologram-glitch"></view>
        </view>
        
        <!-- 双能量光环 -->
        <view class="energy-rings">
          <view class="energy-ring wisdom">
            <view class="ring-glow"></view>
          </view>
          <view class="energy-ring love">
            <view class="ring-glow"></view>
          </view>
        </view>

        <!-- 等级徽章 -->
        <view class="level-badge">
          <view class="badge-glow"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 四象限功能区域 -->
  <view class="function-quadrants">
    <!-- 左上：愿望合成器 -->
    <view class="quadrant top-left wish-synthesizer" bindtap="onOpenWishSynthesizer">
      <view class="quadrant-background">
        <view class="nebula-vortex">
          <view class="vortex-ring ring-1"></view>
          <view class="vortex-ring ring-2"></view>
          <view class="vortex-ring ring-3"></view>
        </view>
      </view>
      <view class="quadrant-content">
        <view class="function-icon">✨</view>
        <text class="function-title">愿望合成器</text>
        <text class="function-subtitle">{{wishCount}}个愿望进行中</text>
        <view class="energy-cost">
          <text class="cost-value">{{wishEnergyCost}}</text>
          <text class="cost-unit">智慧能量</text>
        </view>
      </view>
      <view class="quadrant-glow"></view>
    </view>

    <!-- 右上：成就展示馆 -->
    <view class="quadrant top-right achievement-hall" bindtap="onOpenAchievementHall">
      <view class="quadrant-background">
        <view class="honor-constellation">
          <view class="constellation-star star-1"></view>
          <view class="constellation-star star-2"></view>
          <view class="constellation-star star-3"></view>
          <view class="constellation-star star-4"></view>
          <view class="constellation-star star-5"></view>
          <view class="constellation-line line-1"></view>
          <view class="constellation-line line-2"></view>
          <view class="constellation-line line-3"></view>
        </view>
      </view>
      <view class="quadrant-content">
        <view class="function-icon">🏆</view>
        <text class="function-title">成就展示馆</text>
        <text class="function-subtitle">{{achievementCount}}个成就已获得</text>
        <view class="achievement-preview">
          <view class="recent-badge"></view>
          <text class="recent-text">最新：{{latestAchievement}}</text>
        </view>
      </view>
      <view class="quadrant-glow"></view>
    </view>

    <!-- 左下：好友星际站 -->
    <view class="quadrant bottom-left friend-station" bindtap="onOpenFriendStation">
      <view class="quadrant-background">
        <view class="communication-array">
          <view class="comm-dish dish-1"></view>
          <view class="comm-dish dish-2"></view>
          <view class="comm-dish dish-3"></view>
          <view class="signal-wave wave-1"></view>
          <view class="signal-wave wave-2"></view>
          <view class="signal-wave wave-3"></view>
        </view>
      </view>
      <view class="quadrant-content">
        <view class="function-icon">🤝</view>
        <text class="function-title">好友星际站</text>
        <text class="function-subtitle">{{friendCount}}位好友在线</text>
        <view class="friend-avatars">
          <view class="friend-avatar" wx:for="{{onlineFriends}}" wx:key="id">
            <text class="friend-icon">{{item.avatar}}</text>
            <view class="online-indicator"></view>
          </view>
        </view>
      </view>
      <view class="quadrant-glow"></view>
    </view>

    <!-- 右下：个人设置中心 -->
    <view class="quadrant bottom-right settings-center" bindtap="onOpenSettingsCenter">
      <view class="quadrant-background">
        <view class="control-matrix">
          <view class="matrix-node node-1"></view>
          <view class="matrix-node node-2"></view>
          <view class="matrix-node node-3"></view>
          <view class="matrix-node node-4"></view>
          <view class="matrix-connection conn-1"></view>
          <view class="matrix-connection conn-2"></view>
          <view class="matrix-connection conn-3"></view>
        </view>
      </view>
      <view class="quadrant-content">
        <view class="function-icon">⚙️</view>
        <text class="function-title">个人设置</text>
        <text class="function-subtitle">定制专属体验</text>
        <view class="settings-preview">
          <view class="setting-item">
            <text class="setting-label">主题</text>
            <text class="setting-value">{{currentTheme}}</text>
          </view>
        </view>
      </view>
      <view class="quadrant-glow"></view>
    </view>
  </view>

  <!-- 能量流线连接系统 -->
  <view class="energy-flow-system">
    <view class="flow-line line-1">
      <view class="flow-particle particle-1"></view>
      <view class="flow-particle particle-2"></view>
    </view>
    <view class="flow-line line-2">
      <view class="flow-particle particle-3"></view>
      <view class="flow-particle particle-4"></view>
    </view>
    <view class="flow-line line-3">
      <view class="flow-particle particle-5"></view>
      <view class="flow-particle particle-6"></view>
    </view>
    <view class="flow-line line-4">
      <view class="flow-particle particle-7"></view>
      <view class="flow-particle particle-8"></view>
    </view>
  </view>

  <!-- 全息特效层 -->
  <view class="hologram-effects">
    <view class="hologram-particle hp-1"></view>
    <view class="hologram-particle hp-2"></view>
    <view class="hologram-particle hp-3"></view>
    <view class="hologram-particle hp-4"></view>
    <view class="hologram-particle hp-5"></view>
    <view class="hologram-particle hp-6"></view>
  </view>
</view>
