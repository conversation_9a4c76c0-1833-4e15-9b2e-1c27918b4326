<!--pages/exploration/cognitiveTraining/index.wxml-->
<view class="container">


  <!-- 总体进度展示 -->
  <view class="progress-overview">
    <view class="overview-item">
      <view class="overview-number">{{totalCompletedQuestions}}</view>
      <view class="overview-label">已完成题目</view>
    </view>
    <view class="overview-divider"></view>
    <view class="overview-item">
      <view class="overview-number">{{unlockedTrainingCount}}</view>
      <view class="overview-label">已解锁训练</view>
    </view>
    <view class="overview-divider"></view>
    <view class="overview-item">
      <view class="overview-number">{{overallProgress}}%</view>
      <view class="overview-label">总体进度</view>
    </view>
  </view>

  <!-- 训练类型列表 -->
  <view class="training-list">
    <view
      class="training-item {{item.unlocked ? 'unlocked' : 'locked'}}"
      wx:for="{{trainingTypes}}"
      wx:key="id"
      animation="{{cardAnimations[index]}}"
      data-training="{{item.id}}"
      bindtap="onTrainingTap"
    >
      <!-- 左侧图标区域 -->
      <view class="item-left">
        <view class="training-icon-wrapper" style="background: linear-gradient(135deg, {{item.color}}, {{item.color}}80);">
          <text class="training-icon">{{item.icon}}</text>
          <view class="icon-decoration" style="border-color: {{item.color}};"></view>
        </view>
      </view>

      <!-- 中间信息区域 -->
      <view class="item-center">
        <view class="training-header">
          <view class="training-name">{{item.name}}</view>
          <view class="training-status {{item.unlocked ? 'unlocked' : 'locked'}}">
            {{item.unlocked ? '已解锁' : '未解锁'}}
          </view>
        </view>
        <view class="training-desc">{{item.description}}</view>
        <view class="training-details">
          <view class="detail-tag">{{item.difficulty}}</view>
          <view class="detail-tag">{{item.totalQuestions}}道题目</view>
          <view class="detail-tag">适合6-12岁</view>
        </view>

        <!-- 进度条 -->
        <view class="progress-container">
          <view class="progress-bar">
            <view
              class="progress-fill"
              style="width: {{(item.progress / item.totalQuestions) * 100}}%; background: {{item.color}};"
            ></view>
          </view>
          <view class="progress-text">{{item.progress}}/{{item.totalQuestions}} 完成</view>
        </view>
      </view>

      <!-- 右侧操作区域 -->
      <view class="item-right">
        <view class="action-btn {{item.unlocked ? 'enabled' : 'disabled'}}" style="background: {{item.unlocked ? item.color : '#cccccc'}};">
          <text class="btn-text">{{item.unlocked ? '开始训练' : '🔒'}}</text>
        </view>
        <view class="score-display" wx:if="{{item.progress > 0}}">
          <text class="score-text">最高分: {{item.bestScore || 0}}</text>
        </view>
      </view>

      <!-- 背景装饰 -->
      <view class="item-bg" style="background: linear-gradient(90deg, {{item.color}}08, transparent);"></view>
    </view>
  </view>


</view>


