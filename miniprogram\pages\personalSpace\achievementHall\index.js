// 《能量星球》成就展示馆 - 荣耀星座主题
const personalSystem = require('../../../utils/personalSystem.js');

Page({
  data: {
    // 统计数据
    totalAchievements: 0,
    completionRate: 0,
    recentCount: 0,
    
    // 最新成就
    latestAchievement: null,
    
    // 分类选择
    selectedCategory: 'all',
    
    // 成就数据
    allAchievements: [],
    filteredAchievements: [],
    
    // 时间线数据
    timelineEvents: [],
    
    // 分享数据
    topAchievements: []
  },

  onLoad: function(options) {
    console.log('成就展示馆页面加载');
    this.initAchievementHall();
  },

  onShow: function() {
    console.log('成就展示馆页面显示');
    this.refreshData();
  },

  onPullDownRefresh: function() {
    console.log('下拉刷新成就数据');
    this.refreshData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 初始化成就展示馆
  initAchievementHall: function() {
    this.loadAchievementData();
    this.loadTimelineData();
    this.calculateStats();
  },

  // 加载成就数据
  loadAchievementData: function() {
    try {
      // 获取所有成就数据
      const achievements = this.getAllAchievements();
      
      this.setData({
        allAchievements: achievements,
        filteredAchievements: achievements
      });
      
      // 获取最新成就
      const latestAchievement = this.getLatestAchievement(achievements);
      this.setData({
        latestAchievement: latestAchievement
      });
      
      // 获取顶级成就用于分享
      const topAchievements = achievements
        .filter(a => a.unlocked && a.rarity === 'legendary')
        .slice(0, 3);
      this.setData({
        topAchievements: topAchievements
      });
      
    } catch (error) {
      console.error('加载成就数据失败:', error);
    }
  },

  // 获取所有成就（模拟数据）
  getAllAchievements: function() {
    return [
      // 学习成就
      {
        id: 'learn_001',
        name: '初出茅庐',
        description: '完成第一个学习任务',
        icon: '📚',
        category: 'learning',
        rarity: 'common',
        unlocked: true,
        unlockDate: '2025-01-20',
        progress: null,
        target: null
      },
      {
        id: 'learn_002',
        name: '学习达人',
        description: '连续学习7天',
        icon: '🎓',
        category: 'learning',
        rarity: 'rare',
        unlocked: true,
        unlockDate: '2025-01-25',
        progress: null,
        target: null
      },
      {
        id: 'learn_003',
        name: '智慧之星',
        description: '累计获得1000智慧能量',
        icon: '🌟',
        category: 'learning',
        rarity: 'epic',
        unlocked: false,
        unlockDate: null,
        progress: 750,
        target: 1000
      },
      
      // 善意成就
      {
        id: 'kind_001',
        name: '善意萌芽',
        description: '完成第一个善意行为',
        icon: '🌱',
        category: 'kindness',
        rarity: 'common',
        unlocked: true,
        unlockDate: '2025-01-18',
        progress: null,
        target: null
      },
      {
        id: 'kind_002',
        name: '爱心天使',
        description: '累计完成50个善意行为',
        icon: '😇',
        category: 'kindness',
        rarity: 'rare',
        unlocked: true,
        unlockDate: '2025-01-26',
        progress: null,
        target: null
      },
      {
        id: 'kind_003',
        name: '善意传播者',
        description: '影响10位好友参与善意行为',
        icon: '🌈',
        category: 'kindness',
        rarity: 'legendary',
        unlocked: false,
        unlockDate: null,
        progress: 3,
        target: 10
      },
      
      // 社交成就
      {
        id: 'social_001',
        name: '友谊起航',
        description: '添加第一位好友',
        icon: '🤝',
        category: 'social',
        rarity: 'common',
        unlocked: true,
        unlockDate: '2025-01-15',
        progress: null,
        target: null
      },
      {
        id: 'social_002',
        name: '人气之星',
        description: '拥有10位好友',
        icon: '⭐',
        category: 'social',
        rarity: 'rare',
        unlocked: false,
        unlockDate: null,
        progress: 5,
        target: 10
      },
      
      // 个人成就
      {
        id: 'personal_001',
        name: '宇宙探索者',
        description: '加入能量星球',
        icon: '🚀',
        category: 'personal',
        rarity: 'common',
        unlocked: true,
        unlockDate: '2025-01-10',
        progress: null,
        target: null
      },
      {
        id: 'personal_002',
        name: '坚持不懈',
        description: '连续登录30天',
        icon: '💪',
        category: 'personal',
        rarity: 'epic',
        unlocked: false,
        unlockDate: null,
        progress: 18,
        target: 30
      }
    ];
  },

  // 获取最新成就
  getLatestAchievement: function(achievements) {
    const unlockedAchievements = achievements.filter(a => a.unlocked);
    if (unlockedAchievements.length === 0) return null;
    
    // 按解锁日期排序，获取最新的
    unlockedAchievements.sort((a, b) => new Date(b.unlockDate) - new Date(a.unlockDate));
    return unlockedAchievements[0];
  },

  // 加载时间线数据
  loadTimelineData: function() {
    const timelineEvents = [
      {
        id: 'timeline_001',
        month: '01月',
        day: '26',
        type: 'kindness',
        icon: '😇',
        title: '获得爱心天使成就',
        description: '完成了50个善意行为，获得稀有成就徽章',
        achievements: [
          { id: 'kind_002', icon: '😇' }
        ]
      },
      {
        id: 'timeline_002',
        month: '01月',
        day: '25',
        type: 'learning',
        icon: '🎓',
        title: '获得学习达人成就',
        description: '连续学习7天，养成了良好的学习习惯',
        achievements: [
          { id: 'learn_002', icon: '🎓' }
        ]
      },
      {
        id: 'timeline_003',
        month: '01月',
        day: '20',
        type: 'learning',
        icon: '📚',
        title: '学习之旅开始',
        description: '完成了第一个学习任务，踏上了知识探索之路',
        achievements: [
          { id: 'learn_001', icon: '📚' }
        ]
      },
      {
        id: 'timeline_004',
        month: '01月',
        day: '18',
        type: 'kindness',
        icon: '🌱',
        title: '善意萌芽',
        description: '完成了第一个善意行为，开始传播爱心',
        achievements: [
          { id: 'kind_001', icon: '🌱' }
        ]
      },
      {
        id: 'timeline_005',
        month: '01月',
        day: '15',
        type: 'social',
        icon: '🤝',
        title: '友谊起航',
        description: '添加了第一位好友，开始了社交之旅',
        achievements: [
          { id: 'social_001', icon: '🤝' }
        ]
      },
      {
        id: 'timeline_006',
        month: '01月',
        day: '10',
        type: 'personal',
        icon: '🚀',
        title: '加入能量星球',
        description: '开始了精彩的宇宙探索之旅',
        achievements: [
          { id: 'personal_001', icon: '🚀' }
        ]
      }
    ];
    
    this.setData({
      timelineEvents: timelineEvents
    });
  },

  // 计算统计数据
  calculateStats: function() {
    const achievements = this.data.allAchievements;
    const unlockedCount = achievements.filter(a => a.unlocked).length;
    const totalCount = achievements.length;
    const completionRate = Math.round((unlockedCount / totalCount) * 100);
    
    // 计算本周新增（模拟数据）
    const recentCount = 2;
    
    this.setData({
      totalAchievements: unlockedCount,
      completionRate: completionRate,
      recentCount: recentCount
    });
  },

  // 刷新数据
  refreshData: function() {
    this.loadAchievementData();
    this.calculateStats();
  },

  // 选择分类
  onSelectCategory: function(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      selectedCategory: category
    });
    
    this.filterAchievements(category);
  },

  // 过滤成就
  filterAchievements: function(category) {
    let filtered = this.data.allAchievements;
    
    if (category !== 'all') {
      filtered = this.data.allAchievements.filter(a => a.category === category);
    }
    
    this.setData({
      filteredAchievements: filtered
    });
  },

  // 查看成就详情
  onViewAchievement: function(e) {
    const achievement = e.currentTarget.dataset.achievement;
    console.log('查看成就详情:', achievement);
    
    if (achievement.unlocked) {
      wx.showModal({
        title: achievement.name,
        content: `${achievement.description}\n\n解锁时间: ${achievement.unlockDate}`,
        showCancel: false,
        confirmText: '确定'
      });
    } else {
      const progressText = achievement.progress ? 
        `\n\n当前进度: ${achievement.progress}/${achievement.target}` : '';
      wx.showModal({
        title: achievement.name,
        content: `${achievement.description}${progressText}`,
        showCancel: false,
        confirmText: '继续努力'
      });
    }
  },

  // 获取分类名称
  getCategoryName: function(category) {
    const categoryNames = {
      learning: '学习',
      kindness: '善意',
      social: '社交',
      personal: '个人'
    };
    return categoryNames[category] || '未知';
  },

  // 分享给好友
  onShareToFriends: function() {
    console.log('分享成就给好友');
    wx.showToast({
      title: '分享功能开发中',
      icon: 'none'
    });
  },

  // 分享给家长
  onShareToParents: function() {
    console.log('分享成就给家长');
    wx.showToast({
      title: '分享功能开发中',
      icon: 'none'
    });
  },

  // 页面卸载
  onUnload: function() {
    console.log('成就展示馆页面卸载');
  }
});
