# 故事理解游戏测试指南

## 📋 功能概述

**游戏名称**: 孩子更专注 - 故事理解游戏  
**游戏类型**: 听觉认知训练（音律星球系列）  
**目标用户**: 3-6岁儿童  
**题目数量**: 20道分层题目  

## 🎯 测试目标

验证故事理解游戏的完整功能，包括：
1. 题目数据加载和显示
2. 用户交互和答案选择
3. 计分和反馈系统
4. 年龄适配和难度调整
5. 视觉效果和用户体验

## 🔧 测试步骤

### 1. 进入游戏
1. 打开《能量星球》小程序
2. 点击主界面的"思维工坊"按钮
3. 进入探索星球页面
4. 点击"音律星球"🎵
5. 选择"故事理解"游戏
6. 点击"开始游戏"

### 2. 游戏界面测试
**预期效果**:
- ✅ 显示故事文本卡片（紫色主题）
- ✅ 显示问题和进度指示器
- ✅ 显示4个选项按钮（A、B、C、D）
- ✅ 确认按钮初始为禁用状态

### 3. 交互功能测试
**测试操作**:
1. 点击任意选项按钮
2. 观察选项高亮效果
3. 点击"确认答案"按钮
4. 观察反馈提示
5. 等待自动进入下一题

**预期效果**:
- ✅ 选项按钮有选中状态变化
- ✅ 确认按钮变为可用状态
- ✅ 正确答案显示"回答正确！+25分"
- ✅ 错误答案显示正确答案提示
- ✅ 自动切换到下一题

### 4. 计分系统测试
**测试数据**:
- 每题正确答案：+25分
- 总分计算：正确题数 × 25
- 智慧能量奖励：根据表现计算

**预期效果**:
- ✅ HUD显示实时得分更新
- ✅ 游戏完成后显示最终得分
- ✅ 根据得分给予智慧能量奖励

### 5. 年龄适配测试
**测试方法**:
1. 修改年龄组设置（3-4、4-5、5-6）
2. 重新开始游戏
3. 观察题目难度变化

**预期效果**:
- ✅ 3-4岁：简单场景，基础词汇
- ✅ 4-5岁：中等复杂度，常见场景
- ✅ 5-6岁：复杂场景，高级词汇

## 📊 测试用例

### 用例1：家庭场景题目
**故事**: "周末，妈妈在厨房做饭，爸爸在客厅看电视，哥哥在书房看书，妹妹在客房练琴。"
**问题**: "妈妈在做什么？"
**选项**: A.做饭 B.看电视 C.看书 D.练琴
**正确答案**: A

### 用例2：学校场景题目
**故事**: "上课时间，老师在讲台上讲课，小明在座位上听课，小红在黑板前回答问题，小华在后面整理书包。"
**问题**: "小红在哪里？"
**选项**: A.讲台上 B.座位上 C.黑板前 D.后面
**正确答案**: C

### 用例3：户外场景题目
**故事**: "在公园里，妈妈在长椅上看书，爸爸在草地上放风筝，小明在湖边喂鱼，小红在花园里拍照。"
**问题**: "小明在做什么？"
**选项**: A.看书 B.放风筝 C.喂鱼 D.拍照
**正确答案**: C

## 🎨 视觉效果验证

### 设计标准
- ✅ 紫色音律星球主题色彩（#9C27B0）
- ✅ Soft-UI/Neumorphism设计风格
- ✅ 3D卡片效果和动画
- ✅ 儿童友好的大按钮设计
- ✅ 清晰的字体和对比度

### 动画效果
- ✅ 卡片入场动画
- ✅ 选项按钮交互反馈
- ✅ 确认按钮扫光效果
- ✅ 图标浮动动画

## 🐛 常见问题排查

### 问题1：游戏无法启动
**可能原因**: gameId参数错误
**解决方案**: 检查explorationSystem.js中的游戏配置

### 问题2：题目不显示
**可能原因**: 数据加载失败
**解决方案**: 检查storyComprehensionData.js文件路径

### 问题3：选项无法选择
**可能原因**: 事件绑定问题
**解决方案**: 检查WXML中的bindtap事件

### 问题4：样式显示异常
**可能原因**: CSS类名冲突
**解决方案**: 检查WXSS文件中的样式定义

## 📈 性能指标

### 加载性能
- 游戏启动时间：< 500ms
- 题目切换时间：< 200ms
- 动画流畅度：60fps

### 用户体验
- 按钮响应时间：< 100ms
- 反馈显示时间：1.5s
- 自动切换延迟：1.5s

## ✅ 验收标准

### 功能完整性
- [ ] 20道题目全部可正常显示
- [ ] 所有交互功能正常工作
- [ ] 计分系统准确无误
- [ ] 年龄适配正确生效

### 用户体验
- [ ] 界面美观，符合设计标准
- [ ] 操作流畅，无卡顿现象
- [ ] 反馈及时，信息清晰
- [ ] 适合儿童操作习惯

### 技术质量
- [ ] 代码结构清晰，无语法错误
- [ ] 数据管理规范，易于维护
- [ ] 性能表现良好，资源占用合理
- [ ] 兼容性良好，支持主流设备

---

**测试完成标志**: 所有验收标准通过，游戏可正常运行并提供良好的用户体验。

**下一步计划**: 根据测试结果优化游戏体验，准备开发更多认知训练游戏。
