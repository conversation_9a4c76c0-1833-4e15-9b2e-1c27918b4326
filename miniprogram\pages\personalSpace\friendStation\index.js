// 《能量星球》好友星际站 - 通讯阵列主题
const friendSystem = require('../../../utils/friendSystem.js');

Page({
  data: {
    // 好友分类
    selectedCategory: 'all',
    
    // 好友数据
    allFriends: [],
    filteredFriends: [],
    recentFriends: [],
    pendingFriends: [],
    
    // 统计数据
    onlineFriends: 0,
    
    // 界面状态
    isLoading: false
  },

  onLoad: function(options) {
    console.log('好友星际站页面加载');
    this.initFriendStation();
  },

  onShow: function() {
    console.log('好友星际站页面显示');
    this.refreshFriendData();
  },

  onPullDownRefresh: function() {
    console.log('下拉刷新好友数据');
    this.refreshFriendData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 初始化好友星际站
  initFriendStation: function() {
    this.loadFriendData();
    this.startOnlineStatusUpdate();
  },

  // 加载好友数据
  loadFriendData: function() {
    try {
      // 获取所有好友数据（模拟数据）
      const friends = this.getMockFriendData();
      
      // 分类处理
      const recentFriends = friends.filter(f => f.isRecent);
      const pendingFriends = friends.filter(f => f.status === 'pending');
      const onlineCount = friends.filter(f => f.isOnline && f.status === 'approved').length;
      
      this.setData({
        allFriends: friends,
        filteredFriends: friends,
        recentFriends: recentFriends,
        pendingFriends: pendingFriends,
        onlineFriends: onlineCount
      });
      
    } catch (error) {
      console.error('加载好友数据失败:', error);
    }
  },

  // 获取模拟好友数据
  getMockFriendData: function() {
    return [
      {
        id: 'friend_001',
        name: '小星探索者',
        avatar: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzRGNDZFNSIvPjx0ZXh0IHg9IjUwIiB5PSI2MCIgZm9udC1zaXplPSI0MCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPvCfmoA8L3RleHQ+PC9zdmc+',
        level: 8,
        isOnline: true,
        status: 'approved',
        isRecent: true,
        lastSeen: '',
        achievements: 15,
        wisdomEnergy: 850,
        loveEnergy: 620,
        currentActivity: '正在思维工坊学习',
        friendshipLevel: 3
      },
      {
        id: 'friend_002',
        name: '宇宙小勇士',
        avatar: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzEwQjk4MSIvPjx0ZXh0IHg9IjUwIiB5PSI2MCIgZm9udC1zaXplPSI0MCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPuKalO+4jzwvdGV4dD48L3N2Zz4=',
        level: 6,
        isOnline: true,
        status: 'approved',
        isRecent: false,
        lastSeen: '',
        achievements: 12,
        wisdomEnergy: 640,
        loveEnergy: 780,
        currentActivity: '正在宇宙灯塔做善事',
        friendshipLevel: 2
      },
      {
        id: 'friend_003',
        name: '星际小天使',
        avatar: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI0VDNDg5OSIvPjx0ZXh0IHg9IjUwIiB5PSI2MCIgZm9udC1zaXplPSI0MCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPvCfmIc8L3RleHQ+PC9zdmc+',
        level: 10,
        isOnline: false,
        status: 'approved',
        isRecent: true,
        lastSeen: '2小时前',
        achievements: 22,
        wisdomEnergy: 1200,
        loveEnergy: 950,
        currentActivity: '离线',
        friendshipLevel: 4
      },
      {
        id: 'friend_004',
        name: '能量收集者',
        avatar: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI0Y1OUUwQiIvPjx0ZXh0IHg9IjUwIiB5PSI2MCIgZm9udC1zaXplPSI0MCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPuKaoTwvdGV4dD48L3N2Zz4=',
        level: 5,
        isOnline: false,
        status: 'approved',
        isRecent: false,
        lastSeen: '昨天',
        achievements: 8,
        wisdomEnergy: 420,
        loveEnergy: 380,
        currentActivity: '离线',
        friendshipLevel: 1
      },
      {
        id: 'friend_005',
        name: '新朋友申请',
        avatar: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzZCNzI4MCIvPjx0ZXh0IHg9IjUwIiB5PSI2MCIgZm9udC1zaXplPSI0MCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPuKdkzwvdGV4dD48L3N2Zz4=',
        level: 3,
        isOnline: true,
        status: 'pending',
        isRecent: false,
        lastSeen: '',
        achievements: 5,
        wisdomEnergy: 180,
        loveEnergy: 220,
        currentActivity: '等待审核',
        friendshipLevel: 0
      }
    ];
  },

  // 刷新好友数据
  refreshFriendData: function() {
    this.setData({ isLoading: true });
    
    // 模拟网络请求
    setTimeout(() => {
      this.loadFriendData();
      this.filterFriends(this.data.selectedCategory);
      this.setData({ isLoading: false });
    }, 500);
  },

  // 开始在线状态更新
  startOnlineStatusUpdate: function() {
    // 模拟在线状态变化
    setInterval(() => {
      this.updateOnlineStatus();
    }, 30000); // 30秒更新一次
  },

  // 更新在线状态
  updateOnlineStatus: function() {
    const friends = this.data.allFriends.map(friend => {
      // 随机改变在线状态（模拟）
      if (Math.random() < 0.1) { // 10%概率状态变化
        friend.isOnline = !friend.isOnline;
        if (!friend.isOnline) {
          friend.lastSeen = '刚刚';
        }
      }
      return friend;
    });
    
    const onlineCount = friends.filter(f => f.isOnline && f.status === 'approved').length;
    
    this.setData({
      allFriends: friends,
      onlineFriends: onlineCount
    });
    
    this.filterFriends(this.data.selectedCategory);
  },

  // 选择好友分类
  onSelectCategory: function(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      selectedCategory: category
    });
    this.filterFriends(category);
  },

  // 过滤好友
  filterFriends: function(category) {
    let filtered = [];
    
    switch (category) {
      case 'all':
        filtered = this.data.allFriends.filter(f => f.status === 'approved');
        break;
      case 'online':
        filtered = this.data.allFriends.filter(f => f.isOnline && f.status === 'approved');
        break;
      case 'recent':
        filtered = this.data.recentFriends.filter(f => f.status === 'approved');
        break;
      case 'pending':
        filtered = this.data.pendingFriends;
        break;
      default:
        filtered = this.data.allFriends;
    }
    
    this.setData({
      filteredFriends: filtered
    });
  },

  // 获取好友状态文本
  getFriendStatusText: function(friend) {
    if (friend.status === 'pending') {
      return '等待审核';
    }
    if (friend.isOnline) {
      return friend.currentActivity || '在线';
    }
    return '离线';
  },

  // 获取空状态标题
  getEmptyTitle: function() {
    const category = this.data.selectedCategory;
    const titles = {
      all: '还没有好友',
      online: '没有在线好友',
      recent: '没有最近互动',
      pending: '没有待审核申请'
    };
    return titles[category] || '暂无数据';
  },

  // 获取空状态副标题
  getEmptySubtitle: function() {
    const category = this.data.selectedCategory;
    const subtitles = {
      all: '添加好友开始你的社交之旅',
      online: '好友们都在忙碌中',
      recent: '去和好友互动吧',
      pending: '暂时没有新的好友申请'
    };
    return subtitles[category] || '';
  },

  // 添加好友
  onAddFriend: function() {
    console.log('添加好友');
    wx.showModal({
      title: '添加好友',
      content: '请输入好友的用户ID或扫描二维码',
      showCancel: true,
      confirmText: '输入ID',
      cancelText: '扫码添加',
      success: (res) => {
        if (res.confirm) {
          this.showAddFriendInput();
        } else if (res.cancel) {
          this.scanQRCode();
        }
      }
    });
  },

  // 显示添加好友输入框
  showAddFriendInput: function() {
    wx.showModal({
      title: '输入好友ID',
      editable: true,
      placeholderText: '请输入好友的用户ID',
      success: (res) => {
        if (res.confirm && res.content) {
          this.sendFriendRequest(res.content);
        }
      }
    });
  },

  // 扫描二维码
  scanQRCode: function() {
    wx.scanCode({
      success: (res) => {
        console.log('扫描结果:', res.result);
        this.sendFriendRequest(res.result);
      },
      fail: (error) => {
        console.error('扫描失败:', error);
        wx.showToast({
          title: '扫描失败',
          icon: 'none'
        });
      }
    });
  },

  // 发送好友申请
  sendFriendRequest: function(friendId) {
    wx.showLoading({
      title: '发送申请中...'
    });
    
    // 模拟发送申请
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '好友申请已发送',
        icon: 'success'
      });
    }, 1500);
  },

  // 查看好友详情
  onViewFriend: function(e) {
    const friend = e.currentTarget.dataset.friend;
    console.log('查看好友详情:', friend);
    
    if (friend.status === 'pending') {
      return; // 待审核好友不能查看详情
    }
    
    // TODO: 导航到好友详情页面
    wx.showToast({
      title: '好友详情功能开发中',
      icon: 'none'
    });
  },

  // 访问好友舱室
  onVisitFriend: function(e) {
    e.stopPropagation();
    const friend = e.currentTarget.dataset.friend;
    console.log('访问好友舱室:', friend);
    
    wx.showToast({
      title: '舱室访问功能开发中',
      icon: 'none'
    });
  },

  // 点赞好友
  onLikeFriend: function(e) {
    e.stopPropagation();
    const friend = e.currentTarget.dataset.friend;
    console.log('点赞好友:', friend);
    
    wx.showToast({
      title: `已为${friend.name}点赞`,
      icon: 'success'
    });
  },

  // 发送礼物
  onSendGift: function(e) {
    e.stopPropagation();
    const friend = e.currentTarget.dataset.friend;
    console.log('发送礼物:', friend);
    
    wx.showToast({
      title: '礼物功能开发中',
      icon: 'none'
    });
  },

  // 同意好友申请
  onApproveFriend: function(e) {
    e.stopPropagation();
    const friend = e.currentTarget.dataset.friend;
    console.log('同意好友申请:', friend);
    
    wx.showModal({
      title: '同意好友申请',
      content: `确定要添加 ${friend.name} 为好友吗？`,
      success: (res) => {
        if (res.confirm) {
          this.approveFriendRequest(friend);
        }
      }
    });
  },

  // 拒绝好友申请
  onRejectFriend: function(e) {
    e.stopPropagation();
    const friend = e.currentTarget.dataset.friend;
    console.log('拒绝好友申请:', friend);
    
    wx.showModal({
      title: '拒绝好友申请',
      content: `确定要拒绝 ${friend.name} 的好友申请吗？`,
      success: (res) => {
        if (res.confirm) {
          this.rejectFriendRequest(friend);
        }
      }
    });
  },

  // 处理同意好友申请
  approveFriendRequest: function(friend) {
    // 更新好友状态
    const friends = this.data.allFriends.map(f => {
      if (f.id === friend.id) {
        f.status = 'approved';
      }
      return f;
    });
    
    this.setData({
      allFriends: friends
    });
    
    this.refreshFriendData();
    
    wx.showToast({
      title: '已添加为好友',
      icon: 'success'
    });
  },

  // 处理拒绝好友申请
  rejectFriendRequest: function(friend) {
    // 从列表中移除
    const friends = this.data.allFriends.filter(f => f.id !== friend.id);
    
    this.setData({
      allFriends: friends
    });
    
    this.refreshFriendData();
    
    wx.showToast({
      title: '已拒绝申请',
      icon: 'success'
    });
  },

  // 组队活动
  onGroupActivity: function() {
    console.log('组队活动');
    wx.showToast({
      title: '组队功能开发中',
      icon: 'none'
    });
  },

  // 礼物中心
  onGiftCenter: function() {
    console.log('礼物中心');
    wx.showToast({
      title: '礼物中心开发中',
      icon: 'none'
    });
  },

  // 消息中心
  onMessageCenter: function() {
    console.log('消息中心');
    wx.showToast({
      title: '消息中心开发中',
      icon: 'none'
    });
  },

  // 好友排行
  onFriendRanking: function() {
    console.log('好友排行');
    wx.showToast({
      title: '排行榜功能开发中',
      icon: 'none'
    });
  },

  // 页面卸载
  onUnload: function() {
    console.log('好友星际站页面卸载');
  }
});
