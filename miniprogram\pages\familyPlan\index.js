// 家庭善意计划页面
Page({
  data: {
    // 家庭成员
    familyMembers: [
      { id: 'dad', name: '爸爸', role: '家长', avatar: '👨', contribution: 85, status: 'online' },
      { id: 'mom', name: '妈妈', role: '家长', avatar: '👩', contribution: 92, status: 'online' },
      { id: 'child', name: '我', role: '孩子', avatar: '👧', contribution: 78, status: 'online' },
      { id: 'sibling', name: '弟弟', role: '孩子', avatar: '👦', contribution: 65, status: 'offline' }
    ],
    
    // 活跃计划
    activePlans: [
      {
        id: 'plan_001',
        title: '家庭大扫除日',
        subtitle: '全家一起打扫房间，创造整洁环境',
        icon: '🏠',
        status: 'active',
        completedTasks: 3,
        totalTasks: 5,
        progressPercentage: 60,
        participants: [
          { id: 'dad', name: '爸爸', avatar: '👨' },
          { id: 'mom', name: '妈妈', avatar: '👩' },
          { id: 'child', name: '我', avatar: '👧' }
        ]
      },
      {
        id: 'plan_002',
        title: '社区志愿服务',
        subtitle: '参与社区清洁和帮助邻居',
        icon: '🤝',
        status: 'pending',
        completedTasks: 0,
        totalTasks: 3,
        progressPercentage: 0,
        participants: [
          { id: 'mom', name: '妈妈', avatar: '👩' },
          { id: 'child', name: '我', avatar: '👧' }
        ]
      }
    ],
    
    // 完成率
    completionRate: 75,
    
    // 快速模板
    quickTemplates: [
      {
        id: 'template_001',
        title: '家庭大扫除',
        description: '全家一起打扫房间，整理物品',
        icon: '🧹',
        duration: '2-3小时',
        tasks: ['客厅整理', '厨房清洁', '卧室整理', '垃圾分类', '物品归位']
      },
      {
        id: 'template_002',
        title: '社区服务',
        description: '参与社区志愿活动，帮助他人',
        icon: '🤝',
        duration: '1-2小时',
        tasks: ['社区清洁', '帮助老人', '宣传环保', '维护秩序']
      },
      {
        id: 'template_003',
        title: '环保行动',
        description: '家庭环保实践，保护地球',
        icon: '🌱',
        duration: '1小时',
        tasks: ['垃圾分类', '节约用水', '绿色出行', '植物养护']
      },
      {
        id: 'template_004',
        title: '爱心捐赠',
        description: '整理旧物品，捐赠给需要的人',
        icon: '💝',
        duration: '1-2小时',
        tasks: ['整理衣物', '收集书籍', '准备玩具', '联系机构']
      }
    ],
    
    // 计划历史
    recentHistory: [
      {
        id: 'history_001',
        title: '完成家庭读书计划',
        description: '全家一起阅读并分享心得',
        icon: '📚',
        time: '3天前',
        participants: ['爸爸', '妈妈', '我']
      },
      {
        id: 'history_002',
        title: '参与社区植树活动',
        description: '在社区公园种植了5棵小树',
        icon: '🌳',
        time: '1周前',
        participants: ['妈妈', '我', '弟弟']
      },
      {
        id: 'history_003',
        title: '制作爱心便当',
        description: '为环卫工人准备温暖便当',
        icon: '🍱',
        time: '2周前',
        participants: ['爸爸', '妈妈', '我']
      }
    ],
    
    // 家庭成就
    familyAchievements: [
      {
        id: 'ach_001',
        name: '团结一心',
        description: '完成第一个家庭计划',
        icon: '🤝',
        unlocked: true,
        unlockedDate: '2024-01-15',
        target: 1,
        current: 1,
        progressPercentage: 100
      },
      {
        id: 'ach_002',
        name: '善意传播者',
        description: '完成5个家庭善意计划',
        icon: '💖',
        unlocked: false,
        unlockedDate: '',
        target: 5,
        current: 3,
        progressPercentage: 60
      },
      {
        id: 'ach_003',
        name: '社区英雄',
        description: '参与10次社区服务',
        icon: '🦸',
        unlocked: false,
        unlockedDate: '',
        target: 10,
        current: 2,
        progressPercentage: 20
      },
      {
        id: 'ach_004',
        name: '环保卫士',
        description: '完成8个环保行动',
        icon: '🌍',
        unlocked: false,
        unlockedDate: '',
        target: 8,
        current: 1,
        progressPercentage: 12.5
      }
    ],
    
    // 家庭统计
    familyStats: {
      totalPlans: 15,
      completedPlans: 12,
      totalPoints: 1250,
      achievements: 1
    },
    
    // 本月数据
    monthlyData: [
      { week: 1, completed: 3, percentage: 75 },
      { week: 2, completed: 4, percentage: 100 },
      { week: 3, completed: 2, percentage: 50 },
      { week: 4, completed: 3, percentage: 75 }
    ],
    
    // 页面状态
    loading: false,
    showPlanSuccess: false,
    planSuccessMessage: ''
  },

  onLoad: function (options) {
    console.log('家庭善意计划加载');
    this.initializeFamilyPlan();
  },

  onReady: function () {
    console.log('家庭善意计划渲染完成');
  },

  onShow: function () {
    // 每次显示时刷新数据
    this.loadFamilyData();
  },

  onPullDownRefresh: function () {
    this.refreshFamilyData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  // 初始化家庭计划
  initializeFamilyPlan() {
    this.setData({ loading: true });

    try {
      // 加载家庭数据
      this.loadFamilyData();
      
      // 加载计划数据
      this.loadPlanData();
      
      // 加载成就数据
      this.loadAchievementData();
      
      // 加载统计数据
      this.loadStatsData();
      
    } catch (error) {
      console.error('初始化家庭计划失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载家庭数据
  loadFamilyData() {
    const savedMembers = wx.getStorageSync('familyMembers') || this.data.familyMembers;
    this.setData({
      familyMembers: savedMembers
    });
  },

  // 加载计划数据
  loadPlanData() {
    const savedPlans = wx.getStorageSync('familyPlans') || this.data.activePlans;
    const savedHistory = wx.getStorageSync('familyPlanHistory') || this.data.recentHistory;
    
    // 计算完成率
    const totalTasks = savedPlans.reduce((sum, plan) => sum + plan.totalTasks, 0);
    const completedTasks = savedPlans.reduce((sum, plan) => sum + plan.completedTasks, 0);
    const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
    
    this.setData({
      activePlans: savedPlans,
      recentHistory: savedHistory.slice(0, 3), // 只显示最近3条
      completionRate
    });
  },

  // 加载成就数据
  loadAchievementData() {
    const unlockedAchievements = wx.getStorageSync('familyAchievements') || [];
    const achievements = this.data.familyAchievements.map(achievement => {
      const unlocked = unlockedAchievements.includes(achievement.id);
      return {
        ...achievement,
        unlocked,
        unlockedDate: unlocked ? this.getAchievementDate(achievement.id) : ''
      };
    });
    
    this.setData({
      familyAchievements: achievements
    });
  },

  // 获取成就解锁日期
  getAchievementDate(achievementId) {
    const achievementDates = wx.getStorageSync('familyAchievementDates') || {};
    return achievementDates[achievementId] || '未知日期';
  },

  // 加载统计数据
  loadStatsData() {
    const stats = wx.getStorageSync('familyStats') || this.data.familyStats;
    const monthlyData = wx.getStorageSync('familyMonthlyData') || this.data.monthlyData;
    
    this.setData({
      familyStats: stats,
      monthlyData
    });
  },

  // 选择模板
  onSelectTemplate(e) {
    const template = e.currentTarget.dataset.template;
    
    wx.showModal({
      title: `创建计划：${template.title}`,
      content: `${template.description}\n\n预计用时：${template.duration}\n\n确定要创建这个家庭计划吗？`,
      confirmText: '创建计划',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.createPlanFromTemplate(template);
        }
      }
    });
  },

  // 从模板创建计划
  createPlanFromTemplate(template) {
    const newPlan = {
      id: `plan_${Date.now()}`,
      title: template.title,
      subtitle: template.description,
      icon: template.icon,
      status: 'pending',
      completedTasks: 0,
      totalTasks: template.tasks.length,
      progressPercentage: 0,
      participants: [
        { id: 'child', name: '我', avatar: '👧' }
      ],
      tasks: template.tasks,
      createdAt: new Date().toISOString()
    };
    
    const activePlans = [...this.data.activePlans, newPlan];
    wx.setStorageSync('familyPlans', activePlans);
    
    this.setData({
      activePlans
    });
    
    // 显示成功动画
    this.showPlanSuccessAnimation(`成功创建计划：${template.title}！`);
  },

  // 显示计划创建成功动画
  showPlanSuccessAnimation(message) {
    this.setData({
      showPlanSuccess: true,
      planSuccessMessage: message
    });
    
    setTimeout(() => {
      this.setData({
        showPlanSuccess: false
      });
    }, 3000);
  },

  // 创建自定义计划
  onCreateCustomPlan() {
    wx.showModal({
      title: '自定义计划',
      content: '自定义计划功能开发中，敬请期待！\n\n您可以先使用快速模板创建计划。',
      showCancel: false
    });
  },

  // 查看计划
  onViewPlan(e) {
    const plan = e.currentTarget.dataset.plan;
    
    let content = `${plan.subtitle}\n\n`;
    content += `进度：${plan.completedTasks}/${plan.totalTasks} (${plan.progressPercentage}%)\n`;
    content += `状态：${plan.status === 'active' ? '进行中' : plan.status === 'pending' ? '待开始' : '已完成'}\n`;
    content += `参与成员：${plan.participants.map(p => p.name).join('、')}`;
    
    wx.showModal({
      title: `${plan.icon} ${plan.title}`,
      content: content,
      confirmText: '更新进度',
      cancelText: '关闭',
      success: (res) => {
        if (res.confirm) {
          this.onUpdateProgress({ currentTarget: { dataset: { plan } } });
        }
      }
    });
  },

  // 更新进度
  onUpdateProgress(e) {
    const plan = e.currentTarget.dataset.plan;
    
    if (plan.progressPercentage >= 100) {
      wx.showToast({
        title: '计划已完成',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '更新进度',
      content: `当前进度：${plan.completedTasks}/${plan.totalTasks}\n\n是否要增加一个完成的任务？`,
      confirmText: '增加进度',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.incrementPlanProgress(plan);
        }
      }
    });
  },

  // 增加计划进度
  incrementPlanProgress(plan) {
    const activePlans = this.data.activePlans.map(p => {
      if (p.id === plan.id) {
        const newCompletedTasks = Math.min(p.completedTasks + 1, p.totalTasks);
        const newProgressPercentage = Math.round((newCompletedTasks / p.totalTasks) * 100);
        
        return {
          ...p,
          completedTasks: newCompletedTasks,
          progressPercentage: newProgressPercentage,
          status: newProgressPercentage >= 100 ? 'completed' : 'active'
        };
      }
      return p;
    });
    
    wx.setStorageSync('familyPlans', activePlans);
    
    this.setData({
      activePlans
    });
    
    this.loadPlanData(); // 重新计算完成率
    
    wx.showToast({
      title: '进度已更新！',
      icon: 'success'
    });
  },

  // 完成计划
  onCompletePlan(e) {
    const plan = e.currentTarget.dataset.plan;
    
    wx.showModal({
      title: '完成计划',
      content: `恭喜完成计划"${plan.title}"！\n\n是否要将此计划标记为已完成？`,
      confirmText: '确认完成',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.completePlan(plan);
        }
      }
    });
  },

  // 标记计划完成
  completePlan(plan) {
    // 移动到历史记录
    const history = wx.getStorageSync('familyPlanHistory') || [];
    const historyItem = {
      id: `history_${Date.now()}`,
      title: `完成${plan.title}`,
      description: plan.subtitle,
      icon: plan.icon,
      time: '刚刚',
      participants: plan.participants.map(p => p.name)
    };
    
    history.unshift(historyItem);
    wx.setStorageSync('familyPlanHistory', history);
    
    // 从活跃计划中移除
    const activePlans = this.data.activePlans.filter(p => p.id !== plan.id);
    wx.setStorageSync('familyPlans', activePlans);
    
    // 更新统计
    const stats = this.data.familyStats;
    stats.completedPlans += 1;
    stats.totalPoints += 50; // 完成计划奖励50分
    wx.setStorageSync('familyStats', stats);
    
    this.setData({
      activePlans,
      familyStats: stats
    });
    
    this.loadPlanData();
    
    wx.showToast({
      title: '计划已完成！',
      icon: 'success'
    });
  },

  // 查看成员
  onViewMember(e) {
    const member = e.currentTarget.dataset.member;
    
    wx.showModal({
      title: `${member.avatar} ${member.name}`,
      content: `角色：${member.role}\n贡献分数：${member.contribution}分\n状态：${member.status === 'online' ? '在线' : '离线'}`,
      showCancel: false
    });
  },

  // 管理成员
  onManageMembers() {
    wx.showModal({
      title: '管理家庭成员',
      content: '家庭成员管理功能开发中，敬请期待！',
      showCancel: false
    });
  },

  // 查看成就
  onViewAchievement(e) {
    const achievement = e.currentTarget.dataset.achievement;
    
    let content = `${achievement.description}\n\n`;
    if (achievement.unlocked) {
      content += `解锁时间：${achievement.unlockedDate}`;
    } else {
      content += `进度：${achievement.current}/${achievement.target} (${achievement.progressPercentage}%)`;
    }
    
    wx.showModal({
      title: `${achievement.icon} ${achievement.name}`,
      content: content,
      showCancel: false
    });
  },

  // 查看全部历史
  onViewAllHistory() {
    wx.showModal({
      title: '计划历史',
      content: '查看全部家庭计划历史记录功能开发中！',
      showCancel: false
    });
  },

  // 刷新数据
  refreshFamilyData() {
    this.setData({ loading: true });
    
    setTimeout(() => {
      this.loadFamilyData();
      this.loadPlanData();
      this.loadAchievementData();
      this.loadStatsData();
      this.setData({ loading: false });
    }, 1000);
  }
});
