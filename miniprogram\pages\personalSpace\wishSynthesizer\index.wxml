<!-- 《能量星球》愿望合成器 - 智慧能量驱动的愿望创建系统 -->
<view class="page">
  <view class="wish-synthesizer">
    
    <!-- 深空星云背景 -->
    <view class="nebula-background">
      <view class="star-field">
        <view class="star star-1"></view>
        <view class="star star-2"></view>
        <view class="star star-3"></view>
        <view class="star star-4"></view>
        <view class="star star-5"></view>
      </view>
      
      <!-- 星云漩涡 -->
      <view class="nebula-vortex">
        <view class="vortex-layer layer-1"></view>
        <view class="vortex-layer layer-2"></view>
        <view class="vortex-layer layer-3"></view>
      </view>
    </view>

    <!-- 能量状态HUD -->
    <view class="energy-hud">
      <view class="energy-display">
        <view class="energy-icon">🔷</view>
        <view class="energy-info">
          <text class="energy-value">{{wisdomEnergy}}</text>
          <text class="energy-label">智慧能量</text>
        </view>
      </view>
      <view class="cost-preview" wx:if="{{selectedCategory}}">
        <text class="cost-text">消耗: {{currentCost}}</text>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <scroll-view class="content-scroll" scroll-y="true">
      
      <!-- 愿望创建区域 -->
      <view class="wish-creation-section">
        <view class="section-header">
          <text class="section-title">✨ 创建新愿望</text>
          <text class="section-subtitle">用智慧能量点亮你的梦想</text>
        </view>

        <!-- 愿望分类选择 -->
        <view class="category-selector">
          <text class="selector-label">愿望类型</text>
          <view class="category-grid">
            <view class="category-item {{selectedCategory === 'learning' ? 'selected' : ''}}" 
                  bindtap="onSelectCategory" data-category="learning">
              <view class="category-icon">📚</view>
              <text class="category-name">学习目标</text>
              <text class="category-cost">{{categoryCosts.learning}}能量</text>
            </view>
            <view class="category-item {{selectedCategory === 'hobby' ? 'selected' : ''}}" 
                  bindtap="onSelectCategory" data-category="hobby">
              <view class="category-icon">🎨</view>
              <text class="category-name">兴趣爱好</text>
              <text class="category-cost">{{categoryCosts.hobby}}能量</text>
            </view>
            <view class="category-item {{selectedCategory === 'item' ? 'selected' : ''}}" 
                  bindtap="onSelectCategory" data-category="item">
              <view class="category-icon">🎁</view>
              <text class="category-name">物品需求</text>
              <text class="category-cost">{{categoryCosts.item}}能量</text>
            </view>
            <view class="category-item {{selectedCategory === 'experience' ? 'selected' : ''}}" 
                  bindtap="onSelectCategory" data-category="experience">
              <view class="category-icon">🌟</view>
              <text class="category-name">体验愿望</text>
              <text class="category-cost">{{categoryCosts.experience}}能量</text>
            </view>
          </view>
        </view>

        <!-- 愿望详情表单 -->
        <view class="wish-form" wx:if="{{selectedCategory}}">
          <view class="form-group">
            <text class="form-label">愿望标题</text>
            <input class="form-input" placeholder="给你的愿望起个名字..." 
                   value="{{wishTitle}}" bindinput="onTitleInput" maxlength="30"/>
          </view>
          
          <view class="form-group">
            <text class="form-label">愿望描述</text>
            <textarea class="form-textarea" placeholder="详细描述你的愿望..." 
                      value="{{wishDescription}}" bindinput="onDescriptionInput" maxlength="200"/>
          </view>
          
          <view class="form-group">
            <text class="form-label">希望实现时间</text>
            <picker mode="date" value="{{targetDate}}" bindchange="onDateChange">
              <view class="date-picker">
                <text class="date-text">{{targetDate || '选择日期'}}</text>
                <text class="date-icon">📅</text>
              </view>
            </picker>
          </view>
          
          <view class="form-group">
            <text class="form-label">优先级</text>
            <view class="priority-selector">
              <view class="priority-item {{priority === 'low' ? 'selected' : ''}}" 
                    bindtap="onSelectPriority" data-priority="low">
                <text class="priority-text">普通</text>
              </view>
              <view class="priority-item {{priority === 'normal' ? 'selected' : ''}}" 
                    bindtap="onSelectPriority" data-priority="normal">
                <text class="priority-text">重要</text>
              </view>
              <view class="priority-item {{priority === 'high' ? 'selected' : ''}}" 
                    bindtap="onSelectPriority" data-priority="high">
                <text class="priority-text">紧急</text>
              </view>
            </view>
          </view>

          <!-- 创建按钮 -->
          <view class="create-button-container">
            <view class="create-button {{canCreate ? 'enabled' : 'disabled'}}" 
                  bindtap="onCreateWish">
              <view class="button-glow"></view>
              <text class="button-text">🌟 合成愿望</text>
              <view class="energy-particles">
                <view class="particle particle-1"></view>
                <view class="particle particle-2"></view>
                <view class="particle particle-3"></view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 当前愿望列表 -->
      <view class="current-wishes-section">
        <view class="section-header">
          <text class="section-title">🌠 我的愿望</text>
          <text class="section-subtitle">{{activeWishes.length}}个愿望正在实现中</text>
        </view>

        <view class="wishes-list">
          <view class="wish-card" wx:for="{{activeWishes}}" wx:key="id">
            <view class="wish-header">
              <view class="wish-category-badge {{item.category}}">
                <text class="badge-icon">{{getCategoryIcon(item.category)}}</text>
              </view>
              <view class="wish-status {{item.status}}">
                <text class="status-text">{{getStatusText(item.status)}}</text>
              </view>
            </view>
            
            <view class="wish-content">
              <text class="wish-title">{{item.title}}</text>
              <text class="wish-description">{{item.description}}</text>
              
              <view class="wish-meta">
                <text class="wish-date">目标: {{item.targetDate}}</text>
                <text class="wish-energy">消耗: {{item.energyCost}}🔷</text>
              </view>
              
              <!-- 进度条 -->
              <view class="progress-container">
                <view class="progress-bar">
                  <view class="progress-fill" style="width: {{item.progress}}%"></view>
                </view>
                <text class="progress-text">{{item.progress}}%</text>
              </view>
            </view>
            
            <view class="wish-actions">
              <view class="action-button" bindtap="onViewWish" data-id="{{item.id}}">
                <text class="action-text">查看详情</text>
              </view>
            </view>
          </view>
          
          <!-- 空状态 -->
          <view class="empty-state" wx:if="{{activeWishes.length === 0}}">
            <view class="empty-icon">✨</view>
            <text class="empty-title">还没有愿望</text>
            <text class="empty-subtitle">创建你的第一个愿望吧！</text>
          </view>
        </view>
      </view>

    </scroll-view>

  </view>
</view>
