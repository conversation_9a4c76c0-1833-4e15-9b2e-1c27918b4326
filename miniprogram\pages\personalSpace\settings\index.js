// 《能量星球》个人设置 - 控制台主题
Page({
  data: {
    // 主题设置
    currentTheme: 'space',
    
    // 通知设置
    notifications: {
      achievement: true,
      friend: true,
      parent: true,
      system: false
    },
    
    // 隐私设置
    privacyLevels: ['公开', '仅好友', '私密'],
    privacyIndex: 1,
    privacy: {
      roomAccess: true,
      achievements: true,
      onlineStatus: true
    },
    
    // 应用信息
    appVersion: '1.0.0',
    cacheSize: '12.5MB'
  },

  onLoad: function(options) {
    console.log('个人设置页面加载');
    this.loadSettings();
  },

  onShow: function() {
    console.log('个人设置页面显示');
  },

  // 加载设置数据
  loadSettings: function() {
    try {
      // 从本地存储加载设置
      const settings = wx.getStorageSync('userSettings') || {};
      
      this.setData({
        currentTheme: settings.theme || 'space',
        notifications: {
          achievement: settings.notifications?.achievement !== false,
          friend: settings.notifications?.friend !== false,
          parent: settings.notifications?.parent !== false,
          system: settings.notifications?.system || false
        },
        privacyIndex: settings.privacyIndex || 1,
        privacy: {
          roomAccess: settings.privacy?.roomAccess !== false,
          achievements: settings.privacy?.achievements !== false,
          onlineStatus: settings.privacy?.onlineStatus !== false
        }
      });
    } catch (error) {
      console.error('加载设置失败:', error);
    }
  },

  // 保存设置数据
  saveSettings: function() {
    try {
      const settings = {
        theme: this.data.currentTheme,
        notifications: this.data.notifications,
        privacyIndex: this.data.privacyIndex,
        privacy: this.data.privacy,
        lastUpdated: new Date().toISOString()
      };
      
      wx.setStorageSync('userSettings', settings);
      console.log('设置已保存:', settings);
    } catch (error) {
      console.error('保存设置失败:', error);
    }
  },

  // 选择主题
  onSelectTheme: function(e) {
    const theme = e.currentTarget.dataset.theme;
    this.setData({
      currentTheme: theme
    });
    
    this.saveSettings();
    
    wx.showToast({
      title: '主题已切换',
      icon: 'success'
    });
    
    // TODO: 应用主题到全局
    this.applyTheme(theme);
  },

  // 应用主题
  applyTheme: function(theme) {
    // TODO: 实现全局主题切换
    console.log('应用主题:', theme);
  },

  // 通知设置变更
  onNotificationChange: function(e) {
    const type = e.currentTarget.dataset.type;
    const value = e.detail.value;
    
    const notifications = { ...this.data.notifications };
    notifications[type] = value;
    
    this.setData({
      notifications: notifications
    });
    
    this.saveSettings();
    
    wx.showToast({
      title: value ? '通知已开启' : '通知已关闭',
      icon: 'success'
    });
  },

  // 隐私级别变更
  onPrivacyChange: function(e) {
    const index = e.detail.value;
    this.setData({
      privacyIndex: index
    });
    
    this.saveSettings();
    
    wx.showToast({
      title: '隐私设置已更新',
      icon: 'success'
    });
  },

  // 隐私开关变更
  onPrivacyToggle: function(e) {
    const type = e.currentTarget.dataset.type;
    const value = e.detail.value;
    
    const privacy = { ...this.data.privacy };
    privacy[type] = value;
    
    this.setData({
      privacy: privacy
    });
    
    this.saveSettings();
    
    wx.showToast({
      title: '设置已更新',
      icon: 'success'
    });
  },

  // 修改密码
  onChangePassword: function() {
    console.log('修改密码');
    wx.showModal({
      title: '修改密码',
      content: '此功能需要家长验证，是否继续？',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  // 家长控制
  onParentControl: function() {
    console.log('家长控制');
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 设备管理
  onDeviceManagement: function() {
    console.log('设备管理');
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 数据导出
  onExportData: function() {
    console.log('数据导出');
    wx.showLoading({
      title: '准备导出数据...'
    });
    
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '导出功能开发中',
        icon: 'none'
      });
    }, 1500);
  },

  // 清理缓存
  onClearCache: function() {
    console.log('清理缓存');
    wx.showModal({
      title: '清理缓存',
      content: '确定要清理应用缓存吗？这不会影响你的个人数据。',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '清理中...'
          });
          
          setTimeout(() => {
            wx.hideLoading();
            this.setData({
              cacheSize: '0.8MB'
            });
            wx.showToast({
              title: '缓存清理完成',
              icon: 'success'
            });
          }, 2000);
        }
      }
    });
  },

  // 数据备份
  onBackupData: function() {
    console.log('数据备份');
    wx.showToast({
      title: '备份功能开发中',
      icon: 'none'
    });
  },

  // 版本信息
  onVersionInfo: function() {
    console.log('版本信息');
    wx.showModal({
      title: '版本信息',
      content: `能量星球 v${this.data.appVersion}\n\n更新内容：\n• 新增愿望合成器功能\n• 优化成就展示馆\n• 修复已知问题`,
      showCancel: false,
      confirmText: '确定'
    });
  },

  // 使用帮助
  onHelp: function() {
    console.log('使用帮助');
    wx.showToast({
      title: '帮助功能开发中',
      icon: 'none'
    });
  },

  // 联系客服
  onContact: function() {
    console.log('联系客服');
    wx.showModal({
      title: '联系客服',
      content: '客服邮箱：<EMAIL>\n客服电话：400-123-4567\n工作时间：9:00-18:00',
      showCancel: false,
      confirmText: '确定'
    });
  },

  // 用户协议
  onUserAgreement: function() {
    console.log('用户协议');
    wx.showToast({
      title: '协议页面开发中',
      icon: 'none'
    });
  },

  // 注销账户
  onAccountDeletion: function() {
    console.log('注销账户');
    wx.showModal({
      title: '危险操作',
      content: '注销账户将永久删除所有数据，此操作不可恢复！\n\n此功能需要家长确认。',
      confirmText: '我了解',
      confirmColor: '#FF3B30',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  // 页面卸载
  onUnload: function() {
    console.log('个人设置页面卸载');
  }
});
