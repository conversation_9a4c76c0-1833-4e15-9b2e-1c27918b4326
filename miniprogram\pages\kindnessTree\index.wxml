<!--善意成长树页面-->
<view class="growth-tree-container">
  
  <!-- 背景装饰 -->
  <view class="tree-background">
    <view class="floating-leaf leaf-1">🍃</view>
    <view class="floating-leaf leaf-2">🌿</view>
    <view class="floating-leaf leaf-3">🍀</view>
    <view class="floating-leaf leaf-4">🌱</view>
    <view class="sunlight-ray ray-1"></view>
    <view class="sunlight-ray ray-2"></view>
    <view class="sunlight-ray ray-3"></view>
  </view>

  <!-- 页面标题 -->
  <view class="tree-header">
    <view class="header-icon">{{treeLevel.icon}}</view>
    <view class="header-title">我的善意成长树</view>
    <view class="header-subtitle">{{treeLevel.name}} - {{kindnessCount}}个善意行为</view>
  </view>

  <!-- 成长树主体 -->
  <view class="tree-main-section">
    
    <!-- 成长树可视化 -->
    <view class="tree-visualization">
      
      <!-- 树冠层 -->
      <view class="tree-crown" style="--tree-size: {{treeSize}};">
        <!-- 基础树冠 -->
        <view class="crown-base {{treeLevel.crownClass}}">
          <view class="crown-icon">{{treeLevel.crownIcon}}</view>
        </view>
        
        <!-- 装饰层 -->
        <view class="decorations-layer">
          <view class="decoration decoration-{{item.type}}" 
                wx:for="{{treeDecorations}}" 
                wx:key="id"
                style="top: {{item.top}}%; left: {{item.left}}%;"
                bindtap="onTapDecoration" 
                data-decoration="{{item}}">
            {{item.icon}}
          </view>
        </view>
        
        <!-- 成就徽章层 -->
        <view class="badges-layer">
          <view class="achievement-badge" 
                wx:for="{{achievementBadges}}" 
                wx:key="id"
                style="top: {{item.top}}%; left: {{item.left}}%;"
                bindtap="onTapBadge" 
                data-badge="{{item}}">
            <view class="badge-icon">{{item.icon}}</view>
            <view class="badge-glow"></view>
          </view>
        </view>
      </view>
      
      <!-- 树干 -->
      <view class="tree-trunk">
        <view class="trunk-base"></view>
        <view class="trunk-rings">
          <view class="growth-ring" 
                wx:for="{{growthRings}}" 
                wx:key="level"
                style="bottom: {{item.position}}%;">
            <view class="ring-marker"></view>
            <view class="ring-label">{{item.label}}</view>
          </view>
        </view>
      </view>
      
      <!-- 树根 -->
      <view class="tree-roots">
        <view class="root root-left"></view>
        <view class="root root-right"></view>
        <view class="root root-center"></view>
      </view>
      
      <!-- 土壤 -->
      <view class="tree-soil">
        <view class="soil-layer"></view>
        <view class="soil-nutrients">
          <view class="nutrient" wx:for="{{soilNutrients}}" wx:key="type">
            {{item.icon}}
          </view>
        </view>
      </view>
      
    </view>
    
    <!-- 成长信息面板 -->
    <view class="growth-info-panel">
      <view class="info-tabs">
        <view class="info-tab {{selectedTab === 'stats' ? 'active' : ''}}" 
              bindtap="onSelectTab" data-tab="stats">
          <view class="tab-icon">📊</view>
          <view class="tab-text">统计</view>
        </view>
        <view class="info-tab {{selectedTab === 'decorations' ? 'active' : ''}}" 
              bindtap="onSelectTab" data-tab="decorations">
          <view class="tab-icon">🎨</view>
          <view class="tab-text">装饰</view>
        </view>
        <view class="info-tab {{selectedTab === 'achievements' ? 'active' : ''}}" 
              bindtap="onSelectTab" data-tab="achievements">
          <view class="tab-icon">🏆</view>
          <view class="tab-text">成就</view>
        </view>
      </view>
      
      <!-- 统计信息 -->
      <view class="tab-content stats-content" wx:if="{{selectedTab === 'stats'}}">
        <view class="stats-grid">
          <view class="stat-item">
            <view class="stat-icon">🌟</view>
            <view class="stat-number">{{kindnessCount}}</view>
            <view class="stat-label">善意行为</view>
          </view>
          <view class="stat-item">
            <view class="stat-icon">⭐</view>
            <view class="stat-number">{{totalPoints}}</view>
            <view class="stat-label">获得积分</view>
          </view>
          <view class="stat-item">
            <view class="stat-icon">🏆</view>
            <view class="stat-number">{{badgeCount}}</view>
            <view class="stat-label">获得徽章</view>
          </view>
          <view class="stat-item">
            <view class="stat-icon">🔥</view>
            <view class="stat-number">{{streakDays}}</view>
            <view class="stat-label">连续天数</view>
          </view>
        </view>
        
        <view class="level-progress">
          <view class="progress-title">成长进度</view>
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{levelProgress}}%;"></view>
          </view>
          <view class="progress-text">
            <text wx:if="{{treeLevel.next}}">距离{{treeLevel.nextName}}还需{{treeLevel.next - kindnessCount}}个善意行为</text>
            <text wx:else>已达到最高等级！</text>
          </view>
        </view>
      </view>
      
      <!-- 装饰管理 -->
      <view class="tab-content decorations-content" wx:if="{{selectedTab === 'decorations'}}">
        <view class="decoration-categories">
          <view class="decoration-category {{selectedDecoCategory === item.id ? 'active' : ''}}"
                wx:for="{{decorationCategories}}" 
                wx:key="id"
                bindtap="onSelectDecoCategory" 
                data-category="{{item.id}}">
            <view class="category-icon">{{item.icon}}</view>
            <view class="category-name">{{item.name}}</view>
          </view>
        </view>
        
        <view class="decoration-items">
          <view class="decoration-item" 
                wx:for="{{currentDecorations}}" 
                wx:key="id"
                bindtap="onSelectDecoration" 
                data-decoration="{{item}}">
            <view class="item-icon">{{item.icon}}</view>
            <view class="item-info">
              <view class="item-name">{{item.name}}</view>
              <view class="item-description">{{item.description}}</view>
              <view class="item-cost">{{item.cost}}积分</view>
            </view>
            <view class="item-status">
              <view class="add-btn" wx:if="{{!item.owned && totalPoints >= item.cost}}">添加</view>
              <view class="owned-badge" wx:elif="{{item.owned}}">已拥有</view>
              <view class="insufficient-badge" wx:else>积分不足</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 成就展示 -->
      <view class="tab-content achievements-content" wx:if="{{selectedTab === 'achievements'}}">
        <view class="achievement-list">
          <view class="achievement-item" 
                wx:for="{{allAchievements}}" 
                wx:key="id"
                bindtap="onViewAchievement" 
                data-achievement="{{item}}">
            <view class="achievement-icon {{item.unlocked ? 'unlocked' : 'locked'}}">
              {{item.unlocked ? item.icon : '🔒'}}
            </view>
            <view class="achievement-info">
              <view class="achievement-name">{{item.name}}</view>
              <view class="achievement-description">{{item.description}}</view>
              <view class="achievement-progress" wx:if="{{!item.unlocked}}">
                <view class="progress-bar">
                  <view class="progress-fill" style="width: {{item.progressPercentage}}%;"></view>
                </view>
                <view class="progress-text">{{item.current}}/{{item.target}}</view>
              </view>
              <view class="achievement-date" wx:else>
                {{item.unlockedDate}}
              </view>
            </view>
            <view class="achievement-reward" wx:if="{{item.unlocked}}">
              <view class="reward-icon">🎁</view>
            </view>
          </view>
        </view>
      </view>
      
    </view>
    
  </view>

  <!-- 快速操作 -->
  <view class="quick-actions">
    <view class="action-btn" bindtap="onWaterTree">
      <view class="btn-icon">💧</view>
      <view class="btn-text">浇水</view>
    </view>
    <view class="action-btn" bindtap="onFertilizeTree">
      <view class="btn-icon">🌱</view>
      <view class="btn-text">施肥</view>
    </view>
    <view class="action-btn" bindtap="onShareTree">
      <view class="btn-icon">📤</view>
      <view class="btn-text">分享</view>
    </view>
    <view class="action-btn" bindtap="onResetView">
      <view class="btn-icon">🔄</view>
      <view class="btn-text">重置</view>
    </view>
  </view>

  <!-- 成长里程碑 -->
  <view class="milestones-section">
    <view class="section-title">
      <view class="title-icon">🎯</view>
      <view class="title-text">成长里程碑</view>
    </view>
    
    <view class="milestones-timeline">
      <view class="milestone-item {{item.achieved ? 'achieved' : ''}}" 
            wx:for="{{milestones}}" 
            wx:key="id">
        <view class="milestone-marker">
          <view class="marker-icon">{{item.achieved ? item.icon : '○'}}</view>
        </view>
        <view class="milestone-content">
          <view class="milestone-title">{{item.title}}</view>
          <view class="milestone-description">{{item.description}}</view>
          <view class="milestone-reward" wx:if="{{item.achieved}}">
            已获得：{{item.reward}}
          </view>
        </view>
        <view class="milestone-line" wx:if="{{index < milestones.length - 1}}"></view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-spinner">
      <view class="spinner-icon">🌳</view>
      <view class="loading-text">成长树加载中...</view>
    </view>
  </view>

  <!-- 装饰添加成功动画 -->
  <view class="decoration-success-overlay" wx:if="{{showDecorationSuccess}}">
    <view class="success-content">
      <view class="success-icon">🎉</view>
      <view class="success-message">{{decorationSuccessMessage}}</view>
      <view class="success-sparkles">
        <view class="sparkle sparkle-1">✨</view>
        <view class="sparkle sparkle-2">⭐</view>
        <view class="sparkle sparkle-3">🌟</view>
        <view class="sparkle sparkle-4">💫</view>
        <view class="sparkle sparkle-5">✨</view>
      </view>
    </view>
  </view>

</view>
