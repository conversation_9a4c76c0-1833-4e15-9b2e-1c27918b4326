<!-- 《能量星球》好友星际站 - 通讯阵列主题 -->
<view class="page">
  <view class="friend-station">
    
    <!-- 通讯阵列背景 -->
    <view class="communication-array">
      <!-- 雷达扫描 -->
      <view class="radar-scanner">
        <view class="radar-circle circle-1"></view>
        <view class="radar-circle circle-2"></view>
        <view class="radar-circle circle-3"></view>
        <view class="radar-sweep"></view>
      </view>
      
      <!-- 信号波纹 -->
      <view class="signal-waves">
        <view class="signal-wave wave-1"></view>
        <view class="signal-wave wave-2"></view>
        <view class="signal-wave wave-3"></view>
      </view>
      
      <!-- 数据传输线 -->
      <view class="data-streams">
        <view class="data-stream stream-1"></view>
        <view class="data-stream stream-2"></view>
        <view class="data-stream stream-3"></view>
        <view class="data-stream stream-4"></view>
      </view>
    </view>

    <!-- 通讯状态HUD -->
    <view class="communication-hud">
      <view class="hud-left">
        <view class="signal-strength">
          <text class="signal-icon">📡</text>
          <view class="signal-bars">
            <view class="signal-bar bar-1 active"></view>
            <view class="signal-bar bar-2 active"></view>
            <view class="signal-bar bar-3 active"></view>
            <view class="signal-bar bar-4 active"></view>
          </view>
          <text class="signal-text">信号强度</text>
        </view>
      </view>
      
      <view class="hud-center">
        <view class="online-status">
          <text class="status-count">{{onlineFriends}}</text>
          <text class="status-label">在线好友</text>
        </view>
      </view>
      
      <view class="hud-right">
        <view class="add-friend-btn" bindtap="onAddFriend">
          <text class="add-icon">➕</text>
          <text class="add-text">添加好友</text>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <scroll-view class="content-scroll" scroll-y="true">
      
      <!-- 好友分类标签 -->
      <view class="friend-categories">
        <view class="category-tabs">
          <view class="tab-item {{selectedCategory === 'all' ? 'active' : ''}}" 
                bindtap="onSelectCategory" data-category="all">
            <text class="tab-text">全部</text>
            <view class="tab-count">{{allFriends.length}}</view>
          </view>
          <view class="tab-item {{selectedCategory === 'online' ? 'active' : ''}}" 
                bindtap="onSelectCategory" data-category="online">
            <text class="tab-text">在线</text>
            <view class="tab-count">{{onlineFriends}}</view>
          </view>
          <view class="tab-item {{selectedCategory === 'recent' ? 'active' : ''}}" 
                bindtap="onSelectCategory" data-category="recent">
            <text class="tab-text">最近</text>
            <view class="tab-count">{{recentFriends.length}}</view>
          </view>
          <view class="tab-item {{selectedCategory === 'pending' ? 'active' : ''}}" 
                bindtap="onSelectCategory" data-category="pending">
            <text class="tab-text">待审核</text>
            <view class="tab-count">{{pendingFriends.length}}</view>
          </view>
        </view>
      </view>

      <!-- 好友列表 -->
      <view class="friends-list">
        <view class="friend-card {{item.status}}" 
              wx:for="{{filteredFriends}}" wx:key="id"
              bindtap="onViewFriend" data-friend="{{item}}">
          
          <!-- 好友头像区域 -->
          <view class="friend-avatar-container">
            <view class="avatar-frame {{item.isOnline ? 'online' : 'offline'}}">
              <image class="friend-avatar" src="{{item.avatar}}" mode="aspectFill"/>
              <view class="status-indicator {{item.isOnline ? 'online' : 'offline'}}"></view>
            </view>
            
            <!-- 能量连接线 -->
            <view class="energy-connection" wx:if="{{item.isOnline}}">
              <view class="connection-particle particle-1"></view>
              <view class="connection-particle particle-2"></view>
              <view class="connection-particle particle-3"></view>
            </view>
          </view>
          
          <!-- 好友信息 -->
          <view class="friend-info">
            <view class="friend-header">
              <text class="friend-name">{{item.name}}</text>
              <view class="friend-level">
                <text class="level-text">Lv.{{item.level}}</text>
              </view>
            </view>
            
            <view class="friend-status">
              <text class="status-text">{{getFriendStatusText(item)}}</text>
              <text class="last-seen" wx:if="{{!item.isOnline}}">{{item.lastSeen}}</text>
            </view>
            
            <view class="friend-stats">
              <view class="stat-item">
                <text class="stat-icon">🏆</text>
                <text class="stat-value">{{item.achievements}}</text>
              </view>
              <view class="stat-item">
                <text class="stat-icon">🔷</text>
                <text class="stat-value">{{item.wisdomEnergy}}</text>
              </view>
              <view class="stat-item">
                <text class="stat-icon">❤️</text>
                <text class="stat-value">{{item.loveEnergy}}</text>
              </view>
            </view>
          </view>
          
          <!-- 快速操作 -->
          <view class="friend-actions">
            <view class="action-btn visit-btn" bindtap="onVisitFriend" 
                  data-friend="{{item}}" wx:if="{{item.status === 'approved'}}">
              <text class="action-icon">🚀</text>
            </view>
            <view class="action-btn like-btn" bindtap="onLikeFriend" 
                  data-friend="{{item}}" wx:if="{{item.status === 'approved'}}">
              <text class="action-icon">👍</text>
            </view>
            <view class="action-btn gift-btn" bindtap="onSendGift" 
                  data-friend="{{item}}" wx:if="{{item.status === 'approved'}}">
              <text class="action-icon">🎁</text>
            </view>
            
            <!-- 待审核状态的操作 -->
            <view class="pending-actions" wx:if="{{item.status === 'pending'}}">
              <view class="approve-btn" bindtap="onApproveFriend" data-friend="{{item}}">
                <text class="approve-text">同意</text>
              </view>
              <view class="reject-btn" bindtap="onRejectFriend" data-friend="{{item}}">
                <text class="reject-text">拒绝</text>
              </view>
            </view>
          </view>
          
          <!-- 全息投影效果 -->
          <view class="hologram-effect" wx:if="{{item.isOnline}}"></view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-friends" wx:if="{{filteredFriends.length === 0}}">
          <view class="empty-icon">👥</view>
          <text class="empty-title">{{getEmptyTitle()}}</text>
          <text class="empty-subtitle">{{getEmptySubtitle()}}</text>
          
          <view class="empty-action" wx:if="{{selectedCategory === 'all'}}" bindtap="onAddFriend">
            <text class="action-text">添加第一个好友</text>
          </view>
        </view>
      </view>

      <!-- 互动功能区域 -->
      <view class="interaction-section" wx:if="{{selectedCategory === 'all' && allFriends.length > 0}}">
        <view class="section-header">
          <text class="section-title">🌟 互动功能</text>
          <text class="section-subtitle">与好友一起探索宇宙</text>
        </view>
        
        <view class="interaction-grid">
          <view class="interaction-item" bindtap="onGroupActivity">
            <view class="interaction-icon">🎮</view>
            <text class="interaction-title">组队活动</text>
            <text class="interaction-desc">邀请好友一起完成任务</text>
          </view>
          
          <view class="interaction-item" bindtap="onGiftCenter">
            <view class="interaction-icon">🎁</view>
            <text class="interaction-title">礼物中心</text>
            <text class="interaction-desc">选择礼物送给好友</text>
          </view>
          
          <view class="interaction-item" bindtap="onMessageCenter">
            <view class="interaction-icon">💌</view>
            <text class="interaction-title">消息中心</text>
            <text class="interaction-desc">查看好友互动消息</text>
          </view>
          
          <view class="interaction-item" bindtap="onFriendRanking">
            <view class="interaction-icon">🏅</view>
            <text class="interaction-title">好友排行</text>
            <text class="interaction-desc">查看好友成就排名</text>
          </view>
        </view>
      </view>

      <!-- 安全提醒 -->
      <view class="safety-reminder">
        <view class="reminder-header">
          <text class="reminder-icon">🛡️</text>
          <text class="reminder-title">安全提醒</text>
        </view>
        <text class="reminder-text">• 只添加认识的朋友为好友</text>
        <text class="reminder-text">• 不要分享个人隐私信息</text>
        <text class="reminder-text">• 遇到不当行为请及时举报</text>
        <text class="reminder-text">• 所有互动都会记录并接受家长监督</text>
      </view>

    </scroll-view>

  </view>
</view>
