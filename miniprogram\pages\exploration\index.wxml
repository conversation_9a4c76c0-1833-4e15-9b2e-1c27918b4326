<!--pages/exploration/index.wxml-->
<!-- 探索星球主界面 - 简洁网格布局 -->
<view class="exploration-container">

  <!-- 顶部标题栏 -->
  <view class="header-section">
    <view class="title-area">
      <text class="main-title">🚀 思维工坊</text>
      <text class="sub-title">探索宇宙，启发智慧</text>
    </view>
    <view class="energy-info">
      <text class="energy-text">💎 {{energyData.wisdom}}</text>
    </view>
  </view>

  <!-- 探索者信息卡片 -->
  <view class="explorer-card">
    <view class="explorer-avatar">
      <text class="avatar-icon">👨‍🚀</text>
    </view>
    <view class="explorer-info">
      <text class="explorer-name">{{explorerData.title}}</text>
      <text class="explorer-level">等级 {{explorerData.level}}</text>
    </view>
    <view class="explorer-stats">
      <text class="stats-text">已完成 {{explorerData.totalGames}} 个任务</text>
    </view>
  </view>

  <!-- 星球网格区域 -->
  <view class="planets-grid">

    <!-- 音律星球 -->
    <view class="planet-card {{planetStates.auditory.status}}"
          bindtap="onPlanetTap" data-planet="auditory">
      <view class="planet-icon-area">
        <text class="planet-emoji">🎵</text>
        <view class="planet-glow glow-purple"></view>
      </view>
      <view class="planet-info">
        <text class="planet-name">音律星球</text>
        <text class="planet-desc">听觉训练</text>
      </view>
      <view class="planet-status">
        <text class="status-text">{{planetStates.auditory.status === 'locked' ? '🔒' : '✨'}}</text>
      </view>
    </view>

    <!-- 光影星球 -->
    <view class="planet-card {{planetStates.visual.status}}"
          bindtap="onPlanetTap" data-planet="visual">
      <view class="planet-icon-area">
        <text class="planet-emoji">👁️</text>
        <view class="planet-glow glow-blue"></view>
      </view>
      <view class="planet-info">
        <text class="planet-name">光影星球</text>
        <text class="planet-desc">视觉训练</text>
      </view>
      <view class="planet-status">
        <text class="status-text">{{planetStates.visual.status === 'locked' ? '🔒' : '✨'}}</text>
      </view>
    </view>

    <!-- 智慧星球 -->
    <view class="planet-card {{planetStates.logic.status}}"
          bindtap="onPlanetTap" data-planet="logic">
      <view class="planet-icon-area">
        <text class="planet-emoji">🧠</text>
        <view class="planet-glow glow-orange"></view>
      </view>
      <view class="planet-info">
        <text class="planet-name">智慧星球</text>
        <text class="planet-desc">逻辑思维</text>
      </view>
      <view class="planet-status">
        <text class="status-text">{{planetStates.logic.status === 'locked' ? '🔒' : '✨'}}</text>
      </view>
    </view>

    <!-- 语言星球 -->
    <view class="planet-card {{planetStates.language.status}}"
          bindtap="onPlanetTap" data-planet="language">
      <view class="planet-icon-area">
        <text class="planet-emoji">💬</text>
        <view class="planet-glow glow-green"></view>
      </view>
      <view class="planet-info">
        <text class="planet-name">语言星球</text>
        <text class="planet-desc">语言表达</text>
      </view>
      <view class="planet-status">
        <text class="status-text">{{planetStates.language.status === 'locked' ? '🔒' : '✨'}}</text>
      </view>
    </view>

    <!-- 创造星球 -->
    <view class="planet-card {{planetStates.creative.status}}"
          bindtap="onPlanetTap" data-planet="creative">
      <view class="planet-icon-area">
        <text class="planet-emoji">🎨</text>
        <view class="planet-glow glow-pink"></view>
      </view>
      <view class="planet-info">
        <text class="planet-name">创造星球</text>
        <text class="planet-desc">创意游戏</text>
      </view>
      <view class="planet-status">
        <text class="status-text">{{planetStates.creative.status === 'locked' ? '🔒' : '✨'}}</text>
      </view>
    </view>

    <!-- 认知训练中心 -->
    <view class="planet-card special-card"
          bindtap="onCognitiveTrainingTap">
      <view class="planet-icon-area">
        <text class="planet-emoji">🧩</text>
        <view class="planet-glow glow-rainbow"></view>
      </view>
      <view class="planet-info">
        <text class="planet-name">认知训练</text>
        <text class="planet-desc">综合能力</text>
      </view>
      <view class="planet-status">
        <text class="status-text">🌟</text>
      </view>
    </view>

  </view>



  <!-- 星球详情弹窗 -->
  <view class="modal-overlay {{showPlanetModal ? 'show' : ''}}" bindtap="onClosePlanetModal">
    <view class="planet-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">{{selectedPlanet.name}}</text>
        <text class="modal-close" bindtap="onClosePlanetModal">×</text>
      </view>
      <view class="planet-detail">
        <view class="planet-large-icon">
          <text class="large-emoji">{{selectedPlanet.icon}}</text>
        </view>
        <text class="planet-description">{{selectedPlanet.description}}</text>
        <view class="games-preview">
          <text class="games-title">包含游戏:</text>
          <view class="games-list">
            <text class="game-item" wx:for="{{selectedPlanet.games}}" wx:key="*this">
              • {{gameNames[item]}}
            </text>
          </view>
        </view>
      </view>
      <view class="modal-actions">
        <button class="start-btn {{selectedPlanet.status === 'locked' ? 'disabled' : ''}}"
                bindtap="onStartExploration"
                disabled="{{selectedPlanet.status === 'locked'}}">
          {{selectedPlanet.status === 'locked' ? '未解锁' : '开始探索'}}
        </button>
      </view>
    </view>
  </view>

</view>