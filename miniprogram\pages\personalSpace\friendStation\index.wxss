/* 《能量星球》好友星际站 - 通讯阵列主题 */

/* ==================== CSS变量系统 ==================== */
page {
  /* 通讯阵列主题色彩 */
  --comm-gradient: linear-gradient(135deg, #0D1421 0%, #1E3A8A 30%, #059669 60%, #10B981 100%);
  --comm-dark: #0D1421;
  --comm-medium: #1E3A8A;
  --comm-light: #10B981;
  
  /* 雷达扫描颜色 */
  --radar-green: #00FF88;
  --radar-blue: #00D4FF;
  --radar-orange: #FF8C00;
  --radar-purple: #8B5CF6;
  
  /* 信号颜色系统 */
  --signal-strong: #10B981;
  --signal-medium: #F59E0B;
  --signal-weak: #EF4444;
  --signal-offline: #6B7280;
  
  /* 好友状态颜色 */
  --friend-online: #10B981;
  --friend-offline: #6B7280;
  --friend-pending: #F59E0B;
  --friend-blocked: #EF4444;
  
  /* 全息效果颜色 */
  --hologram-cyan: #06B6D4;
  --hologram-green: #10B981;
  --hologram-blue: #3B82F6;
  --hologram-purple: #8B5CF6;
  
  /* 阴影和动画 */
  --shadow-subtle: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  --shadow-medium: 0 8rpx 24rpx rgba(0, 0, 0, 0.25);
  --shadow-strong: 0 16rpx 48rpx rgba(0, 0, 0, 0.35);
  --shadow-dramatic: 0 32rpx 64rpx rgba(0, 0, 0, 0.45);
  
  --duration-fast: 0.2s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;
  --duration-dramatic: 1s;
  
  --ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
  --ease-in-out-quart: cubic-bezier(0.76, 0, 0.24, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ==================== 基础页面样式 ==================== */
.page {
  width: 100%;
  height: 100vh;
  background: var(--comm-gradient);
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
}

.friend-station {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* ==================== 通讯阵列背景 ==================== */
.communication-array {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* ==================== 雷达扫描系统 ==================== */
.radar-scanner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  height: 600rpx;
  opacity: 0.3;
}

.radar-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2rpx solid var(--radar-green);
  border-radius: 50%;
  animation: radarPulse 4s ease-in-out infinite;
}

.radar-circle.circle-1 {
  width: 600rpx;
  height: 600rpx;
  animation-delay: 0s;
}

.radar-circle.circle-2 {
  width: 400rpx;
  height: 400rpx;
  border-color: var(--radar-blue);
  animation-delay: 1s;
}

.radar-circle.circle-3 {
  width: 200rpx;
  height: 200rpx;
  border-color: var(--radar-orange);
  animation-delay: 2s;
}

@keyframes radarPulse {
  0%, 100% { 
    opacity: 0.3; 
    transform: translate(-50%, -50%) scale(1); 
  }
  50% { 
    opacity: 0.8; 
    transform: translate(-50%, -50%) scale(1.1); 
  }
}

.radar-sweep {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 300rpx;
  height: 2rpx;
  background: linear-gradient(90deg, 
    transparent 0%, 
    var(--radar-green) 50%, 
    transparent 100%
  );
  transform-origin: left center;
  transform: translate(0, -50%);
  animation: radarSweep 3s linear infinite;
  box-shadow: 0 0 20rpx var(--radar-green);
}

@keyframes radarSweep {
  0% { transform: translate(0, -50%) rotate(0deg); }
  100% { transform: translate(0, -50%) rotate(360deg); }
}

/* ==================== 信号波纹 ==================== */
.signal-waves {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.signal-wave {
  position: absolute;
  border: 3rpx solid var(--signal-strong);
  border-radius: 50%;
  animation: waveExpand 6s ease-out infinite;
}

.signal-wave.wave-1 {
  top: 20%;
  left: 20%;
  width: 100rpx;
  height: 100rpx;
  animation-delay: 0s;
}

.signal-wave.wave-2 {
  top: 60%;
  right: 25%;
  width: 80rpx;
  height: 80rpx;
  border-color: var(--signal-medium);
  animation-delay: 2s;
}

.signal-wave.wave-3 {
  bottom: 30%;
  left: 30%;
  width: 120rpx;
  height: 120rpx;
  border-color: var(--signal-weak);
  animation-delay: 4s;
}

@keyframes waveExpand {
  0% { 
    transform: scale(0); 
    opacity: 1; 
  }
  100% { 
    transform: scale(3); 
    opacity: 0; 
  }
}

/* ==================== 数据传输线 ==================== */
.data-streams {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.data-stream {
  position: absolute;
  height: 2rpx;
  background: linear-gradient(90deg, 
    transparent 0%, 
    var(--hologram-cyan) 20%, 
    var(--hologram-green) 50%, 
    var(--hologram-blue) 80%, 
    transparent 100%
  );
  box-shadow: 0 0 8rpx currentColor;
  animation: dataFlow 8s linear infinite;
}

.data-stream.stream-1 {
  top: 25%;
  left: 0;
  width: 100%;
  animation-delay: 0s;
}

.data-stream.stream-2 {
  top: 50%;
  left: 0;
  width: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    var(--hologram-green) 20%, 
    var(--hologram-blue) 50%, 
    var(--hologram-purple) 80%, 
    transparent 100%
  );
  animation-delay: 2s;
}

.data-stream.stream-3 {
  top: 75%;
  left: 0;
  width: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    var(--hologram-blue) 20%, 
    var(--hologram-purple) 50%, 
    var(--hologram-cyan) 80%, 
    transparent 100%
  );
  animation-delay: 4s;
}

.data-stream.stream-4 {
  top: 35%;
  left: 0;
  width: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    var(--hologram-purple) 20%, 
    var(--hologram-cyan) 50%, 
    var(--hologram-green) 80%, 
    transparent 100%
  );
  animation-delay: 6s;
}

@keyframes dataFlow {
  0% { 
    transform: translateX(-100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% { 
    transform: translateX(100%);
    opacity: 0;
  }
}

/* ==================== 通讯状态HUD ==================== */
.communication-hud {
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  background: linear-gradient(90deg, 
    rgba(16, 185, 129, 0.1) 0%, 
    rgba(6, 182, 212, 0.1) 50%, 
    rgba(59, 130, 246, 0.1) 100%
  );
  border-bottom: 2rpx solid rgba(16, 185, 129, 0.3);
  backdrop-filter: blur(20rpx);
  animation: hudGlow 4s ease-in-out infinite;
}

@keyframes hudGlow {
  0%, 100% { 
    border-bottom-color: rgba(16, 185, 129, 0.3);
  }
  50% { 
    border-bottom-color: rgba(16, 185, 129, 0.8);
  }
}

.signal-strength {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.signal-icon {
  font-size: 28rpx;
  filter: drop-shadow(0 0 8rpx var(--signal-strong));
  animation: signalRotate 6s linear infinite;
}

@keyframes signalRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.signal-bars {
  display: flex;
  align-items: end;
  gap: 4rpx;
}

.signal-bar {
  width: 6rpx;
  background: var(--signal-offline);
  border-radius: 3rpx;
  transition: all var(--duration-normal) var(--ease-out-quart);
}

.signal-bar.bar-1 { height: 12rpx; }
.signal-bar.bar-2 { height: 18rpx; }
.signal-bar.bar-3 { height: 24rpx; }
.signal-bar.bar-4 { height: 30rpx; }

.signal-bar.active {
  background: var(--signal-strong);
  box-shadow: 0 0 8rpx var(--signal-strong);
  animation: barPulse 2s ease-in-out infinite;
}

@keyframes barPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.signal-text {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-left: 8rpx;
}

.online-status {
  text-align: center;
  background: rgba(16, 185, 129, 0.15);
  border: 2rpx solid var(--signal-strong);
  border-radius: 20rpx;
  padding: 12rpx 20rpx;
  box-shadow: 0 0 20rpx rgba(16, 185, 129, 0.3);
}

.status-count {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 0 15rpx var(--signal-strong);
  animation: countGlow 3s ease-in-out infinite;
}

@keyframes countGlow {
  0%, 100% { 
    text-shadow: 0 0 15rpx var(--signal-strong);
  }
  50% { 
    text-shadow: 0 0 30rpx var(--signal-strong);
  }
}

.status-label {
  display: block;
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 4rpx;
}

.add-friend-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: linear-gradient(135deg, 
    var(--hologram-cyan) 0%, 
    var(--hologram-blue) 100%
  );
  border: 2rpx solid var(--hologram-cyan);
  border-radius: 20rpx;
  padding: 12rpx 20rpx;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out-quart);
  box-shadow: 0 0 20rpx rgba(6, 182, 212, 0.3);
  animation: addBtnGlow 4s ease-in-out infinite;
}

@keyframes addBtnGlow {
  0%, 100% { 
    box-shadow: 0 0 20rpx rgba(6, 182, 212, 0.3);
  }
  50% { 
    box-shadow: 0 0 35rpx rgba(6, 182, 212, 0.6);
  }
}

.add-friend-btn:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 30rpx rgba(6, 182, 212, 0.5);
}

.add-icon {
  font-size: 20rpx;
  filter: drop-shadow(0 0 6rpx white);
}

.add-text {
  font-size: 20rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* ==================== 主要内容区域 ==================== */
.content-scroll {
  position: relative;
  z-index: 2;
  flex: 1;
  padding: 0 30rpx 40rpx;
  box-sizing: border-box;
  width: 100%;
}

/* ==================== 好友分类标签 ==================== */
.friend-categories {
  margin-bottom: 30rpx;
}

.category-tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 16rpx;
  padding: 8rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.15);
}

.tab-item {
  position: relative;
  flex: 1;
  text-align: center;
  padding: 16rpx 8rpx;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out-quart);
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
}

.tab-item.active {
  background: rgba(16, 185, 129, 0.2);
  box-shadow: 0 0 15rpx rgba(16, 185, 129, 0.3);
}

.tab-text {
  font-size: 20rpx;
  color: white;
  font-weight: bold;
  position: relative;
  z-index: 2;
}

.tab-item.active .tab-text {
  color: var(--signal-strong);
  text-shadow: 0 0 8rpx var(--signal-strong);
}

.tab-count {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10rpx;
  padding: 2rpx 8rpx;
  font-size: 16rpx;
  color: white;
  min-width: 24rpx;
  text-align: center;
}

.tab-item.active .tab-count {
  background: var(--signal-strong);
  color: white;
  box-shadow: 0 0 8rpx var(--signal-strong);
}

/* ==================== 好友列表 ==================== */
.friends-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.friend-card {
  position: relative;
  background: rgba(255, 255, 255, 0.08);
  border: 2rpx solid rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  padding: 24rpx;
  backdrop-filter: blur(10rpx);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out-quart);
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.friend-card:hover {
  background: rgba(255, 255, 255, 0.12);
  transform: translateY(-4rpx);
  box-shadow: var(--shadow-medium);
}

.friend-card.pending {
  border-color: rgba(245, 158, 11, 0.3);
  background: rgba(245, 158, 11, 0.05);
}

.friend-card.approved {
  border-color: rgba(16, 185, 129, 0.3);
}

.friend-card.approved:hover {
  border-color: rgba(16, 185, 129, 0.6);
  box-shadow: 0 0 25rpx rgba(16, 185, 129, 0.3);
}

/* ==================== 好友头像区域 ==================== */
.friend-avatar-container {
  position: relative;
  flex-shrink: 0;
}

.avatar-frame {
  position: relative;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  border: 3rpx solid var(--friend-offline);
  padding: 4rpx;
  transition: all var(--duration-normal) var(--ease-out-quart);
}

.avatar-frame.online {
  border-color: var(--friend-online);
  box-shadow: 0 0 20rpx rgba(16, 185, 129, 0.5);
  animation: avatarGlow 3s ease-in-out infinite;
}

@keyframes avatarGlow {
  0%, 100% {
    box-shadow: 0 0 20rpx rgba(16, 185, 129, 0.5);
  }
  50% {
    box-shadow: 0 0 35rpx rgba(16, 185, 129, 0.8);
  }
}

.friend-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.status-indicator {
  position: absolute;
  bottom: 4rpx;
  right: 4rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  border: 3rpx solid white;
  background: var(--friend-offline);
}

.status-indicator.online {
  background: var(--friend-online);
  box-shadow: 0 0 10rpx var(--friend-online);
  animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

/* ==================== 能量连接线 ==================== */
.energy-connection {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120rpx;
  height: 120rpx;
  pointer-events: none;
}

.connection-particle {
  position: absolute;
  width: 6rpx;
  height: 6rpx;
  background: var(--hologram-cyan);
  border-radius: 50%;
  box-shadow: 0 0 8rpx var(--hologram-cyan);
  animation: particleOrbit 4s linear infinite;
}

.connection-particle.particle-1 {
  animation-delay: 0s;
}

.connection-particle.particle-2 {
  animation-delay: 1.3s;
}

.connection-particle.particle-3 {
  animation-delay: 2.6s;
}

@keyframes particleOrbit {
  0% {
    transform: rotate(0deg) translateX(60rpx) rotate(0deg);
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    transform: rotate(360deg) translateX(60rpx) rotate(-360deg);
    opacity: 1;
  }
}

/* ==================== 好友信息 ==================== */
.friend-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.friend-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.friend-name {
  font-size: 24rpx;
  font-weight: bold;
  color: white;
}

.friend-level {
  background: linear-gradient(135deg,
    var(--hologram-cyan) 0%,
    var(--hologram-blue) 100%
  );
  border-radius: 12rpx;
  padding: 4rpx 12rpx;
  box-shadow: 0 0 10rpx rgba(6, 182, 212, 0.3);
}

.level-text {
  font-size: 16rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

.friend-status {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.status-text {
  font-size: 18rpx;
  color: var(--signal-strong);
  font-weight: bold;
}

.last-seen {
  font-size: 16rpx;
  color: rgba(255, 255, 255, 0.6);
}

.friend-stats {
  display: flex;
  gap: 16rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8rpx;
  padding: 4rpx 8rpx;
}

.stat-icon {
  font-size: 16rpx;
  filter: drop-shadow(0 0 4rpx currentColor);
}

.stat-value {
  font-size: 16rpx;
  color: white;
  font-weight: bold;
}

/* ==================== 快速操作 ==================== */
.friend-actions {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex-shrink: 0;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out-quart);
  border: 2rpx solid transparent;
}

.visit-btn {
  background: linear-gradient(135deg,
    var(--hologram-blue) 0%,
    var(--hologram-purple) 100%
  );
  border-color: var(--hologram-blue);
  box-shadow: 0 0 15rpx rgba(59, 130, 246, 0.3);
}

.visit-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 0 25rpx rgba(59, 130, 246, 0.5);
}

.like-btn {
  background: linear-gradient(135deg,
    var(--hologram-green) 0%,
    var(--hologram-cyan) 100%
  );
  border-color: var(--hologram-green);
  box-shadow: 0 0 15rpx rgba(16, 185, 129, 0.3);
}

.like-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 0 25rpx rgba(16, 185, 129, 0.5);
}

.gift-btn {
  background: linear-gradient(135deg,
    var(--hologram-purple) 0%,
    var(--hologram-cyan) 100%
  );
  border-color: var(--hologram-purple);
  box-shadow: 0 0 15rpx rgba(139, 92, 246, 0.3);
}

.gift-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 0 25rpx rgba(139, 92, 246, 0.5);
}

.action-icon {
  font-size: 24rpx;
  filter: drop-shadow(0 0 6rpx white);
}

/* ==================== 待审核操作 ==================== */
.pending-actions {
  display: flex;
  gap: 8rpx;
}

.approve-btn,
.reject-btn {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  font-weight: bold;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out-quart);
  border: 2rpx solid transparent;
}

.approve-btn {
  background: linear-gradient(135deg,
    var(--signal-strong) 0%,
    var(--hologram-green) 100%
  );
  border-color: var(--signal-strong);
  color: white;
  box-shadow: 0 0 15rpx rgba(16, 185, 129, 0.3);
}

.approve-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 20rpx rgba(16, 185, 129, 0.5);
}

.reject-btn {
  background: linear-gradient(135deg,
    var(--signal-weak) 0%,
    #DC2626 100%
  );
  border-color: var(--signal-weak);
  color: white;
  box-shadow: 0 0 15rpx rgba(239, 68, 68, 0.3);
}

.reject-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 20rpx rgba(239, 68, 68, 0.5);
}

.approve-text,
.reject-text {
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

/* ==================== 全息投影效果 ==================== */
.hologram-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
  background: linear-gradient(45deg,
    transparent 30%,
    rgba(6, 182, 212, 0.1) 50%,
    transparent 70%
  );
  animation: hologramScan 4s linear infinite;
  pointer-events: none;
}

@keyframes hologramScan {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* ==================== 空状态 ==================== */
.empty-friends {
  text-align: center;
  padding: 80rpx 40rpx;
  opacity: 0.8;
}

.empty-icon {
  font-size: 100rpx;
  margin-bottom: 24rpx;
  filter: drop-shadow(0 0 20rpx var(--signal-strong));
  animation: emptyFloat 3s ease-in-out infinite;
}

@keyframes emptyFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-15rpx); }
}

.empty-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 12rpx;
}

.empty-subtitle {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 30rpx;
}

.empty-action {
  background: linear-gradient(135deg,
    var(--hologram-cyan) 0%,
    var(--hologram-blue) 100%
  );
  border: 2rpx solid var(--hologram-cyan);
  border-radius: 16rpx;
  padding: 16rpx 32rpx;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out-quart);
  box-shadow: 0 0 20rpx rgba(6, 182, 212, 0.3);
  display: inline-block;
}

.empty-action:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 30rpx rgba(6, 182, 212, 0.5);
}

.action-text {
  font-size: 22rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* ==================== 互动功能区域 ==================== */
.interaction-section {
  margin-bottom: 40rpx;
}

.section-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
  text-shadow: 0 0 20rpx var(--signal-strong);
}

.section-subtitle {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}

.interaction-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.interaction-item {
  background: rgba(255, 255, 255, 0.08);
  border: 2rpx solid rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out-quart);
  backdrop-filter: blur(10rpx);
}

.interaction-item:hover {
  background: rgba(255, 255, 255, 0.12);
  transform: translateY(-4rpx);
  box-shadow: var(--shadow-medium);
  border-color: var(--hologram-cyan);
}

.interaction-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  filter: drop-shadow(0 0 12rpx currentColor);
  animation: interactionFloat 4s ease-in-out infinite;
}

.interaction-item:nth-child(1) .interaction-icon { animation-delay: 0s; }
.interaction-item:nth-child(2) .interaction-icon { animation-delay: 1s; }
.interaction-item:nth-child(3) .interaction-icon { animation-delay: 2s; }
.interaction-item:nth-child(4) .interaction-icon { animation-delay: 3s; }

@keyframes interactionFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-8rpx); }
}

.interaction-title {
  display: block;
  font-size: 22rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
}

.interaction-desc {
  display: block;
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
}

/* ==================== 安全提醒 ==================== */
.safety-reminder {
  background: rgba(245, 158, 11, 0.1);
  border: 2rpx solid rgba(245, 158, 11, 0.3);
  border-radius: 16rpx;
  padding: 24rpx;
  backdrop-filter: blur(10rpx);
  margin-bottom: 40rpx;
}

.reminder-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.reminder-icon {
  font-size: 24rpx;
  filter: drop-shadow(0 0 8rpx var(--signal-medium));
}

.reminder-title {
  font-size: 22rpx;
  font-weight: bold;
  color: white;
}

.reminder-text {
  display: block;
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  margin-bottom: 8rpx;
  padding-left: 20rpx;
  position: relative;
}

.reminder-text::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--signal-medium);
  font-weight: bold;
}

.reminder-text:last-child {
  margin-bottom: 0;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 750rpx) {
  .content-scroll {
    padding: 0 20rpx 30rpx;
  }

  .communication-hud {
    padding: 20rpx 30rpx;
    flex-direction: column;
    gap: 16rpx;
    height: auto;
  }

  .hud-left,
  .hud-center,
  .hud-right {
    width: 100%;
    justify-content: center;
  }

  .friend-card {
    padding: 20rpx;
    flex-direction: column;
    text-align: center;
    gap: 16rpx;
  }

  .friend-actions {
    flex-direction: row;
    justify-content: center;
  }

  .interaction-grid {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }

  .category-tabs {
    flex-wrap: wrap;
    gap: 8rpx;
  }

  .tab-item {
    min-width: 80rpx;
  }

  .radar-scanner {
    width: 400rpx;
    height: 400rpx;
  }

  .radar-circle.circle-1 {
    width: 400rpx;
    height: 400rpx;
  }

  .radar-circle.circle-2 {
    width: 280rpx;
    height: 280rpx;
  }

  .radar-circle.circle-3 {
    width: 160rpx;
    height: 160rpx;
  }
}

/* ==================== 大屏幕优化 ==================== */
@media (min-width: 1200rpx) {
  .interaction-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .friends-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx;
  }

  .friend-card {
    flex-direction: column;
    text-align: center;
    height: 300rpx;
  }

  .friend-actions {
    flex-direction: row;
    justify-content: center;
  }
}
