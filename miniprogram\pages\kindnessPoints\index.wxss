/* 善意积分站样式 */

.points-station-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FCE4EC 0%, #F8BBD9 50%, #E91E63 100%);
  position: relative;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 背景装饰 */
.station-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.floating-heart {
  position: absolute;
  font-size: 35rpx;
  opacity: 0.7;
  animation: heartFloat 8s infinite ease-in-out;
}

.heart-1 { top: 10%; left: 15%; animation-delay: 0s; }
.heart-2 { top: 30%; right: 20%; animation-delay: 2s; }
.heart-3 { top: 60%; left: 10%; animation-delay: 4s; }
.heart-4 { top: 80%; right: 15%; animation-delay: 6s; }

@keyframes heartFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-15rpx) rotate(90deg);
    opacity: 0.9;
  }
  50% {
    transform: translateY(-25rpx) rotate(180deg);
    opacity: 1;
  }
  75% {
    transform: translateY(-15rpx) rotate(270deg);
    opacity: 0.9;
  }
}

.energy-wave {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(233, 30, 99, 0.2) 0%, transparent 70%);
  animation: waveExpand 6s infinite ease-out;
}

.wave-1 {
  width: 400rpx;
  height: 400rpx;
  top: 20%;
  left: -100rpx;
  animation-delay: 0s;
}

.wave-2 {
  width: 300rpx;
  height: 300rpx;
  top: 50%;
  right: -80rpx;
  animation-delay: 2s;
}

.wave-3 {
  width: 500rpx;
  height: 500rpx;
  bottom: 20%;
  left: 30%;
  animation-delay: 4s;
}

@keyframes waveExpand {
  0% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

/* 页面标题 */
.station-header {
  position: relative;
  z-index: 10;
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx 20rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 25rpx;
  backdrop-filter: blur(15rpx);
  border: 2rpx solid rgba(233, 30, 99, 0.4);
}

.header-icon {
  font-size: 60rpx;
  margin-bottom: 15rpx;
  animation: headerPulse 3s infinite ease-in-out;
}

@keyframes headerPulse {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 15rpx rgba(233, 30, 99, 0.6));
  }
  50% {
    transform: scale(1.1);
    filter: drop-shadow(0 0 25rpx rgba(233, 30, 99, 0.8));
  }
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #AD1457;
  margin-bottom: 8rpx;
  text-shadow: 1rpx 1rpx 3rpx rgba(173, 20, 87, 0.3);
}

.header-subtitle {
  font-size: 24rpx;
  color: #E91E63;
  opacity: 0.8;
}

/* 余额显示 */
.balance-section {
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

.balance-card {
  position: relative;
  flex: 1;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  backdrop-filter: blur(15rpx);
  border: 2rpx solid rgba(233, 30, 99, 0.3);
  overflow: hidden;
}

.balance-icon {
  font-size: 50rpx;
  margin-bottom: 15rpx;
  animation: balanceFloat 4s infinite ease-in-out;
}

@keyframes balanceFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8rpx);
  }
}

.balance-amount {
  font-size: 36rpx;
  font-weight: bold;
  color: #AD1457;
  margin-bottom: 8rpx;
  text-shadow: 1rpx 1rpx 3rpx rgba(173, 20, 87, 0.3);
}

.balance-label {
  font-size: 22rpx;
  color: #E91E63;
  opacity: 0.8;
}

.balance-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80%;
  height: 80%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  opacity: 0.3;
  animation: balanceGlow 4s infinite ease-in-out;
}

.energy-glow {
  background: radial-gradient(circle, rgba(255, 20, 147, 0.4) 0%, transparent 70%);
}

.points-glow {
  background: radial-gradient(circle, rgba(255, 193, 7, 0.4) 0%, transparent 70%);
}

@keyframes balanceGlow {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

.exchange-arrow {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 20rpx;
}

.arrow-icon {
  font-size: 40rpx;
  color: #E91E63;
  animation: arrowPulse 2s infinite ease-in-out;
}

@keyframes arrowPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

.arrow-text {
  font-size: 20rpx;
  color: #AD1457;
  margin-top: 5rpx;
}

/* 快速兑换 */
.quick-exchange-section {
  position: relative;
  z-index: 10;
  margin-bottom: 40rpx;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 25rpx;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #AD1457;
  flex: 1;
}

.view-all-btn {
  font-size: 24rpx;
  color: #E91E63;
  opacity: 0.8;
}

.exchange-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

.exchange-option {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 15rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(233, 30, 99, 0.3);
  transition: all 0.3s ease;
  overflow: hidden;
}

.exchange-option:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.4);
}

.option-energy {
  font-size: 24rpx;
  color: #FF1493;
  font-weight: bold;
}

.option-arrow {
  font-size: 20rpx;
  color: #E91E63;
  margin: 0 10rpx;
}

.option-points {
  font-size: 24rpx;
  color: #FFC107;
  font-weight: bold;
}

.option-rate {
  position: absolute;
  bottom: 5rpx;
  right: 10rpx;
  font-size: 16rpx;
  color: #AD1457;
  opacity: 0.7;
}

.option-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
  background: radial-gradient(circle, rgba(233, 30, 99, 0.2) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.exchange-option:active .option-glow {
  opacity: 1;
  animation: optionGlow 0.6s ease-out;
}

@keyframes optionGlow {
  0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 0.5;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0;
  }
}

/* 积分商店 */
.points-shop-section {
  position: relative;
  z-index: 10;
  margin-bottom: 40rpx;
}

.shop-categories {
  display: flex;
  gap: 15rpx;
  margin-bottom: 25rpx;
  overflow-x: auto;
  padding-bottom: 10rpx;
}

.shop-category {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80rpx;
  padding: 15rpx 10rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 15rpx;
  border: 2rpx solid rgba(233, 30, 99, 0.3);
  transition: all 0.3s ease;
}

.shop-category.active {
  background: rgba(233, 30, 99, 0.4);
  border-color: #E91E63;
  transform: scale(1.05);
}

.category-icon {
  font-size: 30rpx;
  margin-bottom: 5rpx;
}

.category-name {
  font-size: 20rpx;
  color: #AD1457;
  text-align: center;
}

.shop-items {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.shop-item {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 15rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(233, 30, 99, 0.3);
  transition: all 0.3s ease;
  overflow: hidden;
}

.shop-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.4);
}

.item-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #AD1457;
  margin-bottom: 5rpx;
}

.item-description {
  font-size: 20rpx;
  color: #E91E63;
  opacity: 0.8;
  margin-bottom: 8rpx;
}

.item-price {
  font-size: 22rpx;
  color: #FFC107;
  font-weight: bold;
}

.item-status {
  display: flex;
  align-items: center;
}

.purchase-btn {
  background: linear-gradient(135deg, #E91E63, #AD1457);
  color: white;
  font-size: 22rpx;
  padding: 8rpx 20rpx;
  border-radius: 15rpx;
  font-weight: bold;
}

.owned-badge {
  background: rgba(76, 175, 80, 0.8);
  color: white;
  font-size: 20rpx;
  padding: 6rpx 15rpx;
  border-radius: 12rpx;
}

.item-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
  background: radial-gradient(circle, rgba(233, 30, 99, 0.2) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.shop-item:active .item-glow {
  opacity: 1;
}

/* 积分任务 */
.points-tasks-section {
  position: relative;
  z-index: 10;
  margin-bottom: 40rpx;
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.task-item {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 15rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(233, 30, 99, 0.3);
  transition: all 0.3s ease;
}

.task-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.4);
}

.task-icon {
  font-size: 35rpx;
  margin-right: 20rpx;
}

.task-info {
  flex: 1;
  margin-right: 15rpx;
}

.task-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #AD1457;
  margin-bottom: 5rpx;
}

.task-description {
  font-size: 20rpx;
  color: #E91E63;
  opacity: 0.8;
  margin-bottom: 8rpx;
}

.task-reward {
  font-size: 20rpx;
  color: #FFC107;
  font-weight: bold;
}

.task-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 15rpx;
}

.progress-bar {
  width: 80rpx;
  height: 8rpx;
  background: rgba(233, 30, 99, 0.3);
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 5rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #E91E63, #FFC107);
  border-radius: 4rpx;
  transition: width 0.5s ease;
}

.progress-text {
  font-size: 16rpx;
  color: #AD1457;
}

.task-status {
  display: flex;
  align-items: center;
}

.claim-btn {
  background: linear-gradient(135deg, #FFC107, #FF8F00);
  color: white;
  font-size: 20rpx;
  padding: 6rpx 15rpx;
  border-radius: 12rpx;
  font-weight: bold;
}

.completed-badge {
  background: rgba(76, 175, 80, 0.8);
  color: white;
  font-size: 18rpx;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
}

.progress-badge {
  background: rgba(233, 30, 99, 0.6);
  color: white;
  font-size: 18rpx;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
}

/* 积分历史 */
.points-history-section {
  position: relative;
  z-index: 10;
  margin-bottom: 40rpx;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.history-item {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 12rpx;
  padding: 15rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(233, 30, 99, 0.3);
}

.history-icon {
  font-size: 30rpx;
  margin-right: 15rpx;
}

.history-info {
  flex: 1;
}

.history-title {
  font-size: 24rpx;
  color: #AD1457;
  margin-bottom: 3rpx;
}

.history-time {
  font-size: 18rpx;
  color: #E91E63;
  opacity: 0.7;
}

.history-amount {
  font-size: 24rpx;
  font-weight: bold;
}

.history-amount.earn {
  color: #4CAF50;
}

.history-amount.spend {
  color: #FF5722;
}

.no-history {
  text-align: center;
  padding: 40rpx 20rpx;
}

.no-history-icon {
  font-size: 60rpx;
  margin-bottom: 15rpx;
  opacity: 0.6;
}

.no-history-text {
  font-size: 26rpx;
  color: #AD1457;
  margin-bottom: 8rpx;
}

.no-history-tip {
  font-size: 20rpx;
  color: #E91E63;
  opacity: 0.7;
}

/* 积分统计 */
.points-stats-section {
  position: relative;
  z-index: 10;
  margin-bottom: 40rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

.stat-card {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 15rpx;
  padding: 25rpx 20rpx;
  text-align: center;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(233, 30, 99, 0.3);
}

.stat-icon {
  font-size: 40rpx;
  margin-bottom: 10rpx;
}

.stat-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #AD1457;
  margin-bottom: 5rpx;
}

.stat-label {
  font-size: 20rpx;
  color: #E91E63;
  opacity: 0.8;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(252, 228, 236, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5rpx);
}

.loading-spinner {
  text-align: center;
}

.spinner-icon {
  font-size: 60rpx;
  animation: spinnerRotate 1.5s infinite ease-in-out;
}

@keyframes spinnerRotate {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.2);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

.loading-text {
  font-size: 24rpx;
  color: #AD1457;
  margin-top: 15rpx;
  font-weight: bold;
}

/* 兑换成功动画 */
.exchange-success-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(252, 228, 236, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  backdrop-filter: blur(10rpx);
}

.success-content {
  text-align: center;
  position: relative;
}

.success-icon {
  font-size: 100rpx;
  margin-bottom: 25rpx;
  animation: successBounce 0.8s ease-out;
}

@keyframes successBounce {
  0% {
    transform: scale(0) rotate(0deg);
  }
  50% {
    transform: scale(1.3) rotate(180deg);
  }
  100% {
    transform: scale(1) rotate(360deg);
  }
}

.success-message {
  font-size: 32rpx;
  font-weight: bold;
  color: #AD1457;
  margin-bottom: 30rpx;
  text-shadow: 1rpx 1rpx 3rpx rgba(173, 20, 87, 0.3);
}

.success-hearts {
  position: absolute;
  top: -50rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 300rpx;
  height: 300rpx;
}

.success-heart {
  position: absolute;
  font-size: 35rpx;
  animation: successHeartFloat 2s ease-out forwards;
}

.success-heart.heart-1 {
  top: 50%;
  left: 50%;
  animation-delay: 0.2s;
}

.success-heart.heart-2 {
  top: 30%;
  left: 30%;
  animation-delay: 0.4s;
}

.success-heart.heart-3 {
  top: 30%;
  right: 30%;
  animation-delay: 0.6s;
}

.success-heart.heart-4 {
  bottom: 30%;
  left: 30%;
  animation-delay: 0.8s;
}

.success-heart.heart-5 {
  bottom: 30%;
  right: 30%;
  animation-delay: 1s;
}

@keyframes successHeartFloat {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: translate(-50%, -100rpx) scale(0.8);
    opacity: 0;
  }
}
