// pages/exploration/cognitiveTraining/game/questionData.js

/**
 * 认知训练题目数据
 */

// 语言词汇训练题目
const languageQuestions = [
  {
    id: 'lang_001',
    type: 'multiple_choice',
    question: '下面哪个词语的意思是"非常高兴"？',
    options: ['伤心', '愤怒', '欣喜若狂', '害怕'],
    correctAnswer: 2,
    explanation: '"欣喜若狂"表示非常高兴的意思',
    feedback: {
      correct: '太棒了！你理解了词语的含义！',
      wrong: '再想想，哪个词表示很高兴呢？'
    }
  },
  {
    id: 'lang_002',
    type: 'text_input',
    question: '请写出"大"的反义词：',
    correctAnswer: ['小', '微', '细'],
    explanation: '"大"的反义词是"小"',
    feedback: {
      correct: '正确！你掌握了反义词！',
      wrong: '想想什么和"大"相反呢？'
    }
  },
  {
    id: 'lang_003',
    type: 'multiple_choice',
    question: '"春天来了，花儿开了。"这句话描述的是什么季节？',
    options: ['夏天', '秋天', '冬天', '春天'],
    correctAnswer: 3,
    explanation: '句子中明确提到了"春天来了"',
    feedback: {
      correct: '很好！你能理解句子的意思！',
      wrong: '仔细读读句子，里面有答案哦！'
    }
  },
  {
    id: 'lang_004',
    type: 'text_input',
    question: '用"快乐"造一个句子：',
    correctAnswer: ['快乐', '高兴', '开心'],
    explanation: '只要句子中包含"快乐"并且语法正确即可',
    feedback: {
      correct: '造句很棒！你会使用这个词语了！',
      wrong: '记得要用"快乐"这个词来造句哦！'
    }
  },
  {
    id: 'lang_005',
    type: 'multiple_choice',
    question: '下面哪组词语都是表示颜色的？',
    options: ['红色、蓝色、绿色', '苹果、香蕉、橙子', '跑步、游泳、跳跃', '高兴、伤心、愤怒'],
    correctAnswer: 0,
    explanation: '红色、蓝色、绿色都是颜色词',
    feedback: {
      correct: '太好了！你能分类词语！',
      wrong: '想想哪一组都是颜色呢？'
    }
  },
  {
    id: 'lang_006',
    type: 'multiple_choice',
    question: '"小明跑得很快"中，"快"是什么词性？',
    options: ['名词', '动词', '形容词', '副词'],
    correctAnswer: 3,
    explanation: '"快"在这里修饰动词"跑"，是副词',
    feedback: {
      correct: '厉害！你懂得词性分析！',
      wrong: '想想"快"是用来形容"跑"的程度的'
    }
  },
  {
    id: 'lang_007',
    type: 'text_input',
    question: '"美丽"的近义词是什么？',
    correctAnswer: ['漂亮', '好看', '美好', '秀丽'],
    explanation: '近义词是意思相近的词语',
    feedback: {
      correct: '很棒！你理解了近义词！',
      wrong: '想想什么词和"美丽"意思差不多？'
    }
  },
  {
    id: 'lang_008',
    type: 'multiple_choice',
    question: '下面哪个句子是疑问句？',
    options: ['今天天气真好！', '你吃饭了吗？', '我要去上学。', '快点过来。'],
    correctAnswer: 1,
    explanation: '疑问句是用来提问的句子，通常以"吗"、"呢"等结尾',
    feedback: {
      correct: '正确！你认识了疑问句！',
      wrong: '找找哪个句子是在问问题的？'
    }
  },
  {
    id: 'lang_009',
    type: 'text_input',
    question: '请写出一个表示动作的词语：',
    correctAnswer: ['跑', '走', '跳', '飞', '游', '爬', '坐', '站', '躺', '睡'],
    explanation: '动作词表示人或物的行为动作',
    feedback: {
      correct: '很好！你知道什么是动作词！',
      wrong: '想想人可以做什么动作？'
    }
  },
  {
    id: 'lang_010',
    type: 'multiple_choice',
    question: '"一群"可以用来形容下面哪个？',
    options: ['一群水', '一群石头', '一群小鸟', '一群空气'],
    correctAnswer: 2,
    explanation: '"一群"通常用来形容有生命的、会聚集的事物',
    feedback: {
      correct: '太棒了！你理解了量词的用法！',
      wrong: '想想"一群"通常用来形容什么？'
    }
  }
];

// 专注力训练题目
const attentionQuestions = [
  {
    id: 'att_001',
    type: 'multiple_choice',
    question: '在下面的数字中，找出不同的那一个：2 2 2 3 2',
    options: ['第1个', '第2个', '第3个', '第4个'],
    correctAnswer: 3,
    explanation: '第4个数字是3，其他都是2',
    feedback: {
      correct: '专注力很棒！你找到了不同！',
      wrong: '仔细看看，哪个数字和其他不一样？'
    }
  },
  {
    id: 'att_002',
    type: 'text_input',
    question: '请按顺序记住这些数字，然后写出来：5 8 2 9',
    correctAnswer: ['5829', '5 8 2 9'],
    explanation: '需要按照原来的顺序记住数字',
    feedback: {
      correct: '记忆力真好！顺序完全正确！',
      wrong: '再仔细看看数字的顺序哦！'
    }
  },
  {
    id: 'att_003',
    type: 'multiple_choice',
    question: '下面哪个图形组合是对称的？',
    options: ['○△○', '△○△', '○○△', '△△○'],
    correctAnswer: 1,
    explanation: '△○△是左右对称的',
    feedback: {
      correct: '观察力很敏锐！你发现了对称！',
      wrong: '想想哪个组合左右两边是一样的？'
    }
  },
  {
    id: 'att_004',
    type: 'multiple_choice',
    question: '在这串字母中有几个"A"？ A B A C A D A',
    options: ['2个', '3个', '4个', '5个'],
    correctAnswer: 2,
    explanation: '仔细数一数，共有4个A',
    feedback: {
      correct: '计数能力很强！注意力集中！',
      wrong: '再仔细数一遍，不要漏掉哦！'
    }
  },
  {
    id: 'att_005',
    type: 'text_input',
    question: '倒着写出这个词语：学习',
    correctAnswer: ['习学'],
    explanation: '把"学习"倒过来写就是"习学"',
    feedback: {
      correct: '思维很灵活！你会倒序思考！',
      wrong: '想想把字的顺序颠倒过来怎么写？'
    }
  },
  {
    id: 'att_006',
    type: 'multiple_choice',
    question: '下面的序列中，下一个应该是什么？红 蓝 红 蓝 红 ？',
    options: ['红', '蓝', '绿', '黄'],
    correctAnswer: 1,
    explanation: '这是红蓝交替的规律，下一个应该是蓝',
    feedback: {
      correct: '规律发现能力很强！',
      wrong: '看看前面的颜色是怎么排列的？'
    }
  },
  {
    id: 'att_007',
    type: 'multiple_choice',
    question: '在这些形状中，圆形有几个？○ △ ○ □ ○ △',
    options: ['1个', '2个', '3个', '4个'],
    correctAnswer: 2,
    explanation: '仔细数一数，有3个圆形',
    feedback: {
      correct: '观察仔细！计数准确！',
      wrong: '再看看，圆形○有几个？'
    }
  },
  {
    id: 'att_008',
    type: 'text_input',
    question: '请写出这个数字序列的下一个数：2 4 6 8 ?',
    correctAnswer: ['10'],
    explanation: '这是偶数序列，每次加2',
    feedback: {
      correct: '数学规律掌握得很好！',
      wrong: '看看这些数字之间有什么规律？'
    }
  },
  {
    id: 'att_009',
    type: 'multiple_choice',
    question: '下面哪个选项和其他三个不同？',
    options: ['苹果', '香蕉', '橙子', '萝卜'],
    correctAnswer: 3,
    explanation: '萝卜是蔬菜，其他都是水果',
    feedback: {
      correct: '分类能力很强！你能找出不同！',
      wrong: '想想哪个不属于同一类？'
    }
  },
  {
    id: 'att_010',
    type: 'multiple_choice',
    question: '如果按照大小排列，哪个顺序是正确的？',
    options: ['大象 老鼠 猫', '老鼠 猫 大象', '猫 大象 老鼠', '大象 猫 老鼠'],
    correctAnswer: 1,
    explanation: '按从小到大排列：老鼠 < 猫 < 大象',
    feedback: {
      correct: '排序能力很棒！逻辑清晰！',
      wrong: '想想这些动物的大小关系？'
    }
  }
];

// 视觉训练题目
const visualQuestions = [
  {
    id: 'vis_001',
    type: 'multiple_choice',
    question: '下面哪个图形可以拼成正方形？',
    options: ['两个三角形', '四个小正方形', '一个圆形', '三个长方形'],
    correctAnswer: 1,
    explanation: '四个小正方形可以拼成一个大正方形',
    feedback: {
      correct: '空间想象力很好！',
      wrong: '想想什么形状能组成正方形？'
    }
  },
  {
    id: 'vis_002',
    type: 'multiple_choice',
    question: '从正面看一个立方体，你能看到几个面？',
    options: ['1个', '2个', '3个', '4个'],
    correctAnswer: 2,
    explanation: '从正面看立方体，最多能看到3个面',
    feedback: {
      correct: '立体思维很强！',
      wrong: '想象一下立方体的样子，能看到几个面？'
    }
  },
  {
    id: 'vis_003',
    type: 'multiple_choice',
    question: '下面哪个是轴对称图形？',
    options: ['L形', '正方形', 'Z形', 'F形'],
    correctAnswer: 1,
    explanation: '正方形有多条对称轴，是轴对称图形',
    feedback: {
      correct: '几何知识掌握得很好！',
      wrong: '想想哪个图形可以对折重合？'
    }
  },
  {
    id: 'vis_004',
    type: 'text_input',
    question: '一个三角形有几个角？',
    correctAnswer: ['3', '三', '3个', '三个'],
    explanation: '三角形有三个角',
    feedback: {
      correct: '基础几何知识很扎实！',
      wrong: '想想三角形的名字里有提示哦！'
    }
  },
  {
    id: 'vis_005',
    type: 'multiple_choice',
    question: '如果把一张纸对折再对折，展开后会有几条折痕？',
    options: ['1条', '2条', '3条', '4条'],
    correctAnswer: 2,
    explanation: '对折两次会产生3条折痕',
    feedback: {
      correct: '动手能力和想象力都很棒！',
      wrong: '想象一下对折的过程，会产生几条线？'
    }
  },
  {
    id: 'vis_006',
    type: 'multiple_choice',
    question: '下面哪个图形的周长最长？（假设边长都相等）',
    options: ['正方形', '正三角形', '正六边形', '圆形'],
    correctAnswer: 2,
    explanation: '边长相等时，正六边形的周长最长',
    feedback: {
      correct: '几何比较能力很强！',
      wrong: '想想哪个图形的边最多？'
    }
  },
  {
    id: 'vis_007',
    type: 'multiple_choice',
    question: '从侧面看一个圆柱体，看到的形状是什么？',
    options: ['圆形', '正方形', '长方形', '三角形'],
    correctAnswer: 2,
    explanation: '从侧面看圆柱体是长方形',
    feedback: {
      correct: '立体几何理解得很好！',
      wrong: '想象一下圆柱体从侧面看是什么样？'
    }
  },
  {
    id: 'vis_008',
    type: 'text_input',
    question: '一个正方形有几条对称轴？',
    correctAnswer: ['4', '四', '4条', '四条'],
    explanation: '正方形有4条对称轴',
    feedback: {
      correct: '对称性理解得很透彻！',
      wrong: '想想正方形可以通过几种方式对折重合？'
    }
  },
  {
    id: 'vis_009',
    type: 'multiple_choice',
    question: '下面哪个图形旋转180度后和原来一样？',
    options: ['字母P', '字母S', '字母F', '字母L'],
    correctAnswer: 1,
    explanation: '字母S旋转180度后形状不变',
    feedback: {
      correct: '旋转对称理解得很好！',
      wrong: '想想哪个字母转半圈后还是原来的样子？'
    }
  },
  {
    id: 'vis_010',
    type: 'multiple_choice',
    question: '把一个圆形纸片对折，折痕是什么形状？',
    options: ['直线', '曲线', '圆形', '半圆形'],
    correctAnswer: 0,
    explanation: '圆形对折的折痕是一条直线（直径）',
    feedback: {
      correct: '几何直觉很准确！',
      wrong: '想想对折时产生的折痕是什么样的？'
    }
  }
];

// 听觉训练题目（基于现有的故事理解扩展）
const auditoryQuestions = [
  {
    id: 'aud_001',
    type: 'multiple_choice',
    question: '听到"滴答滴答"的声音，你觉得是什么？',
    options: ['下雨声', '时钟声', '脚步声', '心跳声'],
    correctAnswer: 1,
    explanation: '"滴答滴答"通常是时钟的声音',
    feedback: {
      correct: '听觉识别能力很强！',
      wrong: '想想什么东西会发出"滴答滴答"的声音？'
    }
  },
  {
    id: 'aud_002',
    type: 'multiple_choice',
    question: '下面哪个词语的音调是上升的？',
    options: ['妈妈', '爸爸', '哥哥', '姐姐'],
    correctAnswer: 0,
    explanation: '"妈妈"的音调是上升的（第一声到第一声）',
    feedback: {
      correct: '音调感知很敏锐！',
      wrong: '仔细听听这些词的音调变化？'
    }
  },
  {
    id: 'aud_003',
    type: 'text_input',
    question: '请写出和"猫"发音相似的字：',
    correctAnswer: ['毛', '帽', '冒', '茂'],
    explanation: '这些字和"猫"的发音相似',
    feedback: {
      correct: '语音敏感度很高！',
      wrong: '想想哪些字听起来和"猫"差不多？'
    }
  },
  {
    id: 'aud_004',
    type: 'multiple_choice',
    question: '下面哪组词语押韵？',
    options: ['花朵、树叶', '太阳、月亮', '小猫、小鸟', '白云、蓝天'],
    correctAnswer: 1,
    explanation: '"太阳"和"月亮"都以"ang"音结尾，押韵',
    feedback: {
      correct: '韵律感很好！',
      wrong: '听听哪组词的结尾音相似？'
    }
  },
  {
    id: 'aud_005',
    type: 'multiple_choice',
    question: '如果按照音节数量排列，哪个顺序是正确的？',
    options: ['猫 蝴蝶 大象', '大象 蝴蝶 猫', '蝴蝶 大象 猫', '猫 大象 蝴蝶'],
    correctAnswer: 3,
    explanation: '按音节数：猫(1) < 大象(2) < 蝴蝶(3)',
    feedback: {
      correct: '音节分析能力很强！',
      wrong: '数数每个词有几个音节？'
    }
  },
  {
    id: 'aud_006',
    type: 'text_input',
    question: '"小鸟"的第一个字是什么声母？',
    correctAnswer: ['x', 'X'],
    explanation: '"小"字的声母是x',
    feedback: {
      correct: '拼音基础很扎实！',
      wrong: '想想"小"字怎么拼读？'
    }
  },
  {
    id: 'aud_007',
    type: 'multiple_choice',
    question: '下面哪个是叠词？',
    options: ['高兴', '慢慢', '美丽', '快乐'],
    correctAnswer: 1,
    explanation: '"慢慢"是叠词，由相同的字重复组成',
    feedback: {
      correct: '语言结构理解得很好！',
      wrong: '找找哪个词是由相同字组成的？'
    }
  },
  {
    id: 'aud_008',
    type: 'multiple_choice',
    question: '听到"咚咚咚"的声音，最可能是什么？',
    options: ['敲门声', '下雨声', '风声', '鸟叫声'],
    correctAnswer: 0,
    explanation: '"咚咚咚"通常是敲门的声音',
    feedback: {
      correct: '声音联想能力很棒！',
      wrong: '想想什么情况下会听到"咚咚咚"？'
    }
  },
  {
    id: 'aud_009',
    type: 'text_input',
    question: '请写出一个以"sh"开头的词语：',
    correctAnswer: ['书', '树', '水', '手', '山', '上', '十', '是', '谁', '什么'],
    explanation: '这些词都以"sh"音开头',
    feedback: {
      correct: '拼音掌握得很好！',
      wrong: '想想哪些字的拼音以"sh"开头？'
    }
  },
  {
    id: 'aud_010',
    type: 'multiple_choice',
    question: '下面哪个词语的重音在第二个字上？',
    options: ['老师', '学生', '朋友', '同学'],
    correctAnswer: 0,
    explanation: '"老师"的重音通常在"师"字上',
    feedback: {
      correct: '语音重音感知很准确！',
      wrong: '仔细听听这些词，哪个字读得更重？'
    }
  }
];

/**
 * 获取指定类型的题目
 */
function getQuestions(type) {
  switch (type) {
    case 'language':
      return languageQuestions;
    case 'attention':
      return attentionQuestions;
    case 'visual':
      return visualQuestions;
    case 'auditory':
      return auditoryQuestions;
    default:
      return [];
  }
}

/**
 * 获取所有题目类型
 */
function getAllTypes() {
  return ['language', 'attention', 'visual', 'auditory'];
}

/**
 * 获取题目统计信息
 */
function getQuestionStats() {
  return {
    language: languageQuestions.length,
    attention: attentionQuestions.length,
    visual: visualQuestions.length,
    auditory: auditoryQuestions.length,
    total: languageQuestions.length + attentionQuestions.length + 
           visualQuestions.length + auditoryQuestions.length
  };
}

module.exports = {
  getQuestions,
  getAllTypes,
  getQuestionStats,
  languageQuestions,
  attentionQuestions,
  visualQuestions,
  auditoryQuestions
};
