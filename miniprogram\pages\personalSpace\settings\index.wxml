<!-- 《能量星球》个人设置 - 控制台主题 -->
<view class="page">
  <view class="settings-console">
    
    <!-- 控制台背景 -->
    <view class="console-background">
      <view class="circuit-lines">
        <view class="circuit-line line-1"></view>
        <view class="circuit-line line-2"></view>
        <view class="circuit-line line-3"></view>
        <view class="circuit-line line-4"></view>
      </view>
      
      <view class="data-nodes">
        <view class="data-node node-1"></view>
        <view class="data-node node-2"></view>
        <view class="data-node node-3"></view>
        <view class="data-node node-4"></view>
      </view>
    </view>

    <!-- 设置HUD -->
    <view class="settings-hud">
      <view class="hud-title">
        <text class="title-icon">⚙️</text>
        <text class="title-text">系统控制台</text>
      </view>
      <view class="hud-status">
        <view class="status-indicator active"></view>
        <text class="status-text">系统运行正常</text>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <scroll-view class="content-scroll" scroll-y="true">
      
      <!-- 主题设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-icon">🎨</text>
          <text class="section-title">主题设置</text>
        </view>
        
        <view class="setting-item">
          <view class="item-info">
            <text class="item-title">界面主题</text>
            <text class="item-description">选择你喜欢的界面风格</text>
          </view>
          <view class="theme-selector">
            <view class="theme-option {{currentTheme === 'space' ? 'selected' : ''}}" 
                  bindtap="onSelectTheme" data-theme="space">
              <view class="theme-preview space"></view>
              <text class="theme-name">深空蓝</text>
            </view>
            <view class="theme-option {{currentTheme === 'nebula' ? 'selected' : ''}}" 
                  bindtap="onSelectTheme" data-theme="nebula">
              <view class="theme-preview nebula"></view>
              <text class="theme-name">星云紫</text>
            </view>
            <view class="theme-option {{currentTheme === 'energy' ? 'selected' : ''}}" 
                  bindtap="onSelectTheme" data-theme="energy">
              <view class="theme-preview energy"></view>
              <text class="theme-name">能量金</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 通知设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-icon">🔔</text>
          <text class="section-title">通知设置</text>
        </view>
        
        <view class="setting-item">
          <view class="item-info">
            <text class="item-title">成就通知</text>
            <text class="item-description">获得新成就时推送通知</text>
          </view>
          <switch class="setting-switch" checked="{{notifications.achievement}}" 
                  bindchange="onNotificationChange" data-type="achievement"/>
        </view>
        
        <view class="setting-item">
          <view class="item-info">
            <text class="item-title">好友互动</text>
            <text class="item-description">好友点赞或留言时通知</text>
          </view>
          <switch class="setting-switch" checked="{{notifications.friend}}" 
                  bindchange="onNotificationChange" data-type="friend"/>
        </view>
        
        <view class="setting-item">
          <view class="item-info">
            <text class="item-title">家长消息</text>
            <text class="item-description">收到家长消息时通知</text>
          </view>
          <switch class="setting-switch" checked="{{notifications.parent}}" 
                  bindchange="onNotificationChange" data-type="parent"/>
        </view>
        
        <view class="setting-item">
          <view class="item-info">
            <text class="item-title">系统更新</text>
            <text class="item-description">应用更新和维护通知</text>
          </view>
          <switch class="setting-switch" checked="{{notifications.system}}" 
                  bindchange="onNotificationChange" data-type="system"/>
        </view>
      </view>

      <!-- 隐私设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-icon">🔒</text>
          <text class="section-title">隐私设置</text>
        </view>
        
        <view class="setting-item">
          <view class="item-info">
            <text class="item-title">个人信息可见性</text>
            <text class="item-description">控制他人可以看到的信息</text>
          </view>
          <picker mode="selector" range="{{privacyLevels}}" value="{{privacyIndex}}" 
                  bindchange="onPrivacyChange">
            <view class="picker-display">
              <text class="picker-text">{{privacyLevels[privacyIndex]}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
        </view>
        
        <view class="setting-item">
          <view class="item-info">
            <text class="item-title">舱室访问权限</text>
            <text class="item-description">谁可以访问你的个人舱室</text>
          </view>
          <switch class="setting-switch" checked="{{privacy.roomAccess}}" 
                  bindchange="onPrivacyToggle" data-type="roomAccess"/>
        </view>
        
        <view class="setting-item">
          <view class="item-info">
            <text class="item-title">成就展示</text>
            <text class="item-description">是否公开展示个人成就</text>
          </view>
          <switch class="setting-switch" checked="{{privacy.achievements}}" 
                  bindchange="onPrivacyToggle" data-type="achievements"/>
        </view>
        
        <view class="setting-item">
          <view class="item-info">
            <text class="item-title">在线状态</text>
            <text class="item-description">是否显示在线状态给好友</text>
          </view>
          <switch class="setting-switch" checked="{{privacy.onlineStatus}}" 
                  bindchange="onPrivacyToggle" data-type="onlineStatus"/>
        </view>
      </view>

      <!-- 安全设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-icon">🛡️</text>
          <text class="section-title">安全设置</text>
        </view>
        
        <view class="setting-item clickable" bindtap="onChangePassword">
          <view class="item-info">
            <text class="item-title">修改密码</text>
            <text class="item-description">更改登录密码</text>
          </view>
          <text class="item-arrow">></text>
        </view>
        
        <view class="setting-item clickable" bindtap="onParentControl">
          <view class="item-info">
            <text class="item-title">家长控制</text>
            <text class="item-description">家长监护和权限设置</text>
          </view>
          <text class="item-arrow">></text>
        </view>
        
        <view class="setting-item clickable" bindtap="onDeviceManagement">
          <view class="item-info">
            <text class="item-title">设备管理</text>
            <text class="item-description">查看和管理登录设备</text>
          </view>
          <text class="item-arrow">></text>
        </view>
      </view>

      <!-- 数据管理 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-icon">💾</text>
          <text class="section-title">数据管理</text>
        </view>
        
        <view class="setting-item clickable" bindtap="onExportData">
          <view class="item-info">
            <text class="item-title">数据导出</text>
            <text class="item-description">导出个人数据和成就记录</text>
          </view>
          <text class="item-arrow">></text>
        </view>
        
        <view class="setting-item clickable" bindtap="onClearCache">
          <view class="item-info">
            <text class="item-title">清理缓存</text>
            <text class="item-description">清理应用缓存数据</text>
          </view>
          <view class="cache-size">
            <text class="size-text">{{cacheSize}}</text>
          </view>
        </view>
        
        <view class="setting-item clickable" bindtap="onBackupData">
          <view class="item-info">
            <text class="item-title">数据备份</text>
            <text class="item-description">备份重要数据到云端</text>
          </view>
          <text class="item-arrow">></text>
        </view>
      </view>

      <!-- 关于 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-icon">ℹ️</text>
          <text class="section-title">关于</text>
        </view>
        
        <view class="setting-item clickable" bindtap="onVersionInfo">
          <view class="item-info">
            <text class="item-title">版本信息</text>
            <text class="item-description">当前版本 {{appVersion}}</text>
          </view>
          <text class="item-arrow">></text>
        </view>
        
        <view class="setting-item clickable" bindtap="onHelp">
          <view class="item-info">
            <text class="item-title">使用帮助</text>
            <text class="item-description">查看使用指南和常见问题</text>
          </view>
          <text class="item-arrow">></text>
        </view>
        
        <view class="setting-item clickable" bindtap="onContact">
          <view class="item-info">
            <text class="item-title">联系客服</text>
            <text class="item-description">获取技术支持和帮助</text>
          </view>
          <text class="item-arrow">></text>
        </view>
        
        <view class="setting-item clickable" bindtap="onUserAgreement">
          <view class="item-info">
            <text class="item-title">用户协议</text>
            <text class="item-description">查看服务条款和隐私政策</text>
          </view>
          <text class="item-arrow">></text>
        </view>
      </view>

      <!-- 危险操作 -->
      <view class="settings-section danger">
        <view class="setting-item clickable" bindtap="onAccountDeletion">
          <view class="item-info">
            <text class="item-title danger-text">注销账户</text>
            <text class="item-description">永久删除账户和所有数据</text>
          </view>
          <text class="item-arrow danger-text">></text>
        </view>
      </view>

    </scroll-view>

  </view>
</view>
