// 《能量星球》宇宙灯塔 - 慈善系统主逻辑
const { charitySystem } = require('../../utils/charitySystem');

Page({
  data: {
    // 用户状态
    currentLoveEnergy: 0,
    userStats: {
      totalDonations: 0,
      totalContribution: 0,
      loveLevel: { level: 1, name: '爱心萌芽', icon: '🌱', next: 50 },
      badgeCount: 0,
      favoriteCategory: null
    },
    loveLevelProgress: 0,

    // 季节主题
    seasonalTheme: null,

    // 善意数据
    kindnessActions: [],
    kindnessPoints: 0,
    kindnessCount: 0,

    // 页面状态
    loading: false,
    celebrating: false,
    celebrationMessage: ''
  },

  onLoad: function (options) {
    console.log('宇宙灯塔加载');
    this.initializeKindness();
  },

  onReady: function () {
    console.log('宇宙灯塔渲染完成');
  },

  onShow: function () {
    // 每次显示时静默刷新数据，避免显示loading动画
    this.silentRefreshKindnessData();
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    this.refreshKindnessData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  // 初始化善意系统
  initializeKindness() {
    this.setData({ loading: true });

    try {
      // 加载用户数据
      this.loadUserData();

      // 加载季节主题
      this.loadSeasonalTheme();

      // 加载善意数据
      this.loadKindnessData();

      // 加载用户统计
      this.loadUserStats();

    } catch (error) {
      console.error('初始化善意系统失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载用户数据
  loadUserData() {
    const userData = wx.getStorageSync('userData') || {};
    this.setData({
      currentLoveEnergy: userData.loveEnergy || 0
    });
  },

  // 加载季节主题
  loadSeasonalTheme() {
    const currentSeason = this.getCurrentSeason();
    const seasonalThemes = {
      spring: { name: '春日善意', description: '万物复苏，传播善意', color: '#4CAF50' },
      summer: { name: '夏日关爱', description: '炎热夏日，关爱他人', color: '#FF9800' },
      autumn: { name: '秋日感恩', description: '收获季节，感恩分享', color: '#FF5722' },
      winter: { name: '冬日温暖', description: '寒冷冬天，温暖人心', color: '#E91E63' }
    };

    this.setData({
      seasonalTheme: seasonalThemes[currentSeason]
    });
  },

  // 获取当前季节
  getCurrentSeason() {
    const month = new Date().getMonth() + 1;
    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    if (month >= 9 && month <= 11) return 'autumn';
    return 'winter';
  },

  // 加载善意数据
  loadKindnessData() {
    const kindnessPoints = wx.getStorageSync('kindnessPoints') || 0;
    const kindnessCount = wx.getStorageSync('kindnessCount') || 0;

    this.setData({
      kindnessPoints,
      kindnessCount
    });
  },

  // 加载用户统计
  loadUserStats() {
    const kindnessCount = wx.getStorageSync('kindnessCount') || 0;
    const treeLevel = this.calculateTreeLevel(kindnessCount);

    // 计算等级进度
    let loveLevelProgress = 0;
    if (treeLevel.next) {
      loveLevelProgress = (kindnessCount / treeLevel.next) * 100;
    }

    this.setData({
      userStats: {
        loveLevel: treeLevel,
        totalKindness: kindnessCount,
        badgeCount: this.getBadgeCount()
      },
      loveLevelProgress
    });
  },

  // 静默刷新善意数据（不显示loading动画）
  silentRefreshKindnessData() {
    this.loadUserData();
    this.loadKindnessData();
    this.loadUserStats();
  },

  // 刷新善意数据（显示loading动画，用于下拉刷新等场景）
  refreshKindnessData() {
    this.setData({ loading: true });

    setTimeout(() => {
      this.loadUserData();
      this.loadKindnessData();
      this.loadUserStats();
      this.setData({ loading: false });
    }, 1000);
  },

  // 打开善意行为库
  onOpenKindnessLibrary() {
    console.log('打开善意行为库');
    wx.navigateTo({
      url: '/pages/kindnessLibrary/index'
    });
  },

  // 显示善意行为详情
  showKindnessDetail(action) {
    const actionDetails = {
      '🤝 帮助老人过马路': {
        description: '看到老人需要过马路时，主动上前询问是否需要帮助。',
        safety: '注意交通安全，确保在安全的情况下帮助。',
        points: '完成后可获得5个善意积分'
      },
      '🐱 给小动物喂食': {
        description: '为流浪的小猫小狗准备一些食物和水。',
        safety: '注意安全距离，避免被动物抓伤。',
        points: '完成后可获得3个善意积分'
      },
      '🗑️ 捡拾路边垃圾': {
        description: '看到路边的垃圾主动捡起来扔到垃圾桶。',
        safety: '注意卫生，建议戴手套或用工具。',
        points: '完成后可获得2个善意积分'
      }
    };

    const detail = actionDetails[action] || {
      description: '这是一个很棒的善意行为！',
      safety: '请注意安全，在大人陪同下进行。',
      points: '完成后可获得善意积分'
    };

    wx.showModal({
      title: action,
      content: `${detail.description}\n\n安全提醒：${detail.safety}\n\n奖励：${detail.points}`,
      confirmText: '我要做这件好事',
      cancelText: '了解了',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '记得请家长确认哦！',
            icon: 'none'
          });
        }
      }
    });
  },

  // 打开善意积分站
  onOpenKindnessPoints() {
    console.log('打开善意积分站');
    wx.navigateTo({
      url: '/pages/kindnessPoints/index'
    });
  },

  // 显示积分兑换
  showPointsExchange() {
    const currentEnergy = this.data.currentLoveEnergy;
    if (currentEnergy < 5) {
      wx.showModal({
        title: '爱心能量不足',
        content: '你的爱心能量不足，请先完成今日任务获取更多能量！\n\n当前能量：' + currentEnergy + '点\n最低兑换：5点',
        showCancel: false,
        confirmText: '去完成任务',
        success: () => {
          wx.navigateTo({
            url: '/pages/dailyTasks/index'
          });
        }
      });
      return;
    }

    const exchangeOptions = [
      '兑换 5点❤️ → 10个善意积分',
      '兑换 10点❤️ → 25个善意积分',
      '兑换 20点❤️ → 60个善意积分',
      '兑换 50点❤️ → 200个善意积分'
    ];

    wx.showActionSheet({
      itemList: exchangeOptions,
      success: (res) => {
        const exchanges = [
          {energy: 5, points: 10},
          {energy: 10, points: 25},
          {energy: 20, points: 60},
          {energy: 50, points: 200}
        ];

        const exchange = exchanges[res.tapIndex];
        if (exchange.energy <= currentEnergy) {
          this.performPointsExchange(exchange.energy, exchange.points);
        } else {
          wx.showToast({
            title: '能量不足',
            icon: 'none'
          });
        }
      }
    });
  },

  // 执行积分兑换
  performPointsExchange(energyCost, pointsGain) {
    const userData = wx.getStorageSync('userData') || {};
    const currentPoints = wx.getStorageSync('kindnessPoints') || 0;

    // 扣除爱心能量
    userData.loveEnergy = (userData.loveEnergy || 0) - energyCost;
    wx.setStorageSync('userData', userData);

    // 增加善意积分
    const newPoints = currentPoints + pointsGain;
    wx.setStorageSync('kindnessPoints', newPoints);

    // 更新界面数据
    this.setData({
      currentLoveEnergy: userData.loveEnergy
    });

    // 显示成功信息
    this.triggerCelebration(`成功兑换${pointsGain}个善意积分！`);
  },

  // 显示积分用途
  showPointsUsage() {
    wx.showModal({
      title: '善意积分用途',
      content: '善意积分可以用来：\n\n🌳 装饰你的成长树\n🏆 解锁特殊徽章\n🎁 获得虚拟奖励\n📚 解锁更多善意行为\n\n完成现实中的善意行为，可以获得更多积分哦！',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 显示自定义捐赠输入
  showCustomDonationInput() {
    wx.showModal({
      title: '自定义捐赠',
      content: `请输入捐赠的爱心能量数量\n(当前可用：${this.data.currentLoveEnergy}点)`,
      editable: true,
      placeholderText: '请输入数字',
      success: (res) => {
        if (res.confirm && res.content) {
          const amount = parseInt(res.content);
          if (isNaN(amount) || amount < 8) {
            wx.showToast({
              title: '最少捐赠8点能量',
              icon: 'none'
            });
          } else if (amount > this.data.currentLoveEnergy) {
            wx.showToast({
              title: '能量不足',
              icon: 'none'
            });
          } else {
            this.selectProjectForDonation(amount);
          }
        }
      }
    });
  },

  // 选择捐赠项目
  selectProjectForDonation(energyAmount) {
    const projects = this.data.activeProjects;
    const projectTitles = projects.map(p => `${p.icon} ${p.title}`);

    wx.showActionSheet({
      itemList: projectTitles,
      success: (res) => {
        const selectedProject = projects[res.tapIndex];
        this.confirmDonation(selectedProject.id, energyAmount);
      }
    });
  },

  // 确认捐赠
  confirmDonation(projectId, energyAmount) {
    const project = this.data.activeProjects.find(p => p.id === projectId);
    const contributionPoints = Math.floor(energyAmount / project.energyCost);

    wx.showModal({
      title: '确认捐赠',
      content: `向"${project.title}"捐赠${energyAmount}点爱心能量\n将获得${contributionPoints}个贡献点\n\n确定要捐赠吗？`,
      confirmText: '确定捐赠',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.performDonation(projectId, energyAmount);
        }
      }
    });
  },

  // 打开善意成长树
  onOpenGrowthTree() {
    console.log('打开善意成长树');
    wx.navigateTo({
      url: '/pages/kindnessTree/index'
    });
  },

  // 计算成长树等级
  calculateTreeLevel(kindnessCount) {
    if (kindnessCount >= 100) return { level: 4, name: '善意之星', icon: '🌟', next: null };
    if (kindnessCount >= 50) return { level: 3, name: '善意大树', icon: '🌳', next: 100 };
    if (kindnessCount >= 20) return { level: 2, name: '善意花朵', icon: '🌸', next: 50 };
    return { level: 1, name: '善意萌芽', icon: '🌱', next: 20 };
  },

  // 获取徽章数量
  getBadgeCount() {
    const badges = wx.getStorageSync('kindnessBadges') || [];
    return badges.length;
  },

  // 显示成长树装饰
  showTreeDecorations() {
    const kindnessPoints = wx.getStorageSync('kindnessPoints') || 0;

    if (kindnessPoints < 10) {
      wx.showModal({
        title: '积分不足',
        content: '装饰成长树需要善意积分，请先获得更多积分！',
        showCancel: false
      });
      return;
    }

    const decorations = [
      '🌺 美丽花朵 (10积分)',
      '🦋 彩色蝴蝶 (15积分)',
      '🌈 七彩彩虹 (20积分)',
      '⭐ 闪亮星星 (25积分)'
    ];

    wx.showActionSheet({
      itemList: decorations,
      success: (res) => {
        const costs = [10, 15, 20, 25];
        const cost = costs[res.tapIndex];

        if (kindnessPoints >= cost) {
          // 扣除积分并添加装饰
          wx.setStorageSync('kindnessPoints', kindnessPoints - cost);
          wx.showToast({
            title: '装饰成功！',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: '积分不足',
            icon: 'none'
          });
        }
      }
    });
  },

  // 显示善意徽章
  showKindnessBadges() {
    const badges = wx.getStorageSync('kindnessBadges') || [];

    if (badges.length === 0) {
      wx.showModal({
        title: '暂无徽章',
        content: '你还没有获得任何善意徽章，快去做善意行为吧！',
        showCancel: false
      });
      return;
    }

    const badgeList = badges.map(badge => `${badge.icon} ${badge.name}`);

    wx.showActionSheet({
      itemList: badgeList,
      success: (res) => {
        const selectedBadge = badges[res.tapIndex];
        wx.showModal({
          title: `${selectedBadge.icon} ${selectedBadge.name}`,
          content: selectedBadge.description + '\n\n获得时间：' + new Date(selectedBadge.awardedAt).toLocaleDateString(),
          showCancel: false
        });
      }
    });
  },

  // 显示徽章
  showBadges() {
    const badges = wx.getStorageSync('charityBadges') || [];

    if (badges.length === 0) {
      wx.showModal({
        title: '暂无徽章',
        content: '你还没有获得任何慈善徽章，快去参与慈善项目吧！',
        showCancel: false
      });
      return;
    }

    const badgeList = badges.map(badge => `${badge.icon} ${badge.name}`);

    wx.showActionSheet({
      itemList: badgeList,
      success: (res) => {
        const selectedBadge = badges[res.tapIndex];
        wx.showModal({
          title: `${selectedBadge.icon} ${selectedBadge.name}`,
          content: selectedBadge.description + '\n\n获得时间：' + new Date(selectedBadge.awardedAt).toLocaleDateString(),
          showCancel: false
        });
      }
    });
  },

  // 显示捐赠历史
  showDonationHistory() {
    const history = this.data.recentDonations;

    if (history.length === 0) {
      wx.showModal({
        title: '暂无记录',
        content: '你还没有任何捐赠记录，快去参与慈善项目吧！',
        showCancel: false
      });
      return;
    }

    const historyList = history.map(item =>
      `${item.projectIcon} ${item.projectTitle} - ${item.contributionPoints}点`
    );

    wx.showActionSheet({
      itemList: historyList,
      success: (res) => {
        const selectedRecord = history[res.tapIndex];
        wx.showModal({
          title: '捐赠详情',
          content: `项目：${selectedRecord.projectTitle}\n贡献：${selectedRecord.contributionPoints}点\n消耗：${selectedRecord.energyUsed}点爱心能量\n时间：${selectedRecord.date}`,
          showCancel: false
        });
      }
    });
  },

  // 打开家庭善意计划
  onOpenFamilyPlan() {
    console.log('打开家庭善意计划');
    wx.navigateTo({
      url: '/pages/familyPlan/index'
    });
  },

  // 显示家庭计划详情
  showFamilyPlanDetail(plan) {
    const planDetails = {
      '🏠 家庭大扫除日': {
        description: '全家一起打扫房间，整理物品，创造整洁的家庭环境。',
        activities: '• 分工合作清洁各个房间\n• 整理不需要的物品\n• 一起装饰美化家庭空间',
        reward: '完成后全家都可以获得善意积分奖励！'
      },
      '🌳 一起去公园植树': {
        description: '在植树节或周末，全家一起去公园参与植树活动。',
        activities: '• 选择合适的树苗\n• 学习正确的植树方法\n• 为小树浇水和护理',
        reward: '为地球增添绿色，获得环保徽章！'
      },
      '👴 看望爷爷奶奶': {
        description: '定期看望长辈，陪伴他们聊天，帮助他们做一些力所能及的事。',
        activities: '• 陪长辈聊天谈心\n• 帮助做简单的家务\n• 一起看老照片回忆',
        reward: '传承家庭温暖，获得孝心徽章！'
      }
    };

    const detail = planDetails[plan] || {
      description: '这是一个很棒的家庭善意计划！',
      activities: '请和家人一起商量具体的活动安排。',
      reward: '完成后可以获得特殊的家庭徽章！'
    };

    wx.showModal({
      title: plan,
      content: `${detail.description}\n\n活动内容：\n${detail.activities}\n\n奖励：${detail.reward}`,
      confirmText: '开始计划',
      cancelText: '了解了',
      success: (res) => {
        if (res.confirm) {
          wx.showModal({
            title: '计划开始',
            content: '请和家人一起完成这个善意计划，完成后记得在地球指挥部请家长确认哦！',
            showCancel: false,
            confirmText: '好的'
          });
        }
      }
    });
  },



  // 触发庆祝动画
  triggerCelebration(message) {
    this.setData({
      celebrating: true,
      celebrationMessage: message
    });

    setTimeout(() => {
      this.setData({
        celebrating: false,
        celebrationMessage: ''
      });
    }, 3000);
  }
});
