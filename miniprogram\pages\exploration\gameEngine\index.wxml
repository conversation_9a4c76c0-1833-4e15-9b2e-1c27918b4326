<!--pages/exploration/gameEngine/index.wxml-->
<!-- 游戏引擎页面 - 简洁设计 -->
<view class="game-container">
  
  <!-- 游戏启动界面 -->
  <view class="game-start {{gameState === 'start' ? 'show' : 'hide'}}">
    <view class="start-card">
      <view class="game-header">
        <text class="game-planet">{{gameData.planetName}}</text>
        <text class="game-title">{{gameData.name}}</text>
      </view>
      
      <view class="game-icon">
        <text class="icon-emoji">🎮</text>
      </view>
      
      <view class="game-info">
        <view class="info-row">
          <text class="info-label">难度:</text>
          <text class="info-value">{{gameData.difficulty}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">时长:</text>
          <text class="info-value">约{{gameData.duration}}分钟</text>
        </view>
        <view class="info-row">
          <text class="info-label">奖励:</text>
          <text class="info-value">{{gameData.energyMin}}-{{gameData.energyMax}}💎</text>
        </view>
      </view>
      
      <text class="game-desc">{{gameData.description}}</text>
      
      <view class="start-actions">
        <button class="start-btn" bindtap="onStartGame">开始游戏</button>
        <button class="back-btn" bindtap="onBackToMap">返回</button>
      </view>
    </view>
  </view>

  <!-- 游戏进行界面 -->
  <view class="game-playing {{gameState === 'playing' ? 'show' : 'hide'}}">
    <view class="game-hud">
      <text class="timer">⏱️ {{formatTime(gameTimer)}}</text>
      <text class="score">得分: {{currentScore}}</text>
    </view>
    
    <view class="game-area">
      <!-- 故事理解游戏 -->
      <view class="story-game" wx:if="{{gameData.id === 'story_comprehension'}}">
        <view class="story-card">
          <view class="story-header">
            <text class="story-icon">📖</text>
            <text class="story-title">请仔细阅读下面的故事</text>
          </view>

          <view class="story-content">
            <text class="story-text">{{currentStory.story}}</text>
          </view>

          <view class="question-section">
            <view class="question-header">
              <text class="question-number">第{{currentQuestionIndex + 1}}题</text>
              <text class="question-progress">{{currentQuestionIndex + 1}}/{{currentStory.questions.length}}</text>
            </view>

            <text class="question-text">{{currentQuestion.text}}</text>

            <view class="options-grid">
              <button
                class="option-btn {{selectedAnswer === index ? 'selected' : ''}}"
                wx:for="{{currentQuestion.options}}"
                wx:key="index"
                bindtap="onSelectAnswer"
                data-index="{{index}}"
              >
                <text class="option-label">{{index === 0 ? 'A' : index === 1 ? 'B' : index === 2 ? 'C' : 'D'}}</text>
                <text class="option-text">{{item}}</text>
              </button>
            </view>

            <view class="question-actions">
              <button
                class="confirm-btn {{selectedAnswer !== null ? 'active' : 'disabled'}}"
                bindtap="onConfirmAnswer"
                disabled="{{selectedAnswer === null}}"
              >
                确认答案
              </button>
            </view>
          </view>
        </view>
      </view>

      <!-- 其他游戏的演示界面 -->
      <view class="game-demo" wx:else>
        <text class="demo-icon">🎯</text>
        <text class="demo-title">游戏演示</text>
        <text class="demo-desc">这里是游戏内容区域</text>

        <view class="demo-buttons">
          <button class="demo-btn correct" bindtap="onDemoAction" data-action="correct">
            ✅ 正确答案 (+20分)
          </button>
          <button class="demo-btn wrong" bindtap="onDemoAction" data-action="wrong">
            ❌ 错误答案 (-10分)
          </button>
          <button class="demo-btn complete" bindtap="onDemoAction" data-action="complete">
            🎉 完成游戏
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 游戏完成界面 -->
  <view class="game-complete {{gameState === 'complete' ? 'show' : 'hide'}}">
    <view class="complete-card">
      <view class="complete-header">
        <text class="complete-icon">🎉</text>
        <text class="complete-title">游戏完成！</text>
      </view>
      
      <view class="result-stats">
        <view class="stat-item">
          <text class="stat-label">最终得分</text>
          <text class="stat-value">{{finalScore}}分</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">游戏表现</text>
          <text class="stat-value">{{performance}} {{performanceStars}}</text>
        </view>
        <view class="stat-item energy">
          <text class="stat-label">获得奖励</text>
          <text class="stat-value">+{{energyReward}}💎 智慧能量</text>
        </view>
      </view>
      
      <view class="achievements" wx:if="{{newAchievements.length > 0}}">
        <text class="achievement-title">🏆 新成就解锁</text>
        <view class="achievement-list">
          <text class="achievement-item" wx:for="{{newAchievements}}" wx:key="id">
            {{item.icon}} {{item.name}}
          </text>
        </view>
      </view>
      
      <view class="complete-actions">
        <button class="continue-btn" bindtap="onContinueExploration">继续探索</button>
        <button class="back-btn" bindtap="onBackToMap">返回星图</button>
      </view>
    </view>
  </view>

</view>
