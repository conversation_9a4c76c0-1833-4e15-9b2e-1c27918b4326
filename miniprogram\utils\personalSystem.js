/**
 * 《能量星球》个人系统管理工具类
 * 负责个人档案、等级、经验值等数据管理
 */

const personalSystem = {
  // 默认个人数据
  defaultPersonalData: {
    level: 1,
    experience: 0,
    title: '新手船长',
    avatar: '🧑‍🚀',
    motto: '探索无限宇宙，成就无限可能',
    signature: '我是一名勇敢的星际探索者',
    joinDate: new Date().toISOString(),
    totalPlayTime: 0,
    preferences: {
      theme: '深空紫',
      soundEnabled: true,
      animationEnabled: true
    }
  },

  // 等级配置
  levelConfig: {
    1: { title: '新手船长', expRequired: 0 },
    2: { title: '见习探索者', expRequired: 100 },
    3: { title: '星际航行员', expRequired: 300 },
    4: { title: '宇宙探险家', expRequired: 600 },
    5: { title: '星际探索者', expRequired: 1000 },
    6: { title: '银河系船长', expRequired: 1500 },
    7: { title: '宇宙指挥官', expRequired: 2100 },
    8: { title: '星际传奇', expRequired: 2800 },
    9: { title: '宇宙大师', expRequired: 3600 },
    10: { title: '星际至尊', expRequired: 4500 }
  },

  /**
   * 获取个人数据
   */
  getPersonalData: function() {
    try {
      const data = wx.getStorageSync('personalData');
      if (data) {
        return { ...this.defaultPersonalData, ...data };
      }
      return this.defaultPersonalData;
    } catch (error) {
      console.error('获取个人数据失败:', error);
      return this.defaultPersonalData;
    }
  },

  /**
   * 保存个人数据
   */
  savePersonalData: function(data) {
    try {
      const currentData = this.getPersonalData();
      const newData = { ...currentData, ...data };
      wx.setStorageSync('personalData', newData);
      return true;
    } catch (error) {
      console.error('保存个人数据失败:', error);
      return false;
    }
  },

  /**
   * 获取当前等级信息
   */
  getCurrentLevel: function() {
    const personalData = this.getPersonalData();
    const experience = personalData.experience || 0;
    
    let currentLevel = 1;
    for (let level = 10; level >= 1; level--) {
      if (experience >= this.levelConfig[level].expRequired) {
        currentLevel = level;
        break;
      }
    }
    
    const nextLevel = Math.min(currentLevel + 1, 10);
    const currentLevelExp = this.levelConfig[currentLevel].expRequired;
    const nextLevelExp = this.levelConfig[nextLevel].expRequired;
    const progress = nextLevel <= 10 ? 
      (experience - currentLevelExp) / (nextLevelExp - currentLevelExp) : 1;
    
    return {
      level: currentLevel,
      title: this.levelConfig[currentLevel].title,
      experience: experience,
      progress: Math.max(0, Math.min(1, progress)),
      nextLevelExp: nextLevel <= 10 ? nextLevelExp : null,
      expToNext: nextLevel <= 10 ? nextLevelExp - experience : 0
    };
  },

  /**
   * 增加经验值
   */
  addExperience: function(amount) {
    try {
      const personalData = this.getPersonalData();
      const oldLevel = this.getCurrentLevel().level;
      
      personalData.experience = (personalData.experience || 0) + amount;
      this.savePersonalData(personalData);
      
      const newLevel = this.getCurrentLevel().level;
      
      // 检查是否升级
      if (newLevel > oldLevel) {
        this.onLevelUp(newLevel);
        return { levelUp: true, newLevel: newLevel, oldLevel: oldLevel };
      }
      
      return { levelUp: false, experience: personalData.experience };
    } catch (error) {
      console.error('增加经验值失败:', error);
      return { levelUp: false, error: true };
    }
  },

  /**
   * 升级处理
   */
  onLevelUp: function(newLevel) {
    try {
      const newTitle = this.levelConfig[newLevel].title;
      this.savePersonalData({ 
        level: newLevel, 
        title: newTitle 
      });
      
      // 触发升级庆祝
      console.log(`恭喜升级到 ${newLevel} 级: ${newTitle}`);
      
      // 可以在这里添加升级奖励逻辑
      this.grantLevelUpRewards(newLevel);
      
    } catch (error) {
      console.error('升级处理失败:', error);
    }
  },

  /**
   * 发放升级奖励
   */
  grantLevelUpRewards: function(level) {
    try {
      // 根据等级发放不同奖励
      const rewards = {
        wisdom: level * 20,
        love: level * 15,
        items: []
      };
      
      // 特殊等级奖励
      if (level === 5) {
        rewards.items.push('星际探索者徽章');
      } else if (level === 10) {
        rewards.items.push('星际至尊皇冠');
      }
      
      // 这里可以调用能量系统增加奖励
      console.log('升级奖励:', rewards);
      
      return rewards;
    } catch (error) {
      console.error('发放升级奖励失败:', error);
      return null;
    }
  },

  /**
   * 获取成就汇总
   */
  getAchievementSummary: function() {
    try {
      // 这里应该汇总来自各个模块的成就
      const achievements = wx.getStorageSync('achievements') || [];
      const recentAchievements = achievements
        .sort((a, b) => new Date(b.date) - new Date(a.date))
        .slice(0, 5);
      
      return {
        totalCount: achievements.length,
        latest: recentAchievements[0]?.name || '暂无成就',
        recentList: recentAchievements,
        categories: this.categorizeAchievements(achievements)
      };
    } catch (error) {
      console.error('获取成就汇总失败:', error);
      return {
        totalCount: 0,
        latest: '暂无成就',
        recentList: [],
        categories: {}
      };
    }
  },

  /**
   * 成就分类
   */
  categorizeAchievements: function(achievements) {
    const categories = {
      learning: [], // 学习成就
      kindness: [], // 善意成就
      social: [],   // 社交成就
      special: []   // 特殊成就
    };
    
    achievements.forEach(achievement => {
      const category = achievement.category || 'special';
      if (categories[category]) {
        categories[category].push(achievement);
      }
    });
    
    return categories;
  },

  /**
   * 添加成就
   */
  addAchievement: function(achievement) {
    try {
      const achievements = wx.getStorageSync('achievements') || [];
      
      // 检查是否已存在
      const exists = achievements.some(a => a.id === achievement.id);
      if (exists) {
        return false;
      }
      
      // 添加时间戳
      achievement.date = new Date().toISOString();
      achievements.push(achievement);
      
      wx.setStorageSync('achievements', achievements);
      
      // 增加经验值奖励
      this.addExperience(achievement.expReward || 10);
      
      return true;
    } catch (error) {
      console.error('添加成就失败:', error);
      return false;
    }
  },

  /**
   * 更新游戏时长
   */
  updatePlayTime: function(minutes) {
    try {
      const personalData = this.getPersonalData();
      personalData.totalPlayTime = (personalData.totalPlayTime || 0) + minutes;
      this.savePersonalData(personalData);
      
      // 检查游戏时长成就
      this.checkPlayTimeAchievements(personalData.totalPlayTime);
      
      return personalData.totalPlayTime;
    } catch (error) {
      console.error('更新游戏时长失败:', error);
      return 0;
    }
  },

  /**
   * 检查游戏时长成就
   */
  checkPlayTimeAchievements: function(totalMinutes) {
    const hours = Math.floor(totalMinutes / 60);
    
    const timeAchievements = [
      { hours: 1, id: 'play_1h', name: '初次探索', description: '累计游戏1小时' },
      { hours: 10, id: 'play_10h', name: '探索爱好者', description: '累计游戏10小时' },
      { hours: 50, id: 'play_50h', name: '星际老手', description: '累计游戏50小时' },
      { hours: 100, id: 'play_100h', name: '宇宙大师', description: '累计游戏100小时' }
    ];
    
    timeAchievements.forEach(achievement => {
      if (hours >= achievement.hours) {
        this.addAchievement({
          ...achievement,
          category: 'special',
          expReward: achievement.hours * 2
        });
      }
    });
  }
};

module.exports = personalSystem;
