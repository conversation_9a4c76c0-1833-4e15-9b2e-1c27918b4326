// 《能量星球》愿望合成器 - 智慧能量驱动的愿望创建系统
const wishSystem = require('../../../utils/wishSystem.js');

Page({
  data: {
    // 能量数据
    wisdomEnergy: 0,
    
    // 愿望分类成本
    categoryCosts: {
      learning: 30,
      hobby: 40,
      item: 60,
      experience: 50
    },
    
    // 表单数据
    selectedCategory: '',
    currentCost: 0,
    wishTitle: '',
    wishDescription: '',
    targetDate: '',
    priority: 'normal',
    
    // 当前愿望列表
    activeWishes: [],
    
    // 界面状态
    canCreate: false
  },

  onLoad: function(options) {
    console.log('愿望合成器页面加载');
    this.initWishSynthesizer();
  },

  onShow: function() {
    console.log('愿望合成器页面显示');
    this.refreshData();
  },

  // 初始化愿望合成器
  initWishSynthesizer: function() {
    this.loadEnergyData();
    this.loadWishData();
    this.updateCategoryCosts();
  },

  // 加载能量数据
  loadEnergyData: function() {
    try {
      // 从本地存储获取智慧能量
      const energyData = wx.getStorageSync('energyData') || { wisdom: 150, love: 200 };
      this.setData({
        wisdomEnergy: energyData.wisdom
      });
    } catch (error) {
      console.error('加载能量数据失败:', error);
      this.setData({
        wisdomEnergy: 150 // 默认值
      });
    }
  },

  // 加载愿望数据
  loadWishData: function() {
    try {
      const wishes = wishSystem.getAllWishes();
      const activeWishes = wishes.filter(wish => 
        wish.status === 'pending' || 
        wish.status === 'approved' || 
        wish.status === 'in_progress'
      );
      
      this.setData({
        activeWishes: activeWishes
      });
    } catch (error) {
      console.error('加载愿望数据失败:', error);
      this.setData({
        activeWishes: []
      });
    }
  },

  // 更新分类成本
  updateCategoryCosts: function() {
    const activeCount = this.data.activeWishes.length;
    const baseCosts = {
      learning: 30,
      hobby: 40,
      item: 60,
      experience: 50
    };
    
    // 活跃愿望越多，成本越高
    const multiplier = 1 + (activeCount * 0.1);
    const categoryCosts = {};
    
    Object.keys(baseCosts).forEach(category => {
      categoryCosts[category] = Math.floor(baseCosts[category] * multiplier);
    });
    
    this.setData({
      categoryCosts: categoryCosts
    });
  },

  // 刷新数据
  refreshData: function() {
    this.loadEnergyData();
    this.loadWishData();
    this.updateCategoryCosts();
  },

  // 选择愿望分类
  onSelectCategory: function(e) {
    const category = e.currentTarget.dataset.category;
    const cost = this.data.categoryCosts[category];
    
    this.setData({
      selectedCategory: category,
      currentCost: cost
    });
    
    this.checkCanCreate();
  },

  // 输入愿望标题
  onTitleInput: function(e) {
    this.setData({
      wishTitle: e.detail.value
    });
    this.checkCanCreate();
  },

  // 输入愿望描述
  onDescriptionInput: function(e) {
    this.setData({
      wishDescription: e.detail.value
    });
    this.checkCanCreate();
  },

  // 选择目标日期
  onDateChange: function(e) {
    this.setData({
      targetDate: e.detail.value
    });
    this.checkCanCreate();
  },

  // 选择优先级
  onSelectPriority: function(e) {
    const priority = e.currentTarget.dataset.priority;
    this.setData({
      priority: priority
    });
  },

  // 检查是否可以创建愿望
  checkCanCreate: function() {
    const { selectedCategory, wishTitle, wishDescription, targetDate, wisdomEnergy, currentCost } = this.data;
    
    const canCreate = selectedCategory && 
                     wishTitle.trim() && 
                     wishDescription.trim() && 
                     targetDate && 
                     wisdomEnergy >= currentCost;
    
    this.setData({
      canCreate: canCreate
    });
  },

  // 创建愿望
  onCreateWish: function() {
    if (!this.data.canCreate) {
      wx.showToast({
        title: '请完善愿望信息',
        icon: 'none'
      });
      return;
    }

    const wishData = {
      title: this.data.wishTitle,
      description: this.data.wishDescription,
      category: this.data.selectedCategory,
      priority: this.data.priority,
      targetDate: this.data.targetDate,
      tags: []
    };

    wx.showLoading({
      title: '正在合成愿望...'
    });

    // 模拟合成过程
    setTimeout(() => {
      const result = wishSystem.createWish(wishData);
      
      wx.hideLoading();
      
      if (result.success) {
        wx.showToast({
          title: '愿望合成成功！',
          icon: 'success'
        });
        
        // 重置表单
        this.resetForm();
        
        // 刷新数据
        this.refreshData();
        
        // 播放成功动画
        this.playCreateAnimation();
        
      } else {
        wx.showToast({
          title: result.error || '合成失败',
          icon: 'none'
        });
      }
    }, 1500);
  },

  // 重置表单
  resetForm: function() {
    this.setData({
      selectedCategory: '',
      currentCost: 0,
      wishTitle: '',
      wishDescription: '',
      targetDate: '',
      priority: 'normal',
      canCreate: false
    });
  },

  // 播放创建动画
  playCreateAnimation: function() {
    // TODO: 添加粒子爆炸动画
    console.log('播放愿望创建成功动画');
  },

  // 查看愿望详情
  onViewWish: function(e) {
    const wishId = e.currentTarget.dataset.id;
    console.log('查看愿望详情:', wishId);
    
    // TODO: 导航到愿望详情页面
    wx.showToast({
      title: '愿望详情功能开发中',
      icon: 'none'
    });
  },

  // 获取分类图标
  getCategoryIcon: function(category) {
    const icons = {
      learning: '📚',
      hobby: '🎨',
      item: '🎁',
      experience: '🌟'
    };
    return icons[category] || '✨';
  },

  // 获取状态文本
  getStatusText: function(status) {
    const statusMap = {
      pending: '待审核',
      approved: '已批准',
      in_progress: '进行中',
      completed: '已完成',
      rejected: '已拒绝'
    };
    return statusMap[status] || '未知';
  },

  // 页面卸载
  onUnload: function() {
    console.log('愿望合成器页面卸载');
  }
});
