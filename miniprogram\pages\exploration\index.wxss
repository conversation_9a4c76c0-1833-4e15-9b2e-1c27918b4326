/* pages/exploration/index.wxss */
/* 探索星球主界面样式 - 简洁网格布局 */

.exploration-container {
  width: 100%;
  min-height: 100vh;
  background:
    radial-gradient(circle at 20% 30%, rgba(77, 159, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255, 215, 0, 0.08) 0%, transparent 50%),
    linear-gradient(180deg, #1A1A2E 0%, #16213E 50%, #0F3460 100%);
  padding: 20rpx;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

/* 添加星空背景动画 */
.exploration-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.8), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(255,255,255,0.6), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.4), transparent),
    radial-gradient(2px 2px at 130px 80px, rgba(255,255,255,0.7), transparent),
    radial-gradient(1px 1px at 160px 30px, rgba(255,255,255,0.5), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: starTwinkle 8s ease-in-out infinite;
  z-index: 1;
  pointer-events: none;
}

@keyframes starTwinkle {
  0%, 100% { opacity: 0.3; transform: translateY(0px); }
  50% { opacity: 0.8; transform: translateY(-5px); }
}

/* 顶部标题栏 - 3D拟物化设计 */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 10;
  background: linear-gradient(145deg, rgba(26, 26, 46, 0.9), rgba(15, 52, 96, 0.8));
  border-radius: 25rpx;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.2);
  animation: headerFloat 6s ease-in-out infinite;
  transform: perspective(1000rpx) rotateX(2deg);
}

.header-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(77, 159, 255, 0.1), transparent 50%, rgba(255, 215, 0, 0.05));
  border-radius: 25rpx;
  z-index: -1;
}

@keyframes headerFloat {
  0%, 100% {
    transform: perspective(1000rpx) rotateX(2deg) translateY(0rpx);
    box-shadow:
      0 8rpx 32rpx rgba(0, 0, 0, 0.3),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.2),
      inset 0 -1rpx 0 rgba(0, 0, 0, 0.2);
  }
  50% {
    transform: perspective(1000rpx) rotateX(2deg) translateY(-3rpx);
    box-shadow:
      0 12rpx 40rpx rgba(0, 0, 0, 0.4),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.3),
      inset 0 -1rpx 0 rgba(0, 0, 0, 0.1);
  }
}

.title-area {
  display: flex;
  flex-direction: column;
}

.main-title {
  color: #FFFFFF;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 5rpx;
  text-shadow: 0 0 20rpx rgba(77, 159, 255, 0.6);
  animation: titlePulse 3s ease-in-out infinite;
}

@keyframes titlePulse {
  0%, 100% { text-shadow: 0 0 20rpx rgba(77, 159, 255, 0.6); }
  50% { text-shadow: 0 0 30rpx rgba(77, 159, 255, 0.9); }
}

.sub-title {
  color: #B0BEC5;
  font-size: 24rpx;
}

.energy-info {
  background: linear-gradient(145deg, rgba(255, 215, 0, 0.15), rgba(255, 165, 0, 0.1));
  padding: 18rpx 30rpx;
  border-radius: 30rpx;
  border: 1px solid rgba(255, 215, 0, 0.4);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 6rpx 20rpx rgba(255, 215, 0, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3),
    inset 0 -1rpx 0 rgba(255, 165, 0, 0.3);
  animation: energyPulse 3s ease-in-out infinite;
  transform: perspective(500rpx) rotateX(5deg);
}

.energy-info::before {
  content: '';
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  background: linear-gradient(45deg, rgba(255, 215, 0, 0.3), rgba(255, 165, 0, 0.2), rgba(255, 215, 0, 0.3));
  border-radius: 32rpx;
  z-index: -1;
  animation: energyBorder 4s linear infinite;
}

.energy-info::after {
  content: '';
  position: absolute;
  top: 10%;
  left: -100%;
  width: 100%;
  height: 80%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: energyShine 4s ease-in-out infinite;
  z-index: 1;
}

.energy-text {
  color: #FFD700;
  font-size: 28rpx;
  font-weight: bold;
  position: relative;
  z-index: 2;
  text-shadow:
    0 0 10rpx rgba(255, 215, 0, 0.8),
    0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

@keyframes energyPulse {
  0%, 100% {
    transform: perspective(500rpx) rotateX(5deg) scale(1);
    box-shadow:
      0 6rpx 20rpx rgba(255, 215, 0, 0.2),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.3),
      inset 0 -1rpx 0 rgba(255, 165, 0, 0.3);
  }
  50% {
    transform: perspective(500rpx) rotateX(5deg) scale(1.02);
    box-shadow:
      0 8rpx 25rpx rgba(255, 215, 0, 0.4),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.4),
      inset 0 -1rpx 0 rgba(255, 165, 0, 0.4);
  }
}

@keyframes energyBorder {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes energyShine {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

/* 探索者信息卡片 - 增强拟物化设计 */
.explorer-card {
  display: flex;
  align-items: center;
  background:
    linear-gradient(145deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.85)),
    radial-gradient(circle at 20% 20%, rgba(77, 159, 255, 0.1), transparent 50%);
  border-radius: 30rpx;
  padding: 35rpx;
  margin-bottom: 30rpx;
  border: 2px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 10;
  backdrop-filter: blur(25px);
  box-shadow:
    0 20rpx 60rpx rgba(0, 0, 0, 0.4),
    0 8rpx 20rpx rgba(0, 0, 0, 0.3),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.3),
    inset 0 -2rpx 0 rgba(0, 0, 0, 0.3),
    inset -2rpx 0 0 rgba(0, 0, 0, 0.2),
    inset 2rpx 0 0 rgba(255, 255, 255, 0.1);
  animation: cardFloat3D 8s ease-in-out infinite;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform: perspective(1000rpx) rotateX(6deg);
}

.explorer-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(77, 159, 255, 0.1), transparent 50%, rgba(255, 215, 0, 0.05));
  border-radius: 25rpx;
  z-index: -1;
}

.explorer-card:active {
  transform: perspective(1000rpx) rotateX(10deg) translateY(4rpx) scale(0.96);
  box-shadow:
    0 10rpx 30rpx rgba(0, 0, 0, 0.5),
    0 4rpx 10rpx rgba(0, 0, 0, 0.4),
    inset 0 3rpx 6rpx rgba(0, 0, 0, 0.4),
    inset 0 -1rpx 0 rgba(255, 255, 255, 0.1),
    inset -2rpx 0 0 rgba(0, 0, 0, 0.3),
    inset 2rpx 0 0 rgba(255, 255, 255, 0.05);
}

@keyframes cardFloat3D {
  0%, 100% {
    transform: perspective(1000rpx) rotateX(3deg) translateY(0rpx);
  }
  50% {
    transform: perspective(1000rpx) rotateX(3deg) translateY(-5rpx);
  }
}

.explorer-avatar {
  width: 90rpx;
  height: 90rpx;
  background: linear-gradient(145deg, #4D9FFF 0%, #2196F3 50%, #1976D2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 25rpx;
  position: relative;
  box-shadow:
    0 8rpx 25rpx rgba(77, 159, 255, 0.4),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.3),
    inset 0 -2rpx 0 rgba(0, 0, 0, 0.2);
  animation: avatar3D 5s ease-in-out infinite;
  transform: perspective(500rpx) rotateY(5deg);
}

.explorer-avatar::before {
  content: '';
  position: absolute;
  top: -3rpx;
  left: -3rpx;
  right: -3rpx;
  bottom: -3rpx;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(77, 159, 255, 0.6), rgba(33, 150, 243, 0.4), rgba(77, 159, 255, 0.6));
  z-index: -1;
  animation: avatarHalo 4s linear infinite;
}

.explorer-avatar::after {
  content: '';
  position: absolute;
  top: 15%;
  left: 15%;
  width: 30%;
  height: 30%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.6), transparent 70%);
  border-radius: 50%;
  animation: avatarHighlight 3s ease-in-out infinite;
}

@keyframes avatar3D {
  0%, 100% {
    transform: perspective(500rpx) rotateY(5deg) rotateX(0deg);
    box-shadow:
      0 8rpx 25rpx rgba(77, 159, 255, 0.4),
      inset 0 2rpx 0 rgba(255, 255, 255, 0.3),
      inset 0 -2rpx 0 rgba(0, 0, 0, 0.2);
  }
  50% {
    transform: perspective(500rpx) rotateY(5deg) rotateX(5deg);
    box-shadow:
      0 12rpx 35rpx rgba(77, 159, 255, 0.6),
      inset 0 2rpx 0 rgba(255, 255, 255, 0.4),
      inset 0 -2rpx 0 rgba(0, 0, 0, 0.1);
  }
}

@keyframes avatarHalo {
  0% { transform: rotate(0deg) scale(1); opacity: 0.6; }
  50% { transform: rotate(180deg) scale(1.1); opacity: 0.8; }
  100% { transform: rotate(360deg) scale(1); opacity: 0.6; }
}

@keyframes avatarHighlight {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 0.9; transform: scale(1.2); }
}

.avatar-icon {
  font-size: 40rpx;
}

.explorer-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.explorer-name {
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.explorer-level {
  color: #4D9FFF;
  font-size: 24rpx;
}

.explorer-stats {
  display: flex;
  align-items: center;
}

.stats-text {
  color: #B0BEC5;
  font-size: 22rpx;
}

/* 星球网格区域 */
.planets-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 25rpx;
  margin-bottom: 30rpx;
  padding: 15rpx;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 25rpx;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
}

/* 星球卡片 - 增强拟物化设计 */
.planet-card {
  background:
    linear-gradient(145deg, rgba(26, 26, 46, 0.9), rgba(22, 33, 62, 0.7)),
    radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.1), transparent 60%);
  border-radius: 30rpx;
  padding: 35rpx;
  border: 2px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(25px);
  box-shadow:
    0 15rpx 50rpx rgba(0, 0, 0, 0.3),
    0 5rpx 15rpx rgba(0, 0, 0, 0.2),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.3),
    inset 0 -2rpx 0 rgba(0, 0, 0, 0.3),
    inset -2rpx 0 0 rgba(0, 0, 0, 0.2),
    inset 2rpx 0 0 rgba(255, 255, 255, 0.1);
  animation: planetFloat3D 6s ease-in-out infinite;
  transform: perspective(1000rpx) rotateX(8deg) rotateY(3deg);
}

.planet-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent 50%, rgba(77, 159, 255, 0.05));
  border-radius: 25rpx;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.planet-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: cardShimmer3D 8s ease-in-out infinite;
  z-index: 2;
  pointer-events: none;
}

.planet-card.unlocked {
  opacity: 1;
}

.planet-card.unlocked::before {
  opacity: 1;
}

.planet-card.locked {
  opacity: 0.5;
  filter: grayscale(60%);
  transform: perspective(1000rpx) rotateX(5deg) rotateY(2deg) scale(0.95);
}

/* 认知训练中心特殊样式 */
.planet-card.special-card {
  background:
    linear-gradient(145deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.85)),
    radial-gradient(circle at 30% 30%, rgba(255, 152, 0, 0.15), transparent 40%),
    radial-gradient(circle at 70% 70%, rgba(33, 150, 243, 0.15), transparent 40%);
  border: 2px solid rgba(255, 215, 0, 0.3);
  box-shadow:
    0 20rpx 60rpx rgba(255, 215, 0, 0.2),
    0 8rpx 20rpx rgba(0, 0, 0, 0.3),
    inset 0 2rpx 0 rgba(255, 215, 0, 0.4),
    inset 0 -2rpx 0 rgba(0, 0, 0, 0.3),
    inset -2rpx 0 0 rgba(0, 0, 0, 0.2),
    inset 2rpx 0 0 rgba(255, 215, 0, 0.2);
  animation: specialCardFloat 5s ease-in-out infinite;
}

.planet-card.special-card::before {
  background: linear-gradient(135deg,
    rgba(255, 215, 0, 0.2),
    rgba(255, 152, 0, 0.1) 30%,
    rgba(33, 150, 243, 0.1) 60%,
    rgba(76, 175, 80, 0.1) 80%,
    transparent);
  opacity: 1;
}

.planet-card.special-card:hover {
  transform: perspective(1000rpx) rotateX(3deg) rotateY(1deg) translateY(-12rpx);
  box-shadow:
    0 30rpx 100rpx rgba(255, 215, 0, 0.3),
    0 15rpx 35rpx rgba(0, 0, 0, 0.4),
    inset 0 3rpx 0 rgba(255, 215, 0, 0.5),
    inset 0 -3rpx 0 rgba(0, 0, 0, 0.4),
    inset -3rpx 0 0 rgba(0, 0, 0, 0.3),
    inset 3rpx 0 0 rgba(255, 215, 0, 0.3);
}

@keyframes specialCardFloat {
  0%, 100% {
    transform: perspective(1000rpx) rotateX(5deg) rotateY(2deg) translateY(0rpx);
    box-shadow:
      0 20rpx 60rpx rgba(255, 215, 0, 0.2),
      0 8rpx 20rpx rgba(0, 0, 0, 0.3),
      inset 0 2rpx 0 rgba(255, 215, 0, 0.4),
      inset 0 -2rpx 0 rgba(0, 0, 0, 0.3);
  }
  50% {
    transform: perspective(1000rpx) rotateX(5deg) rotateY(2deg) translateY(-5rpx);
    box-shadow:
      0 25rpx 70rpx rgba(255, 215, 0, 0.3),
      0 12rpx 25rpx rgba(0, 0, 0, 0.4),
      inset 0 2rpx 0 rgba(255, 215, 0, 0.5),
      inset 0 -2rpx 0 rgba(0, 0, 0, 0.2);
  }
}

.planet-card:active {
  transform: perspective(1000rpx) rotateX(12deg) rotateY(6deg) translateY(5rpx) scale(0.94);
  box-shadow:
    0 8rpx 25rpx rgba(0, 0, 0, 0.5),
    0 3rpx 8rpx rgba(0, 0, 0, 0.4),
    inset 0 4rpx 8rpx rgba(0, 0, 0, 0.4),
    inset 0 -1rpx 0 rgba(255, 255, 255, 0.1),
    inset -2rpx 0 0 rgba(0, 0, 0, 0.4),
    inset 2rpx 0 0 rgba(255, 255, 255, 0.05);
}

.planet-card:hover {
  transform: perspective(1000rpx) rotateX(5deg) rotateY(2deg) translateY(-8rpx);
  box-shadow:
    0 25rpx 80rpx rgba(0, 0, 0, 0.4),
    0 10rpx 25rpx rgba(0, 0, 0, 0.3),
    inset 0 3rpx 0 rgba(255, 255, 255, 0.4),
    inset 0 -3rpx 0 rgba(0, 0, 0, 0.4),
    inset -3rpx 0 0 rgba(0, 0, 0, 0.3),
    inset 3rpx 0 0 rgba(255, 255, 255, 0.2);
}

@keyframes planetFloat3D {
  0%, 100% {
    transform: perspective(1000rpx) rotateX(5deg) rotateY(2deg) translateY(0rpx);
  }
  50% {
    transform: perspective(1000rpx) rotateX(5deg) rotateY(2deg) translateY(-3rpx);
  }
}

@keyframes cardShimmer3D {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); opacity: 0; }
}

/* 星球图标区域 */
.planet-icon-area {
  position: relative;
  margin-bottom: 15rpx;
}

.planet-emoji {
  font-size: 70rpx;
  display: block;
  position: relative;
  z-index: 3;
  animation: emoji3D 4s ease-in-out infinite;
  filter:
    drop-shadow(0 0 15rpx rgba(255, 255, 255, 0.4))
    drop-shadow(0 5rpx 10rpx rgba(0, 0, 0, 0.3));
  transform: perspective(300rpx) rotateX(10deg);
}

@keyframes emoji3D {
  0%, 100% {
    transform: perspective(300rpx) rotateX(10deg) translateY(0rpx) scale(1);
  }
  50% {
    transform: perspective(300rpx) rotateX(10deg) translateY(-8rpx) scale(1.1);
  }
}

.planet-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  z-index: 2;
  animation: glow3D 3s ease-in-out infinite;
  filter: blur(2rpx);
}

.glow-purple {
  background: radial-gradient(circle, rgba(156, 39, 176, 0.6), rgba(156, 39, 176, 0.3) 50%, transparent 80%);
  box-shadow: 0 0 30rpx rgba(156, 39, 176, 0.4);
}
.glow-blue {
  background: radial-gradient(circle, rgba(33, 150, 243, 0.6), rgba(33, 150, 243, 0.3) 50%, transparent 80%);
  box-shadow: 0 0 30rpx rgba(33, 150, 243, 0.4);
}
.glow-orange {
  background: radial-gradient(circle, rgba(255, 152, 0, 0.6), rgba(255, 152, 0, 0.3) 50%, transparent 80%);
  box-shadow: 0 0 30rpx rgba(255, 152, 0, 0.4);
}
.glow-green {
  background: radial-gradient(circle, rgba(76, 175, 80, 0.6), rgba(76, 175, 80, 0.3) 50%, transparent 80%);
  box-shadow: 0 0 30rpx rgba(76, 175, 80, 0.4);
}
.glow-pink {
  background: radial-gradient(circle, rgba(233, 30, 99, 0.6), rgba(233, 30, 99, 0.3) 50%, transparent 80%);
  box-shadow: 0 0 30rpx rgba(233, 30, 99, 0.4);
}
.glow-rainbow {
  background: radial-gradient(circle,
    rgba(255, 152, 0, 0.4) 0%,
    rgba(33, 150, 243, 0.4) 25%,
    rgba(76, 175, 80, 0.4) 50%,
    rgba(156, 39, 176, 0.4) 75%,
    transparent 100%);
  box-shadow:
    0 0 30rpx rgba(255, 152, 0, 0.3),
    0 0 40rpx rgba(33, 150, 243, 0.2),
    0 0 50rpx rgba(76, 175, 80, 0.2);
  animation: rainbowGlow 4s ease-in-out infinite;
}

@keyframes rainbowGlow {
  0% {
    filter: hue-rotate(0deg) brightness(1);
    transform: translate(-50%, -50%) scale(0.8) rotate(0deg);
  }
  25% {
    filter: hue-rotate(90deg) brightness(1.2);
    transform: translate(-50%, -50%) scale(1.1) rotate(90deg);
  }
  50% {
    filter: hue-rotate(180deg) brightness(1.4);
    transform: translate(-50%, -50%) scale(1.3) rotate(180deg);
  }
  75% {
    filter: hue-rotate(270deg) brightness(1.2);
    transform: translate(-50%, -50%) scale(1.1) rotate(270deg);
  }
  100% {
    filter: hue-rotate(360deg) brightness(1);
    transform: translate(-50%, -50%) scale(0.8) rotate(360deg);
  }
}

@keyframes glow3D {
  0%, 100% {
    opacity: 0.4;
    transform: translate(-50%, -50%) scale(0.8) perspective(200rpx) rotateX(0deg);
  }
  50% {
    opacity: 0.9;
    transform: translate(-50%, -50%) scale(1.3) perspective(200rpx) rotateX(10deg);
  }
}

/* 星球信息 */
.planet-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.planet-name {
  color: #FFFFFF;
  font-size: 26rpx;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.planet-desc {
  color: #B0BEC5;
  font-size: 22rpx;
}

/* 星球状态 */
.planet-status {
  position: absolute;
  top: 15rpx;
  right: 15rpx;
}

.status-text {
  font-size: 24rpx;
}



/* 弹窗遮罩 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* 通用弹窗样式 - 扁平设计 */
.age-modal, .planet-modal {
  width: 600rpx;
  max-height: 80vh;
  background: rgba(27, 38, 59, 0.95);
  border-radius: 20rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.5);
  overflow: hidden;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid rgba(77, 159, 255, 0.3);
  background: rgba(77, 159, 255, 0.1);
}

.modal-title {
  color: #4D9FFF;
  font-size: 32rpx;
  font-weight: bold;
}

.modal-close {
  color: #fff;
  font-size: 40rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}



/* 星球详情 */
.planet-detail {
  padding: 30rpx;
  text-align: center;
}

.planet-large-icon {
  margin-bottom: 20rpx;
}

.large-emoji {
  font-size: 80rpx;
}

.planet-description {
  color: #B0BEC5;
  font-size: 26rpx;
  line-height: 1.5;
  margin-bottom: 25rpx;
  display: block;
}

.games-preview {
  text-align: left;
}

.games-title {
  color: #4D9FFF;
  font-size: 26rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
  display: block;
}

.games-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.game-item {
  color: #B0BEC5;
  font-size: 24rpx;
  line-height: 1.4;
}

/* 弹窗操作区 */
.modal-actions {
  padding: 30rpx;
  border-top: 1px solid rgba(77, 159, 255, 0.2);
}

.start-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #4D9FFF 0%, #2196F3 100%);
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  border-radius: 15rpx;
  border: none;
  box-shadow: 0 5rpx 15rpx rgba(77, 159, 255, 0.4);
  transition: all 0.3s ease;
}

.start-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 3rpx 10rpx rgba(77, 159, 255, 0.6);
}

.start-btn.disabled {
  background: rgba(255, 255, 255, 0.1);
  color: #666;
  box-shadow: none;
}
