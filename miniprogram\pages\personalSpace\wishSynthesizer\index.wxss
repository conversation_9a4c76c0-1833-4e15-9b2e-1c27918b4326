/* 《能量星球》愿望合成器 - 世界500强级别UI设计 */

/* ==================== CSS变量系统 ==================== */
page {
  /* 星云紫色渐变系统 */
  --nebula-gradient: linear-gradient(135deg, #2D1B69 0%, #4A148C 30%, #6A1B9A 60%, #8E24AA 100%);
  --nebula-dark: #1A0B3D;
  --nebula-medium: #4A148C;
  --nebula-light: #8E24AA;
  
  /* 智慧蓝色系统 */
  --wisdom-blue: #00E5FF;
  --wisdom-blue-light: #62EFFF;
  --wisdom-blue-dark: #00B8D4;
  --wisdom-blue-glow: rgba(0, 229, 255, 0.4);
  
  /* 愿望紫色系统 */
  --wish-purple: #E1BEE7;
  --wish-purple-light: #F3E5F5;
  --wish-purple-dark: #9C27B0;
  --wish-purple-glow: rgba(156, 39, 176, 0.4);
  
  /* 能量金色系统 */
  --energy-gold: #FFD700;
  --energy-gold-light: #FFECB3;
  --energy-gold-dark: #FF8F00;
  --energy-gold-glow: rgba(255, 215, 0, 0.4);
  
  /* 成功绿色系统 */
  --success-green: #00FF88;
  --success-green-light: #69F0AE;
  --success-green-dark: #00BFA5;
  --success-green-glow: rgba(0, 255, 136, 0.4);
  
  /* 阴影系统 */
  --shadow-subtle: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  --shadow-medium: 0 8rpx 24rpx rgba(0, 0, 0, 0.25);
  --shadow-strong: 0 16rpx 48rpx rgba(0, 0, 0, 0.35);
  --shadow-dramatic: 0 32rpx 64rpx rgba(0, 0, 0, 0.45);
  
  /* 动画时长系统 */
  --duration-fast: 0.2s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;
  --duration-dramatic: 1s;
  
  /* 缓动函数系统 */
  --ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
  --ease-in-out-quart: cubic-bezier(0.76, 0, 0.24, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ==================== 基础页面样式 ==================== */
.page {
  width: 100%;
  height: 100vh;
  background: var(--nebula-gradient);
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
}

.wish-synthesizer {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* ==================== 深空星云背景 ==================== */
.nebula-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.star-field {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.star {
  position: absolute;
  width: 6rpx;
  height: 6rpx;
  background: white;
  border-radius: 50%;
  box-shadow: 0 0 15rpx rgba(255, 255, 255, 0.8);
  animation: starTwinkle 4s ease-in-out infinite;
}

.star-1 { top: 20%; left: 15%; animation-delay: 0s; }
.star-2 { top: 30%; left: 85%; animation-delay: 1s; }
.star-3 { top: 60%; left: 10%; animation-delay: 2s; }
.star-4 { top: 75%; left: 90%; animation-delay: 3s; }
.star-5 { top: 45%; left: 50%; animation-delay: 1.5s; }

@keyframes starTwinkle {
  0%, 100% { 
    opacity: 0.4; 
    transform: scale(1); 
  }
  50% { 
    opacity: 1; 
    transform: scale(1.5); 
  }
}

/* ==================== 星云漩涡 ==================== */
.nebula-vortex {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  height: 600rpx;
  opacity: 0.3;
}

.vortex-layer {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 3rpx solid rgba(156, 39, 176, 0.3);
  border-radius: 50%;
  animation: vortexRotate 20s linear infinite;
}

.vortex-layer.layer-1 {
  width: 600rpx;
  height: 600rpx;
  animation-duration: 20s;
}

.vortex-layer.layer-2 {
  width: 400rpx;
  height: 400rpx;
  animation-duration: 15s;
  animation-direction: reverse;
}

.vortex-layer.layer-3 {
  width: 200rpx;
  height: 200rpx;
  animation-duration: 10s;
}

@keyframes vortexRotate {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* ==================== 能量状态HUD ==================== */
.energy-hud {
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  background: linear-gradient(90deg, 
    rgba(0, 229, 255, 0.1) 0%, 
    rgba(156, 39, 176, 0.1) 100%
  );
  border-bottom: 2rpx solid rgba(0, 229, 255, 0.3);
  backdrop-filter: blur(20rpx);
}

.energy-display {
  display: flex;
  align-items: center;
  gap: 16rpx;
  background: rgba(0, 229, 255, 0.15);
  border: 2rpx solid var(--wisdom-blue);
  border-radius: 24rpx;
  padding: 12rpx 20rpx;
  box-shadow: 0 0 20rpx var(--wisdom-blue-glow);
  animation: energyPulse 3s ease-in-out infinite;
}

@keyframes energyPulse {
  0%, 100% { 
    box-shadow: 0 0 20rpx var(--wisdom-blue-glow);
  }
  50% { 
    box-shadow: 0 0 35rpx var(--wisdom-blue-glow);
  }
}

.energy-icon {
  font-size: 32rpx;
  filter: drop-shadow(0 0 10rpx var(--wisdom-blue));
}

.energy-info {
  display: flex;
  flex-direction: column;
}

.energy-value {
  font-size: 28rpx;
  font-weight: bold;
  color: white;
}

.energy-label {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
}

.cost-preview {
  background: rgba(255, 215, 0, 0.15);
  border: 2rpx solid var(--energy-gold);
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  box-shadow: 0 0 15rpx var(--energy-gold-glow);
}

.cost-text {
  font-size: 22rpx;
  color: white;
  font-weight: bold;
}

/* ==================== 主要内容区域 ==================== */
.content-scroll {
  position: relative;
  z-index: 2;
  flex: 1;
  padding: 0 30rpx 40rpx;
  box-sizing: border-box;
  width: 100%;
}

/* ==================== 区域标题样式 ==================== */
.section-header {
  margin-bottom: 30rpx;
  text-align: center;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
  text-shadow: 0 0 20rpx var(--wish-purple-glow);
}

.section-subtitle {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  opacity: 0.9;
}

/* ==================== 愿望创建区域 ==================== */
.wish-creation-section {
  margin-bottom: 50rpx;
}

/* ==================== 分类选择器 ==================== */
.category-selector {
  margin-bottom: 40rpx;
}

.selector-label {
  display: block;
  font-size: 24rpx;
  color: white;
  margin-bottom: 20rpx;
  font-weight: bold;
}

.category-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.category-item {
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 24rpx;
  text-align: center;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out-quart);
  backdrop-filter: blur(10rpx);
}

.category-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-4rpx);
}

.category-item.selected {
  background: rgba(156, 39, 176, 0.2);
  border-color: var(--wish-purple);
  box-shadow: 0 0 25rpx var(--wish-purple-glow);
  transform: translateY(-4rpx);
}

.category-icon {
  font-size: 40rpx;
  margin-bottom: 12rpx;
  filter: drop-shadow(0 0 8rpx currentColor);
}

.category-name {
  display: block;
  font-size: 22rpx;
  color: white;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.category-cost {
  display: block;
  font-size: 18rpx;
  color: var(--energy-gold);
  font-weight: bold;
}

/* ==================== 愿望表单 ==================== */
.wish-form {
  background: rgba(255, 255, 255, 0.08);
  border: 2rpx solid rgba(255, 255, 255, 0.15);
  border-radius: 24rpx;
  padding: 32rpx;
  backdrop-filter: blur(15rpx);
  box-shadow: var(--shadow-medium);
  animation: formSlideIn 0.5s var(--ease-out-quart);
}

@keyframes formSlideIn {
  0% {
    opacity: 0;
    transform: translateY(30rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 22rpx;
  color: white;
  margin-bottom: 12rpx;
  font-weight: bold;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 0 20rpx;
  font-size: 24rpx;
  color: white;
  box-sizing: border-box;
  transition: all var(--duration-normal) var(--ease-out-quart);
}

.form-input:focus {
  border-color: var(--wisdom-blue);
  box-shadow: 0 0 20rpx var(--wisdom-blue-glow);
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 20rpx;
  font-size: 22rpx;
  color: white;
  box-sizing: border-box;
  transition: all var(--duration-normal) var(--ease-out-quart);
}

.form-textarea:focus {
  border-color: var(--wisdom-blue);
  box-shadow: 0 0 20rpx var(--wisdom-blue-glow);
}

.date-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 20rpx;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out-quart);
}

.date-picker:hover {
  border-color: var(--wisdom-blue);
  box-shadow: 0 0 15rpx var(--wisdom-blue-glow);
}

.date-text {
  font-size: 22rpx;
  color: white;
}

.date-icon {
  font-size: 24rpx;
}

.priority-selector {
  display: flex;
  gap: 12rpx;
}

.priority-item {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  padding: 16rpx;
  text-align: center;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out-quart);
}

.priority-item:hover {
  background: rgba(255, 255, 255, 0.15);
}

.priority-item.selected {
  background: rgba(0, 229, 255, 0.2);
  border-color: var(--wisdom-blue);
  box-shadow: 0 0 15rpx var(--wisdom-blue-glow);
}

.priority-text {
  font-size: 20rpx;
  color: white;
  font-weight: bold;
}

/* ==================== 创建按钮 ==================== */
.create-button-container {
  margin-top: 40rpx;
  text-align: center;
}

.create-button {
  position: relative;
  display: inline-block;
  background: linear-gradient(135deg,
    var(--wish-purple) 0%,
    var(--wish-purple-dark) 100%
  );
  border: 3rpx solid var(--wish-purple-light);
  border-radius: 24rpx;
  padding: 20rpx 40rpx;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out-quart);
  overflow: hidden;
}

.create-button.enabled {
  box-shadow: 0 0 30rpx var(--wish-purple-glow);
  animation: buttonGlow 2s ease-in-out infinite;
}

.create-button.enabled:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 40rpx var(--wish-purple-glow);
}

.create-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

@keyframes buttonGlow {
  0%, 100% {
    box-shadow: 0 0 30rpx var(--wish-purple-glow);
  }
  50% {
    box-shadow: 0 0 50rpx var(--wish-purple-glow);
  }
}

.button-glow {
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  width: calc(100% + 8rpx);
  height: calc(100% + 8rpx);
  border-radius: 28rpx;
  background: linear-gradient(45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 70%
  );
  animation: glowSweep 3s linear infinite;
}

@keyframes glowSweep {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.button-text {
  position: relative;
  z-index: 2;
  font-size: 26rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.energy-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  background: var(--energy-gold);
  border-radius: 50%;
  box-shadow: 0 0 10rpx var(--energy-gold-glow);
  animation: particleFloat 3s ease-in-out infinite;
}

.particle-1 {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.particle-2 {
  top: 60%;
  right: 30%;
  animation-delay: 1s;
}

.particle-3 {
  bottom: 30%;
  left: 70%;
  animation-delay: 2s;
}

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20rpx) scale(1.2);
    opacity: 1;
  }
}

/* ==================== 当前愿望列表 ==================== */
.current-wishes-section {
  margin-top: 50rpx;
}

.wishes-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.wish-card {
  background: rgba(255, 255, 255, 0.08);
  border: 2rpx solid rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  padding: 24rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: var(--shadow-medium);
  transition: all var(--duration-normal) var(--ease-out-quart);
}

.wish-card:hover {
  background: rgba(255, 255, 255, 0.12);
  transform: translateY(-4rpx);
  box-shadow: var(--shadow-strong);
}

.wish-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.wish-category-badge {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12rpx;
  padding: 8rpx 12rpx;
  display: flex;
  align-items: center;
}

.wish-category-badge.learning {
  background: rgba(33, 150, 243, 0.2);
  border: 1rpx solid rgba(33, 150, 243, 0.5);
}

.wish-category-badge.hobby {
  background: rgba(255, 152, 0, 0.2);
  border: 1rpx solid rgba(255, 152, 0, 0.5);
}

.wish-category-badge.item {
  background: rgba(76, 175, 80, 0.2);
  border: 1rpx solid rgba(76, 175, 80, 0.5);
}

.wish-category-badge.experience {
  background: rgba(156, 39, 176, 0.2);
  border: 1rpx solid rgba(156, 39, 176, 0.5);
}

.badge-icon {
  font-size: 20rpx;
}

.wish-status {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10rpx;
  padding: 6rpx 12rpx;
}

.wish-status.pending {
  background: rgba(255, 193, 7, 0.2);
  border: 1rpx solid rgba(255, 193, 7, 0.5);
}

.wish-status.approved {
  background: rgba(76, 175, 80, 0.2);
  border: 1rpx solid rgba(76, 175, 80, 0.5);
}

.wish-status.in_progress {
  background: rgba(33, 150, 243, 0.2);
  border: 1rpx solid rgba(33, 150, 243, 0.5);
}

.status-text {
  font-size: 18rpx;
  color: white;
  font-weight: bold;
}

.wish-content {
  margin-bottom: 20rpx;
}

.wish-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
}

.wish-description {
  display: block;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
  margin-bottom: 16rpx;
}

.wish-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.wish-date,
.wish-energy {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.7);
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg,
    var(--success-green) 0%,
    var(--success-green-light) 100%
  );
  border-radius: 4rpx;
  transition: width var(--duration-slow) var(--ease-out-quart);
  box-shadow: 0 0 10rpx var(--success-green-glow);
}

.progress-text {
  font-size: 18rpx;
  color: var(--success-green);
  font-weight: bold;
  min-width: 60rpx;
  text-align: right;
}

.wish-actions {
  display: flex;
  justify-content: flex-end;
}

.action-button {
  background: rgba(0, 229, 255, 0.15);
  border: 2rpx solid var(--wisdom-blue);
  border-radius: 12rpx;
  padding: 12rpx 20rpx;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out-quart);
}

.action-button:hover {
  background: rgba(0, 229, 255, 0.25);
  box-shadow: 0 0 15rpx var(--wisdom-blue-glow);
}

.action-text {
  font-size: 20rpx;
  color: white;
  font-weight: bold;
}

/* ==================== 空状态 ==================== */
.empty-state {
  text-align: center;
  padding: 60rpx 40rpx;
  opacity: 0.8;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  filter: drop-shadow(0 0 20rpx var(--wish-purple-glow));
  animation: emptyFloat 3s ease-in-out infinite;
}

@keyframes emptyFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

.empty-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 12rpx;
}

.empty-subtitle {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 750rpx) {
  .content-scroll {
    padding: 0 20rpx 30rpx;
  }

  .category-grid {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }

  .wish-form {
    padding: 24rpx;
  }

  .wish-card {
    padding: 20rpx;
  }
}
