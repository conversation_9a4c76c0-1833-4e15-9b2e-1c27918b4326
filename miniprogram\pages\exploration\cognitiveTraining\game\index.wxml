<!--pages/exploration/cognitiveTraining/game/index.wxml-->
<view class="container">


  <!-- 游戏进行中的界面 -->
  <view class="game-screen" wx:if="{{gameStarted && !gameCompleted}}">
    <!-- 游戏头部信息 -->
    <view class="game-header">
      <view class="progress-info">
        <text class="question-number">{{currentQuestionIndex + 1}}/{{totalQuestions}}</text>
        <view class="progress-bar">
          <view 
            class="progress-fill" 
            style="width: {{((currentQuestionIndex + 1) / totalQuestions) * 100}}%; background: {{trainingConfig[trainingType].color}};"
          ></view>
        </view>
      </view>
      
      <view class="timer-section">
        <view class="timer {{timeLeft <= 5 ? 'warning' : ''}}" style="border-color: {{trainingConfig[trainingType].color}};">
          {{timeLeft}}
        </view>
        <text class="timer-label">秒</text>
      </view>
      
      <view class="score-section">
        <text class="score-label">得分</text>
        <text class="score-value">{{score}}</text>
      </view>
    </view>

    <!-- 题目内容区域 -->
    <view class="question-content" animation="{{questionAnimation}}">
      <!-- 题目卡片 -->
      <view class="question-card">
        <view class="question-number">第 {{currentQuestionIndex + 1}} 题</view>
        <view class="question-text">{{currentQuestion.question}}</view>
      </view>

      <!-- 选择题选项 -->
      <view class="options-container" wx:if="{{currentQuestion.type === 'multiple_choice'}}">
        <view
          class="option-item {{selectedOption === index ? 'selected' : ''}}"
          wx:for="{{currentQuestion.options}}"
          wx:key="index"
          data-index="{{index}}"
          bindtap="selectOption"
          style="border-color: {{selectedOption === index ? trainingConfig[trainingType].color : ''}};"
        >
          <view class="option-prefix" style="background: {{selectedOption === index ? trainingConfig[trainingType].color : ''}};">
            {{['A', 'B', 'C', 'D'][index]}}
          </view>
          <view class="option-text">{{item}}</view>
          <view class="option-check {{selectedOption === index ? 'checked' : ''}}" style="color: {{trainingConfig[trainingType].color}};">
            ✓
          </view>
        </view>
      </view>

      <!-- 文本输入题 -->
      <view class="input-container" wx:if="{{currentQuestion.type === 'text_input'}}">
        <view class="input-label">请输入你的答案</view>
        <input
          class="answer-input"
          placeholder="在这里输入答案..."
          value="{{userAnswer}}"
          bindinput="onAnswerInput"
          style="border-color: {{trainingConfig[trainingType].color}};"
        />
      </view>

      <!-- 提交按钮 -->
      <view class="submit-container">
        <view
          class="submit-btn {{(currentQuestion.type === 'multiple_choice' && selectedOption === -1) || (currentQuestion.type === 'text_input' && !userAnswer.trim()) ? 'disabled' : ''}}"
          style="background: {{trainingConfig[trainingType].color}};"
          bindtap="submitAnswer"
        >
          提交答案
        </view>
      </view>
    </view>
  </view>

  <!-- 游戏完成界面 -->
  <view class="result-screen" wx:if="{{gameCompleted}}">
    <view class="result-content">
      <view class="result-icon">🎉</view>
      <view class="result-title">训练完成！</view>
      <view class="result-subtitle">恭喜你完成了{{trainingName}}训练</view>
      
      <view class="result-stats">
        <view class="stat-item">
          <view class="stat-number">{{correctAnswers}}</view>
          <view class="stat-label">正确题数</view>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <view class="stat-number">{{score}}</view>
          <view class="stat-label">总得分</view>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <view class="stat-number">{{accuracyRate}}%</view>
          <view class="stat-label">正确率</view>
        </view>
      </view>
      
      <view class="energy-reward">
        <view class="reward-title">获得奖励</view>
        <view class="reward-items">
          <view class="reward-item">
            <text class="reward-icon">🔷</text>
            <text class="reward-text">智慧能量 +{{correctAnswers * 2}}</text>
          </view>
          <view class="reward-item">
            <text class="reward-icon">❤️</text>
            <text class="reward-text">爱心能量 +{{loveEnergyReward}}</text>
          </view>
        </view>
      </view>
      
      <view class="result-actions">
        <view class="action-btn secondary-btn" bindtap="restartGame">
          再次训练
        </view>
        <view class="action-btn primary-btn" style="background: {{trainingConfig[trainingType].color}};" bindtap="backToTrainingCenter">
          返回训练中心
        </view>
      </view>
    </view>
  </view>

  <!-- 答题反馈弹窗 -->
  <view class="feedback-overlay {{showFeedback ? 'show' : ''}}" wx:if="{{showFeedback}}">
    <view class="feedback-modal {{feedbackType}}">
      <view class="feedback-icon">
        {{feedbackType === 'correct' ? '✅' : '❌'}}
      </view>
      <view class="feedback-message">{{feedbackMessage}}</view>
      <view class="feedback-explanation" wx:if="{{currentQuestion.explanation}}">
        {{currentQuestion.explanation}}
      </view>
    </view>
  </view>
</view>
