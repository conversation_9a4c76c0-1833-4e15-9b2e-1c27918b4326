/* 《能量星球》沉浸式飞船舰桥 - 主界面样式 */

/* 页面基础样式 */
.page {
  min-height: 100vh;
  background: radial-gradient(ellipse at center, #1A183E 0%, #0D0B1E 70%, #000000 100%);
  color: #FFFFFF;
  padding: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

/* 动态背景粒子系统 */
.particle-system {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.floating-particle {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: rgba(77, 159, 255, 0.6);
  border-radius: 50%;
  animation: floatParticle 8s linear infinite;
}

.floating-particle.p1 { left: 10%; animation-delay: 0s; }
.floating-particle.p2 { left: 20%; animation-delay: 1s; }
.floating-particle.p3 { left: 30%; animation-delay: 2s; }
.floating-particle.p4 { left: 40%; animation-delay: 3s; }
.floating-particle.p5 { left: 60%; animation-delay: 4s; }
.floating-particle.p6 { left: 70%; animation-delay: 5s; }
.floating-particle.p7 { left: 80%; animation-delay: 6s; }
.floating-particle.p8 { left: 90%; animation-delay: 7s; }

@keyframes floatParticle {
  0% {
    transform: translateY(100vh) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
    transform: translateY(90vh) scale(1);
  }
  90% {
    opacity: 1;
    transform: translateY(10vh) scale(1);
  }
  100% {
    transform: translateY(0vh) scale(0);
    opacity: 0;
  }
}

/* HUD 抬头显示器 */
.hud-display {
  position: relative;
  z-index: 100;
  padding: 20rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: linear-gradient(180deg, rgba(26, 24, 62, 0.8) 0%, rgba(26, 24, 62, 0) 100%);
  backdrop-filter: blur(10px);
}

/* 能量状态显示 */
.energy-status, .love-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}

.energy-orb, .love-orb {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.orb-core {
  width: 60rpx;
  height: 60rpx;
  background: radial-gradient(circle, rgba(77, 159, 255, 0.8) 0%, rgba(77, 159, 255, 0.3) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: orbPulse 2s ease-in-out infinite;
}

.orb-core.love {
  background: radial-gradient(circle, rgba(255, 99, 132, 0.8) 0%, rgba(255, 99, 132, 0.3) 100%);
}

.orb-ring {
  position: absolute;
  width: 80rpx;
  height: 80rpx;
  border: 2rpx solid rgba(77, 159, 255, 0.5);
  border-radius: 50%;
  animation: orbRotate 3s linear infinite;
}

.orb-ring.love {
  border-color: rgba(255, 99, 132, 0.5);
}

.energy-value, .love-value {
  color: #FFFFFF;
  font-size: 20rpx;
  font-weight: bold;
  text-shadow: 0 0 10rpx rgba(77, 159, 255, 0.8);
}

.energy-label, .love-label {
  color: #FFFFFF;
  font-size: 18rpx;
  opacity: 0.8;
}

@keyframes orbPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes orbRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}



/* 宇宙标语横幅 */
.cosmic-banner {
  position: relative;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 60rpx;
}

.banner-glow {
  position: absolute;
  width: 100%;
  height: 60rpx;
  background: linear-gradient(90deg,
    rgba(77, 159, 255, 0) 0%,
    rgba(77, 159, 255, 0.3) 20%,
    rgba(255, 215, 106, 0.4) 50%,
    rgba(155, 89, 182, 0.3) 80%,
    rgba(155, 89, 182, 0) 100%);
  border-radius: 30rpx;
  filter: blur(8rpx);
  animation: banner-pulse 4s infinite ease-in-out;
}

.banner-content {
  position: relative;
  z-index: 10;
  text-align: center;
  padding: 16rpx 32rpx;
  background: rgba(26, 24, 62, 0.8);
  border: 1rpx solid rgba(255, 215, 106, 0.6);
  border-radius: 20rpx;
  backdrop-filter: blur(10px);
}

.banner-line {
  width: 100%;
  height: 1rpx;
  background: linear-gradient(90deg,
    rgba(255, 215, 106, 0) 0%,
    rgba(255, 215, 106, 0.8) 50%,
    rgba(255, 215, 106, 0) 100%);
}

.banner-line.top {
  margin-bottom: 8rpx;
}

.banner-line.bottom {
  margin-top: 8rpx;
}

.cosmic-slogan {
  font-size: 28rpx;
  font-weight: 600;
  color: #FFD76A;
  text-shadow: 0 2rpx 12rpx rgba(255, 215, 106, 0.6);
  letter-spacing: 3rpx;
  animation: slogan-glow 3s infinite ease-in-out;
}

.banner-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: #FFD76A;
  border-radius: 50%;
  animation: particle-float 6s infinite ease-in-out;
}

.particle.p1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.particle.p2 {
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.particle.p3 {
  bottom: 30%;
  left: 80%;
  animation-delay: 4s;
}

@keyframes banner-pulse {
  0%, 100% { opacity: 0.8; transform: scaleX(1); }
  50% { opacity: 1; transform: scaleX(1.02); }
}

@keyframes particle-float {
  0%, 100% { transform: translateY(0px) scale(1); opacity: 0.6; }
  50% { transform: translateY(-15rpx) scale(1.2); opacity: 1; }
}

@keyframes slogan-glow {
  0%, 100% {
    color: #FFD76A;
    text-shadow: 0 2rpx 12rpx rgba(255, 215, 106, 0.6), 0 0 20rpx rgba(255, 215, 106, 0.3);
    transform: scale(1);
  }
  25% {
    color: #F4C842;
    text-shadow: 0 2rpx 15rpx rgba(244, 200, 66, 0.8), 0 0 30rpx rgba(244, 200, 66, 0.5);
    transform: scale(1.02);
  }
  50% {
    color: #FFE55C;
    text-shadow: 0 2rpx 18rpx rgba(255, 229, 92, 0.9), 0 0 40rpx rgba(255, 229, 92, 0.6);
    transform: scale(1.05);
  }
  75% {
    color: #F4C842;
    text-shadow: 0 2rpx 15rpx rgba(244, 200, 66, 0.8), 0 0 30rpx rgba(244, 200, 66, 0.5);
    transform: scale(1.02);
  }
}

/* 主舷窗区域 */
.main-viewport {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* 宇宙背景 */
.space-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

/* 星空层 */
.stars-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.star {
  position: absolute;
  background: #FFFFFF;
  border-radius: 50%;
  animation: twinkle 3s infinite;
}

.star-1 {
  width: 4rpx;
  height: 4rpx;
  top: 20%;
  left: 15%;
  animation-delay: 0s;
}

.star-2 {
  width: 6rpx;
  height: 6rpx;
  top: 35%;
  right: 20%;
  animation-delay: 1s;
}

.star-3 {
  width: 3rpx;
  height: 3rpx;
  top: 60%;
  left: 25%;
  animation-delay: 2s;
}

.star-4 {
  width: 5rpx;
  height: 5rpx;
  top: 15%;
  right: 35%;
  animation-delay: 0.5s;
}

.star-5 {
  width: 4rpx;
  height: 4rpx;
  bottom: 25%;
  left: 40%;
  animation-delay: 1.5s;
}

.star-6 {
  width: 3rpx;
  height: 3rpx;
  bottom: 40%;
  right: 15%;
  animation-delay: 2.5s;
}

@keyframes twinkle {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* 星云层 */
.nebula-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.nebula {
  position: absolute;
  border-radius: 50%;
  filter: blur(40rpx);
  animation: float 8s infinite ease-in-out;
}

.nebula-1 {
  width: 200rpx;
  height: 200rpx;
  background: radial-gradient(circle, rgba(77, 159, 255, 0.3) 0%, rgba(77, 159, 255, 0) 70%);
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.nebula-2 {
  width: 150rpx;
  height: 150rpx;
  background: radial-gradient(circle, rgba(155, 89, 182, 0.2) 0%, rgba(155, 89, 182, 0) 70%);
  bottom: 20%;
  left: 15%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) translateX(0px); }
  25% { transform: translateY(-20rpx) translateX(10rpx); }
  50% { transform: translateY(-10rpx) translateX(-15rpx); }
  75% { transform: translateY(-30rpx) translateX(5rpx); }
}

/* 流星层 */
.meteor-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 15;
  overflow: hidden;
}

.meteor {
  position: absolute;
  border: 2rpx solid transparent;
  border-bottom: 60rpx solid #fff;
  transform-origin: top center;
  opacity: 0;
}

.meteor::after {
  content: '';
  position: absolute;
  top: 58rpx;
  left: 50%;
  height: 4rpx;
  width: 4rpx;
  border-radius: 100%;
  background: #fff;
  transform: translateX(-50%);
  box-shadow: 0 0 15rpx 0 #fff;
}

/* 流星1 */
.meteor-1 {
  top: 15%;
  left: 20%;
  animation: shower 2s ease-out infinite;
  animation-delay: 3s;
}

/* 流星2 */
.meteor-2 {
  top: 35%;
  left: 60%;
  animation: shower 2s ease-out infinite;
  animation-delay: 8s;
}

/* 流星3 */
.meteor-3 {
  top: 55%;
  left: 10%;
  animation: shower 2s ease-out infinite;
  animation-delay: 15s;
}

/* 流星4 */
.meteor-4 {
  top: 25%;
  left: 80%;
  animation: shower 2s ease-out infinite;
  animation-delay: 22s;
}

/* 流星5 */
.meteor-5 {
  top: 65%;
  left: 45%;
  animation: shower 2s ease-out infinite;
  animation-delay: 30s;
}

/* 流星6 */
.meteor-6 {
  top: 45%;
  left: 75%;
  animation: shower 2s ease-out infinite;
  animation-delay: 38s;
}

/* 专业流星动画 */
@keyframes shower {
  0% {
    transform: rotate(30deg) scaleY(0);
    opacity: 0;
  }
  65% {
    transform: rotate(30deg) scaleY(1);
    opacity: 1;
  }
  100% {
    transform: rotate(30deg) scaleY(1) translateY(500%);
    opacity: 0;
  }
}

/* 全息星图 (思维工坊入口) */
.holographic-starmap {
  position: relative;
  z-index: 50;
  width: 360rpx;
  height: 360rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: hover 4s infinite ease-in-out;
}

.starmap-core {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.starmap-ring {
  position: absolute;
  border: 2rpx solid rgba(77, 159, 255, 0.6);
  border-radius: 50%;
  animation: rotate 10s linear infinite;
}

.ring-1 {
  width: 340rpx;
  height: 340rpx;
  border-style: dashed;
}

.ring-2 {
  width: 300rpx;
  height: 300rpx;
  animation-direction: reverse;
  animation-duration: 15s;
}

.ring-3 {
  width: 260rpx;
  height: 260rpx;
  border-color: rgba(255, 215, 0, 0.4);
  animation-duration: 20s;
}

.starmap-center {
  position: relative;
  z-index: 10;
  width: 240rpx;
  height: 240rpx;
  background: radial-gradient(circle, rgba(77, 159, 255, 0.3) 0%, rgba(77, 159, 255, 0.1) 50%, rgba(77, 159, 255, 0) 100%);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 2rpx solid rgba(77, 159, 255, 0.5);
  box-shadow: 0 0 50rpx rgba(77, 159, 255, 0.4);
}

.center-glow {
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  animation: centerPulse 3s ease-in-out infinite;
}

.progress-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
}

.progress-fill {
  position: absolute;
  top: 0;
  left: 50%;
  width: 50%;
  height: 100%;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 215, 0, 0.6) 100%);
  transform-origin: left center;
  transition: transform 0.3s ease;
}

.starmap-satellites {
  position: absolute;
  width: 100%;
  height: 100%;
}

.satellite {
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  animation: satelliteOrbit 15s linear infinite;
}

.sat-1 { transform: rotate(0deg) translateX(140rpx) rotate(0deg); }
.sat-2 { transform: rotate(72deg) translateX(140rpx) rotate(-72deg); }
.sat-3 { transform: rotate(144deg) translateX(140rpx) rotate(-144deg); }
.sat-4 { transform: rotate(216deg) translateX(140rpx) rotate(-216deg); }
.sat-5 { transform: rotate(288deg) translateX(140rpx) rotate(-288deg); }

@keyframes centerPulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
}

@keyframes satelliteOrbit {
  from { transform: rotate(0deg) translateX(140rpx) rotate(0deg); }
  to { transform: rotate(360deg) translateX(140rpx) rotate(-360deg); }
}

.starmap-icon {
  font-size: 56rpx;
  margin-bottom: 10rpx;
}

.starmap-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #FFFFFF;
  text-shadow: 0 2rpx 8rpx rgba(77, 159, 255, 0.5);
}

.starmap-subtitle {
  font-size: 20rpx;
  color: #FFFFFF;
  opacity: 0.8;
  margin-top: 6rpx;
}

@keyframes hover {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10rpx); }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 宇宙灯塔星云 */
.cosmic-lighthouse {
  position: absolute;
  top: 10%;
  right: 6%;
  z-index: 40;
  width: 220rpx;
  height: 220rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  animation: pulse 3s infinite ease-in-out;
}

.lighthouse-glow {
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 215, 106, 0.4) 0%, rgba(255, 215, 106, 0) 70%);
  border-radius: 50%;
  animation: glow 2s infinite ease-in-out;
}

.lighthouse-beam {
  position: absolute;
  top: 0;
  left: 50%;
  width: 4rpx;
  height: 100%;
  background: linear-gradient(0deg,
    rgba(255, 215, 106, 0) 0%,
    rgba(255, 215, 106, 0.8) 30%,
    rgba(255, 215, 106, 0.6) 70%,
    rgba(255, 215, 106, 0) 100%);
  transform: translateX(-50%);
  animation: beamSweep 4s linear infinite;
}

.lighthouse-core {
  position: relative;
  z-index: 10;
  width: 150rpx;
  height: 150rpx;
  background: radial-gradient(circle, rgba(255, 215, 106, 0.6) 0%, rgba(255, 215, 106, 0.2) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid rgba(255, 215, 106, 0.8);
  box-shadow: 0 0 40rpx rgba(255, 215, 106, 0.6);
}

.lighthouse-inner {
  position: relative;
  z-index: 15;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lighthouse-rings {
  position: absolute;
  width: 100%;
  height: 100%;
}

.lighthouse-rings .ring {
  position: absolute;
  border: 1rpx solid rgba(255, 215, 106, 0.4);
  border-radius: 50%;
  animation: ringExpand 3s ease-out infinite;
}

.lighthouse-rings .r1 {
  width: 120%;
  height: 120%;
  top: -10%;
  left: -10%;
  animation-delay: 0s;
}

.lighthouse-rings .r2 {
  width: 140%;
  height: 140%;
  top: -20%;
  left: -20%;
  animation-delay: 1s;
}

.lighthouse-rings .r3 {
  width: 160%;
  height: 160%;
  top: -30%;
  left: -30%;
  animation-delay: 2s;
}

.lighthouse-status {
  position: absolute;
  bottom: -40rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 215, 106, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 215, 106, 0.3);
}

.status-text {
  color: #FFD76A;
  font-size: 18rpx;
  font-weight: 500;
}

@keyframes beamSweep {
  0% { transform: translateX(-50%) rotate(0deg); opacity: 0.3; }
  50% { opacity: 1; }
  100% { transform: translateX(-50%) rotate(360deg); opacity: 0.3; }
}

@keyframes ringExpand {
  0% {
    opacity: 0.8;
    transform: scale(0.8);
  }
  100% {
    opacity: 0;
    transform: scale(1.2);
  }
}

.lighthouse-icon {
  font-size: 60rpx;
}

.lighthouse-label {
  font-size: 24rpx;
  color: #FFFFFF;
  opacity: 0.9;
  margin-top: 16rpx;
  text-align: center;
  font-weight: 600;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.5);
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes glow {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* 能量水晶 - 船长舱室 */
.energy-crystal {
  position: absolute;
  width: 200rpx;
  height: 220rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.4s ease;
  z-index: 40;
  bottom: 15%;
  left: 8%;
}

.energy-crystal:active {
  transform: scale(0.95);
}

.crystal-base {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  margin-top: 30rpx;
}

.crystal-facet {
  position: absolute;
  background: linear-gradient(135deg, rgba(255, 215, 106, 0.8) 0%, rgba(245, 166, 35, 0.4) 100%);
  border: 1rpx solid rgba(255, 215, 106, 0.6);
  animation: crystal-shimmer 3s infinite ease-in-out;
}

.crystal-facet.f1 {
  width: 60rpx;
  height: 80rpx;
  top: 0;
  left: 30rpx;
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  animation-delay: 0s;
}

.crystal-facet.f2 {
  width: 50rpx;
  height: 70rpx;
  top: 15rpx;
  left: 10rpx;
  clip-path: polygon(0% 0%, 100% 50%, 0% 100%);
  animation-delay: 0.5s;
}

.crystal-facet.f3 {
  width: 50rpx;
  height: 70rpx;
  top: 15rpx;
  right: 10rpx;
  clip-path: polygon(100% 0%, 0% 50%, 100% 100%);
  animation-delay: 1s;
}

.crystal-facet.f4 {
  width: 80rpx;
  height: 40rpx;
  bottom: 0;
  left: 20rpx;
  clip-path: polygon(0% 0%, 100% 0%, 50% 100%);
  animation-delay: 1.5s;
}

.crystal-core {
  position: absolute;
  top: 50rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 40rpx;
  background: radial-gradient(circle, rgba(255, 215, 106, 0.9) 0%, rgba(255, 215, 106, 0.3) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  box-shadow: 0 0 30rpx rgba(255, 215, 106, 0.8);
}

.crystal-icon {
  font-size: 24rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.3));
}

.crystal-energy-flow {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 30rpx;
}

.energy-beam {
  position: absolute;
  width: 2rpx;
  height: 40rpx;
  background: linear-gradient(0deg, rgba(255, 215, 106, 0) 0%, rgba(255, 215, 106, 0.8) 50%, rgba(255, 215, 106, 0) 100%);
  animation: energy-flow 2s infinite ease-in-out;
}

.energy-beam.b1 {
  top: 20rpx;
  left: 20rpx;
  animation-delay: 0s;
}

.energy-beam.b2 {
  top: 30rpx;
  right: 25rpx;
  animation-delay: 0.7s;
}

.energy-beam.b3 {
  bottom: 20rpx;
  left: 50%;
  animation-delay: 1.4s;
}

.crystal-label {
  font-size: 26rpx;
  font-weight: 600;
  color: #FFD76A;
  text-align: center;
  margin-top: 20rpx;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.5);
}

@keyframes crystal-shimmer {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.05); }
}

@keyframes energy-flow {
  0%, 100% { opacity: 0.3; transform: translateY(0px); }
  50% { opacity: 1; transform: translateY(-10rpx); }
}

/* 数据终端 - 今日任务 */
.data-terminal {
  position: absolute;
  width: 200rpx;
  height: 220rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.4s ease;
  z-index: 40;
  bottom: 20%;
  right: 10%;
}

.data-terminal:active {
  transform: scale(0.95);
}

.terminal-screen {
  position: relative;
  width: 140rpx;
  height: 100rpx;
  margin-top: 30rpx;
  background: linear-gradient(135deg, rgba(99, 226, 183, 0.2) 0%, rgba(45, 181, 146, 0.1) 100%);
  border: 2rpx solid rgba(99, 226, 183, 0.6);
  border-radius: 8rpx;
  overflow: hidden;
}

.screen-glow {
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(99, 226, 183, 0.3) 0%, rgba(99, 226, 183, 0) 70%);
  animation: screen-flicker 4s infinite ease-in-out;
}

.screen-content {
  position: relative;
  z-index: 10;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12rpx;
}

.data-line {
  width: 80%;
  height: 2rpx;
  background: linear-gradient(90deg, rgba(99, 226, 183, 0) 0%, rgba(99, 226, 183, 0.8) 50%, rgba(99, 226, 183, 0) 100%);
  margin: 3rpx 0;
  animation: data-scroll 2s infinite ease-in-out;
}

.data-line.l1 {
  width: 90%;
  animation-delay: 0s;
}

.data-line.l2 {
  width: 70%;
  animation-delay: 0.3s;
}

.data-line.l3 {
  width: 85%;
  animation-delay: 0.6s;
}

.terminal-icon {
  font-size: 32rpx;
  margin-top: 8rpx;
  filter: drop-shadow(0 0 8rpx rgba(99, 226, 183, 0.6));
}

.terminal-base {
  width: 160rpx;
  height: 20rpx;
  background: linear-gradient(135deg, rgba(99, 226, 183, 0.4) 0%, rgba(45, 181, 146, 0.2) 100%);
  border: 1rpx solid rgba(99, 226, 183, 0.5);
  border-radius: 0 0 12rpx 12rpx;
  margin-top: -2rpx;
}

.terminal-label {
  font-size: 26rpx;
  font-weight: 600;
  color: #63E2B7;
  text-align: center;
  margin-top: 20rpx;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.5);
}

@keyframes screen-flicker {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
  95% { opacity: 0.9; }
  96% { opacity: 0.7; }
  97% { opacity: 1; }
}

@keyframes data-scroll {
  0%, 100% { opacity: 0.4; transform: translateX(-10rpx); }
  50% { opacity: 1; transform: translateX(10rpx); }
}

/* 传送门 - 地球指挥部 */
.portal-gate {
  position: absolute;
  width: 320rpx;
  height: 340rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.4s ease;
  z-index: 40;
  top: 20%;
  left: -1%;
}

.portal-gate:active {
  transform: scale(0.95);
}

.portal-ring {
  position: absolute;
  border-radius: 50%;
  border: 2rpx solid;
  top: 30rpx;
  left: 50%;
  transform: translateX(-50%);
}

.portal-ring.outer {
  width: 240rpx;
  height: 240rpx;
  border-color: rgba(116, 185, 255, 0.4);
  animation: portal-rotate 8s linear infinite;
}

.portal-ring.middle {
  width: 190rpx;
  height: 190rpx;
  top: 55rpx;
  border-color: rgba(116, 185, 255, 0.6);
  border-style: dashed;
  animation: portal-rotate 6s linear infinite reverse;
}

.portal-ring.inner {
  width: 140rpx;
  height: 140rpx;
  top: 80rpx;
  border-color: rgba(116, 185, 255, 0.8);
  animation: portal-rotate 4s linear infinite;
}

.portal-core {
  position: absolute;
  top: 105rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 110rpx;
  height: 110rpx;
  background: radial-gradient(circle, rgba(116, 185, 255, 0.8) 0%, rgba(116, 185, 255, 0.2) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  box-shadow: 0 0 70rpx rgba(116, 185, 255, 0.8);
}

.portal-vortex {
  position: absolute;
  width: 100%;
  height: 100%;
  background: conic-gradient(from 0deg, rgba(116, 185, 255, 0) 0%, rgba(116, 185, 255, 0.6) 50%, rgba(116, 185, 255, 0) 100%);
  border-radius: 50%;
  animation: vortex-spin 2s linear infinite;
}

.portal-energy {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 30rpx;
}

.energy-spark {
  position: absolute;
  width: 10rpx;
  height: 10rpx;
  background: #74B9FF;
  border-radius: 50%;
  animation: spark-dance 3s infinite ease-in-out;
  box-shadow: 0 0 18rpx rgba(116, 185, 255, 0.8);
}

.energy-spark.s1 {
  top: 50rpx;
  left: 60rpx;
  animation-delay: 0s;
}

.energy-spark.s2 {
  top: 100rpx;
  right: 50rpx;
  animation-delay: 0.75s;
}

.energy-spark.s3 {
  bottom: 60rpx;
  left: 100rpx;
  animation-delay: 1.5s;
}

.energy-spark.s4 {
  top: 140rpx;
  left: 40rpx;
  animation-delay: 2.25s;
}

.portal-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #74B9FF;
  text-align: center;
  margin-top: 35rpx;
  text-shadow: 0 2rpx 8rpx rgba(116, 185, 255, 0.6);
}

@keyframes portal-rotate {
  from { transform: translateX(-50%) rotate(0deg); }
  to { transform: translateX(-50%) rotate(360deg); }
}

@keyframes vortex-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes spark-dance {
  0%, 100% { transform: scale(1) translateY(0px); opacity: 0.6; }
  25% { transform: scale(1.2) translateY(-15rpx); opacity: 1; }
  50% { transform: scale(0.8) translateY(-8rpx); opacity: 0.8; }
  75% { transform: scale(1.1) translateY(-20rpx); opacity: 1; }
}

/* 底部控制台网格线 */
.console-grid-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.grid-line {
  position: absolute;
  background: rgba(77, 159, 255, 0.1);
}

.grid-line.horizontal {
  width: 100%;
  height: 1rpx;
}

.grid-line.vertical {
  width: 1rpx;
  height: 100%;
}

.grid-line.h1 { top: 33.33%; }
.grid-line.h2 { top: 66.66%; }
.grid-line.v1 { left: 33.33%; }
.grid-line.v2 { left: 66.66%; }

/* 模块边框效果 */
.module-border {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 1rpx solid rgba(77, 159, 255, 0.2);
  border-radius: 20rpx;
  z-index: 1;
  pointer-events: none;
}

/* 图标容器 */
.icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
}

.icon-pulse {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(77, 159, 255, 0.2);
  animation: iconPulse 2s ease-in-out infinite;
}

.module-subtitle {
  color: rgba(255, 255, 255, 0.6);
  font-size: 20rpx;
  margin-top: 5rpx;
}

.status-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 18rpx;
  margin-left: 8rpx;
}

/* 模块进度条 */
.module-progress {
  position: absolute;
  bottom: 10rpx;
  left: 20rpx;
  right: 20rpx;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2rpx;
  overflow: hidden;
}

.progress-bar {
  width: 100%;
  height: 100%;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, rgba(77, 159, 255, 0.6), rgba(77, 159, 255, 1));
  border-radius: 2rpx;
  transition: width 0.3s ease;
}

@keyframes iconPulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.1); }
}







