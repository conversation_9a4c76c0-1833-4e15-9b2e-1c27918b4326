/* 《能量星球》船长私人舱室 - 世界500强级别UI设计 */

/* ==================== CSS变量系统 ==================== */
page {
  /* 深空紫色渐变系统 */
  --primary-gradient: linear-gradient(135deg, #1A0B3D 0%, #2D1B69 50%, #4A148C 100%);
  --primary-dark: #1A0B3D;
  --primary-medium: #2D1B69;
  --primary-light: #4A148C;
  
  /* 水晶蓝色系统 */
  --crystal-blue: #00E5FF;
  --crystal-blue-light: #62EFFF;
  --crystal-blue-dark: #00B8D4;
  --crystal-blue-glow: rgba(0, 229, 255, 0.3);
  
  /* 全息绿色系统 */
  --hologram-green: #00FF88;
  --hologram-green-light: #69F0AE;
  --hologram-green-dark: #00BFA5;
  --hologram-green-glow: rgba(0, 255, 136, 0.3);
  
  /* 能量金色系统 */
  --energy-gold: #FFD700;
  --energy-gold-light: #FFECB3;
  --energy-gold-dark: #FF8F00;
  --energy-gold-glow: rgba(255, 215, 0, 0.3);
  
  /* 量子红色系统 */
  --quantum-red: #FF1744;
  --quantum-red-light: #FF5983;
  --quantum-red-dark: #C51162;
  
  /* 中性色系统 */
  --space-gray: #37474F;
  --space-gray-light: #546E7A;
  --space-gray-dark: #263238;
  
  /* 透明度系统 */
  --alpha-high: 0.9;
  --alpha-medium: 0.6;
  --alpha-low: 0.3;
  --alpha-subtle: 0.1;
  
  /* 阴影系统 */
  --shadow-subtle: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
  --shadow-strong: 0 16rpx 48rpx rgba(0, 0, 0, 0.3);
  --shadow-dramatic: 0 32rpx 64rpx rgba(0, 0, 0, 0.4);
  
  /* 动画时长系统 */
  --duration-fast: 0.2s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;
  --duration-dramatic: 1s;
  
  /* 缓动函数系统 */
  --ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
  --ease-in-out-quart: cubic-bezier(0.76, 0, 0.24, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ==================== 基础页面样式 ==================== */
.page {
  width: 100vw;
  height: 100vh;
  background: var(--primary-gradient);
  overflow: hidden;
  position: relative;
}

.holographic-quarters {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* ==================== 全息激活系统 ==================== */
.hologram-activation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  pointer-events: none;
}

.scan-line {
  position: absolute;
  top: -4rpx;
  left: 0;
  width: 100%;
  height: 4rpx;
  background: linear-gradient(90deg, 
    transparent 0%, 
    var(--crystal-blue) 50%, 
    transparent 100%
  );
  box-shadow: 0 0 20rpx var(--crystal-blue-glow);
  animation: scanActivation 2.5s ease-out forwards;
}

@keyframes scanActivation {
  0% { 
    top: -4rpx; 
    opacity: 0; 
  }
  10% { 
    opacity: 1; 
  }
  90% { 
    opacity: 1; 
  }
  100% { 
    top: 100vh; 
    opacity: 0; 
  }
}

.activation-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  animation: gridActivation 1.5s ease-out 0.5s forwards;
}

@keyframes gridActivation {
  0% { opacity: 0; }
  100% { opacity: var(--alpha-subtle); }
}

.grid-line {
  position: absolute;
  background: var(--hologram-green);
  box-shadow: 0 0 10rpx var(--hologram-green-glow);
}

.grid-line.horizontal {
  width: 100%;
  height: 2rpx;
}

.grid-line.vertical {
  width: 2rpx;
  height: 100%;
}

.grid-line.h1 { top: 25%; }
.grid-line.h2 { top: 50%; }
.grid-line.h3 { top: 75%; }
.grid-line.v1 { left: 25%; }
.grid-line.v2 { left: 50%; }
.grid-line.v3 { left: 75%; }

/* ==================== 深空背景系统 ==================== */
.deep-space-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.nebula-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.nebula {
  position: absolute;
  border-radius: 50%;
  filter: blur(60rpx);
  opacity: var(--alpha-low);
  animation: nebulaFloat 20s ease-in-out infinite;
}

.nebula-1 {
  width: 400rpx;
  height: 400rpx;
  top: 10%;
  left: -10%;
  background: radial-gradient(circle, var(--crystal-blue) 0%, transparent 70%);
  animation-delay: 0s;
}

.nebula-2 {
  width: 600rpx;
  height: 600rpx;
  top: 40%;
  right: -15%;
  background: radial-gradient(circle, var(--primary-light) 0%, transparent 70%);
  animation-delay: -7s;
}

.nebula-3 {
  width: 300rpx;
  height: 300rpx;
  bottom: 20%;
  left: 20%;
  background: radial-gradient(circle, var(--hologram-green) 0%, transparent 70%);
  animation-delay: -14s;
}

@keyframes nebulaFloat {
  0%, 100% { 
    transform: translate(0, 0) rotate(0deg); 
  }
  25% { 
    transform: translate(20rpx, -30rpx) rotate(90deg); 
  }
  50% { 
    transform: translate(-10rpx, -20rpx) rotate(180deg); 
  }
  75% { 
    transform: translate(-30rpx, 10rpx) rotate(270deg); 
  }
}

.starfield-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.star {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: white;
  border-radius: 50%;
  box-shadow: 0 0 10rpx rgba(255, 255, 255, 0.8);
  animation: starTwinkle 3s ease-in-out infinite;
}

.star-1 { top: 15%; left: 20%; animation-delay: 0s; }
.star-2 { top: 25%; left: 80%; animation-delay: 0.5s; }
.star-3 { top: 45%; left: 15%; animation-delay: 1s; }
.star-4 { top: 35%; left: 70%; animation-delay: 1.5s; }
.star-5 { top: 65%; left: 25%; animation-delay: 2s; }
.star-6 { top: 75%; left: 85%; animation-delay: 2.5s; }
.star-7 { top: 85%; left: 45%; animation-delay: 3s; }
.star-8 { top: 55%; left: 60%; animation-delay: 3.5s; }

@keyframes starTwinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.5);
  }
}

/* ==================== 舰长信息HUD ==================== */
.captain-hud {
  position: absolute;
  top: 60rpx;
  left: 0;
  right: 0;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40rpx;
  height: 80rpx;
  background: linear-gradient(90deg,
    rgba(0, 229, 255, 0.05) 0%,
    rgba(0, 229, 255, 0.1) 20%,
    rgba(0, 255, 136, 0.1) 50%,
    rgba(255, 215, 0, 0.1) 80%,
    rgba(255, 215, 0, 0.05) 100%
  );
  border-top: 1rpx solid rgba(0, 229, 255, 0.3);
  border-bottom: 1rpx solid rgba(255, 215, 0, 0.3);
  backdrop-filter: blur(20rpx);
  animation: hudScanline 4s linear infinite;
}

.captain-hud::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(0, 229, 255, 0.3) 50%,
    transparent 100%
  );
  animation: hudSweep 6s linear infinite;
}

@keyframes hudScanline {
  0%, 100% {
    border-top-color: rgba(0, 229, 255, 0.3);
    border-bottom-color: rgba(255, 215, 0, 0.3);
  }
  50% {
    border-top-color: rgba(0, 229, 255, 0.8);
    border-bottom-color: rgba(255, 215, 0, 0.8);
  }
}

@keyframes hudSweep {
  0% { left: -100%; }
  100% { left: 100%; }
}

.hud-left, .hud-center, .hud-right {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.captain-rank {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: linear-gradient(135deg,
    rgba(0, 229, 255, 0.2) 0%,
    rgba(0, 229, 255, 0.05) 50%,
    rgba(0, 229, 255, 0.2) 100%
  );
  border: 2rpx solid var(--crystal-blue);
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  backdrop-filter: blur(15rpx);
  animation: rankGlow 3s ease-in-out infinite;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 0 20rpx rgba(0, 229, 255, 0.3),
    inset 0 0 20rpx rgba(0, 229, 255, 0.1);
}

.captain-rank::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(0, 229, 255, 0.3), transparent);
  animation: rankScan 4s linear infinite;
}

@keyframes rankGlow {
  0%, 100% {
    box-shadow: 0 0 10rpx var(--crystal-blue-glow);
    border-color: var(--crystal-blue);
  }
  50% {
    box-shadow: 0 0 25rpx var(--crystal-blue-glow), 0 0 40rpx rgba(0, 229, 255, 0.3);
    border-color: var(--crystal-blue-light);
  }
}

@keyframes rankScan {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.rank-icon {
  font-size: 24rpx;
  color: var(--energy-gold);
  filter: drop-shadow(0 0 8rpx var(--energy-gold-glow));
}

.rank-text {
  font-size: 20rpx;
  color: white;
  font-weight: bold;
}

.captain-title {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12rpx;
  padding: 6rpx 12rpx;
}

.ship-status {
  display: flex;
  align-items: center;
  gap: 12rpx;
  background: linear-gradient(135deg,
    rgba(0, 255, 136, 0.2) 0%,
    rgba(0, 255, 136, 0.05) 50%,
    rgba(0, 255, 136, 0.2) 100%
  );
  border: 2rpx solid var(--hologram-green);
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  backdrop-filter: blur(15rpx);
  animation: shipStatusPulse 2.5s ease-in-out infinite;
  position: relative;
  box-shadow:
    0 0 20rpx rgba(0, 255, 136, 0.3),
    inset 0 0 20rpx rgba(0, 255, 136, 0.1);
}

.ship-status::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 20rpx;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(0, 255, 136, 0.2) 50%,
    transparent 100%
  );
  animation: statusSweep 3s linear infinite;
}

@keyframes shipStatusPulse {
  0%, 100% {
    box-shadow: 0 0 8rpx var(--hologram-green-glow);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 20rpx var(--hologram-green-glow), 0 0 35rpx rgba(0, 255, 136, 0.4);
    transform: scale(1.02);
  }
}

@keyframes statusSweep {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.status-text {
  font-size: 18rpx;
  color: white;
  font-weight: bold;
}

.status-indicator {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: var(--hologram-green);
  box-shadow: 0 0 10rpx var(--hologram-green-glow);
  animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.2); }
}

.energy-status {
  display: flex;
  gap: 16rpx;
}

.energy-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: rgba(255, 255, 255, 0.05);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 6rpx 12rpx;
  backdrop-filter: blur(10rpx);
  animation: energyFlicker 2s ease-in-out infinite;
  position: relative;
  overflow: hidden;
}

.energy-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%
  );
  animation: energyFlow 3s linear infinite;
}

.energy-item:nth-child(1) {
  animation-delay: 0s;
}

.energy-item:nth-child(2) {
  animation-delay: 1s;
}

@keyframes energyFlicker {
  0%, 100% {
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 0 5rpx rgba(255, 255, 255, 0.1);
  }
  50% {
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 15rpx rgba(255, 255, 255, 0.3);
  }
}

@keyframes energyFlow {
  0% { left: -100%; }
  100% { left: 100%; }
}

.energy-icon {
  font-size: 20rpx;
  filter: drop-shadow(0 0 6rpx currentColor);
}

.energy-value {
  font-size: 18rpx;
  color: white;
  font-weight: bold;
}

/* ==================== 装饰性全息元素 ==================== */
.hologram-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 3;
  pointer-events: none;
}

/* 全息几何体 */
.holo-geometry {
  position: absolute;
  width: 120rpx;
  height: 120rpx;
  animation: holoFloat 8s ease-in-out infinite;
}

.holo-1 {
  top: 15%;
  left: 10%;
  animation-delay: 0s;
}

.holo-2 {
  top: 20%;
  right: 15%;
  animation-delay: 2s;
}

.holo-3 {
  top: 45%;
  left: 5%;
  animation-delay: 4s;
}

.holo-4 {
  top: 50%;
  right: 8%;
  animation-delay: 6s;
}

.geo-core {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20rpx;
  height: 20rpx;
  background: var(--crystal-blue);
  border-radius: 50%;
  box-shadow:
    0 0 20rpx var(--crystal-blue-glow),
    0 0 40rpx rgba(0, 229, 255, 0.3);
  animation: coreGlow 3s ease-in-out infinite;
}

.geo-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2rpx solid rgba(0, 229, 255, 0.4);
  border-radius: 50%;
  animation: ringRotate 6s linear infinite;
}

.geo-ring.ring-1 {
  width: 60rpx;
  height: 60rpx;
  animation-duration: 6s;
}

.geo-ring.ring-2 {
  width: 100rpx;
  height: 100rpx;
  animation-duration: 8s;
  animation-direction: reverse;
}

.geo-particle {
  position: absolute;
  width: 6rpx;
  height: 6rpx;
  background: var(--hologram-green);
  border-radius: 50%;
  box-shadow: 0 0 10rpx var(--hologram-green-glow);
  animation: particleOrbit 4s linear infinite;
}

.geo-particle.particle-1 {
  top: 20%;
  left: 50%;
  animation-delay: 0s;
}

.geo-particle.particle-2 {
  top: 50%;
  right: 20%;
  animation-delay: 1.3s;
}

.geo-particle.particle-3 {
  bottom: 20%;
  left: 50%;
  animation-delay: 2.6s;
}

/* 数据流线 */
.data-streams {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.stream {
  position: absolute;
  height: 2rpx;
  background: linear-gradient(90deg,
    transparent 0%,
    var(--crystal-blue) 20%,
    var(--hologram-green) 50%,
    var(--energy-gold) 80%,
    transparent 100%
  );
  box-shadow: 0 0 10rpx currentColor;
  animation: streamFlow 5s linear infinite;
}

.stream-1 {
  top: 30%;
  left: 0;
  width: 100%;
  animation-delay: 0s;
}

.stream-2 {
  top: 60%;
  left: 0;
  width: 100%;
  animation-delay: 1.5s;
}

.stream-3 {
  top: 80%;
  left: 0;
  width: 100%;
  animation-delay: 3s;
}

/* 全息几何体动画 */
@keyframes holoFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-20rpx) rotate(90deg);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-40rpx) rotate(180deg);
    opacity: 1;
  }
  75% {
    transform: translateY(-20rpx) rotate(270deg);
    opacity: 0.8;
  }
}

@keyframes coreGlow {
  0%, 100% {
    box-shadow:
      0 0 20rpx var(--crystal-blue-glow),
      0 0 40rpx rgba(0, 229, 255, 0.3);
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    box-shadow:
      0 0 40rpx var(--crystal-blue-glow),
      0 0 80rpx rgba(0, 229, 255, 0.6);
    transform: translate(-50%, -50%) scale(1.2);
  }
}

@keyframes ringRotate {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes particleOrbit {
  0% {
    transform: rotate(0deg) translateX(40rpx) rotate(0deg);
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    transform: rotate(360deg) translateX(40rpx) rotate(-360deg);
    opacity: 1;
  }
}

@keyframes streamFlow {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* ==================== 中央全息投影台 ==================== */
.hologram-center {
  position: relative;
  z-index: 5;
  width: 300rpx;
  height: 300rpx;
  margin: 20rpx 0;
  opacity: 0;
  transform: translateY(50rpx);
  animation: hologramActivation 1s ease-out 1.6s forwards;
}

@keyframes hologramActivation {
  0% {
    opacity: 0;
    transform: translateY(50rpx) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.projection-platform {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.platform-base {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 200rpx;
  height: 40rpx;
}

.base-ring {
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  border: 2rpx solid var(--crystal-blue);
  border-radius: 50%;
  box-shadow: 0 0 20rpx var(--crystal-blue-glow);
  animation: baseRingPulse 3s ease-in-out infinite;
}

.base-ring.ring-1 {
  width: 200rpx;
  height: 40rpx;
  animation-delay: 0s;
}

.base-ring.ring-2 {
  width: 160rpx;
  height: 32rpx;
  animation-delay: 1s;
}

.base-ring.ring-3 {
  width: 120rpx;
  height: 24rpx;
  animation-delay: 2s;
}

@keyframes baseRingPulse {
  0%, 100% {
    opacity: 0.6;
    transform: translateX(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) scale(1.1);
  }
}

.captain-hologram {
  position: relative;
  width: 160rpx;
  height: 200rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: transform var(--duration-normal) var(--ease-out-quart);
}

.captain-hologram:hover {
  transform: scale(1.05);
}

.hologram-avatar {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3rpx solid var(--crystal-blue);
  border-radius: 50%;
  background: radial-gradient(circle,
    rgba(0, 229, 255, 0.1) 0%,
    rgba(0, 229, 255, 0.05) 50%,
    transparent 100%
  );
  box-shadow:
    0 0 30rpx var(--crystal-blue-glow),
    inset 0 0 20rpx rgba(0, 229, 255, 0.1);
  animation: hologramFlicker 4s ease-in-out infinite;
}

@keyframes hologramFlicker {
  0%, 100% {
    opacity: 1;
    box-shadow:
      0 0 30rpx var(--crystal-blue-glow),
      inset 0 0 20rpx rgba(0, 229, 255, 0.1);
  }
  50% {
    opacity: 0.8;
    box-shadow:
      0 0 40rpx var(--crystal-blue-glow),
      inset 0 0 30rpx rgba(0, 229, 255, 0.2);
  }
}

.avatar-icon {
  font-size: 60rpx;
  filter: drop-shadow(0 0 10rpx var(--crystal-blue));
}

.hologram-glitch {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(0, 229, 255, 0.1) 50%,
    transparent 100%
  );
  animation: glitchSweep 8s ease-in-out infinite;
}

@keyframes glitchSweep {
  0%, 90%, 100% {
    transform: translateX(-100%);
    opacity: 0;
  }
  5%, 85% {
    transform: translateX(100%);
    opacity: 1;
  }
}

/* ==================== 双能量光环系统 ==================== */
.energy-rings {
  position: absolute;
  top: -50rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 200rpx;
  height: 200rpx;
  pointer-events: none;
}

.energy-ring {
  position: absolute;
  width: 180rpx;
  height: 180rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.energy-ring.wisdom {
  top: -40rpx;
  left: -60rpx;
  border: 3rpx solid var(--crystal-blue);
  background: radial-gradient(circle,
    rgba(0, 229, 255, 0.1) 0%,
    transparent 70%
  );
  animation: wisdomRingRotate 10s linear infinite;
}

.energy-ring.love {
  bottom: -40rpx;
  right: -60rpx;
  border: 3rpx solid #FF69B4;
  background: radial-gradient(circle,
    rgba(255, 105, 180, 0.1) 0%,
    transparent 70%
  );
  animation: loveRingRotate 12s linear infinite reverse;
}

@keyframes wisdomRingRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes loveRingRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.ring-glow {
  position: absolute;
  top: -6rpx;
  left: -6rpx;
  width: calc(100% + 12rpx);
  height: calc(100% + 12rpx);
  border-radius: 50%;
  opacity: 0.6;
  animation: ringGlow 2s ease-in-out infinite alternate;
}

.wisdom .ring-glow {
  box-shadow: 0 0 30rpx var(--crystal-blue-glow);
}

.love .ring-glow {
  box-shadow: 0 0 30rpx rgba(255, 105, 180, 0.3);
}

@keyframes ringGlow {
  0% { opacity: 0.4; }
  100% { opacity: 0.8; }
}

/* 能量数值和标签已移除 - 数据显示在HUD中 */

/* ==================== 等级徽章系统 ==================== */
.level-badge {
  position: absolute;
  top: -30rpx;
  right: -30rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: radial-gradient(circle,
    var(--energy-gold) 0%,
    var(--energy-gold-dark) 100%
  );
  border: 3rpx solid var(--energy-gold-light);
  border-radius: 50%;
  box-shadow:
    0 0 20rpx var(--energy-gold-glow),
    inset 0 0 10rpx rgba(255, 215, 0, 0.3);
  animation: badgeFloat 3s ease-in-out infinite;
}

@keyframes badgeFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-10rpx) rotate(180deg);
  }
}

.badge-glow {
  position: absolute;
  top: -6rpx;
  left: -6rpx;
  width: calc(100% + 12rpx);
  height: calc(100% + 12rpx);
  border-radius: 50%;
  box-shadow: 0 0 30rpx var(--energy-gold-glow);
  animation: badgeGlow 2s ease-in-out infinite alternate;
}

@keyframes badgeGlow {
  0% { opacity: 0.6; }
  100% { opacity: 1; }
}

/* 等级数字和标题已移除 - 信息显示在HUD中 */

/* ==================== 四象限功能区域 ==================== */
.function-quadrants {
  position: relative;
  z-index: 4;
  width: 100%;
  max-width: 700rpx;
  height: 500rpx;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 30rpx;
  padding: 0 40rpx;
  opacity: 0;
  transform: translateY(30rpx);
  animation: quadrantsActivation 1s ease-out 1.1s forwards;
}

@keyframes quadrantsActivation {
  0% {
    opacity: 0;
    transform: translateY(30rpx);
    margin-top: 0;
  }
  100% {
    opacity: 1;
    transform: translateY(0);
    margin-top: 60rpx;
  }
}

.quadrant {
  position: relative;
  border-radius: 24rpx;
  overflow: hidden;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out-quart);
  transform: translateZ(0); /* 启用硬件加速 */
}

.quadrant:hover {
  transform: translateY(-8rpx) scale(1.02);
}

.quadrant:active {
  transform: translateY(-4rpx) scale(0.98);
}

.quadrant-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.quadrant-content {
  position: relative;
  z-index: 3;
  width: 100%;
  height: 100%;
  padding: 24rpx;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  color: white;
  box-sizing: border-box;
}

.quadrant-glow {
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  width: calc(100% + 8rpx);
  height: calc(100% + 8rpx);
  border-radius: 28rpx;
  z-index: 2;
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-out-quart);
}

.quadrant:hover .quadrant-glow {
  opacity: 1;
}

.function-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  filter: drop-shadow(0 0 10rpx currentColor);
  animation: iconFloat 3s ease-in-out infinite;
  text-align: center !important;
  width: 100%;
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-6rpx); }
}

.function-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  text-align: center !important;
  width: 100%;
}

.function-subtitle {
  font-size: 22rpx;
  opacity: 0.8;
  margin-bottom: 16rpx;
  line-height: 1.3;
  text-align: center !important;
  width: 100%;
}

/* ==================== 愿望合成器样式 ==================== */
.wish-synthesizer {
  background: linear-gradient(135deg,
    rgba(156, 39, 176, 0.9) 0%,
    rgba(74, 20, 140, 0.9) 100%
  );
  border: 2rpx solid rgba(156, 39, 176, 0.5);
}

.wish-synthesizer .quadrant-glow {
  box-shadow: 0 0 40rpx rgba(156, 39, 176, 0.6);
}

.nebula-vortex {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200rpx;
  height: 200rpx;
}

.vortex-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2rpx solid rgba(156, 39, 176, 0.6);
  border-radius: 50%;
  animation: vortexSpin 8s linear infinite;
}

.vortex-ring.ring-1 {
  width: 200rpx;
  height: 200rpx;
  animation-duration: 8s;
}

.vortex-ring.ring-2 {
  width: 150rpx;
  height: 150rpx;
  animation-duration: 6s;
  animation-direction: reverse;
}

.vortex-ring.ring-3 {
  width: 100rpx;
  height: 100rpx;
  animation-duration: 4s;
}

@keyframes vortexSpin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.energy-cost {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8rpx;
  font-size: 20rpx;
  opacity: 0.9;
  width: 100%;
  text-align: center !important;
}

.cost-value {
  font-weight: bold;
  color: var(--crystal-blue);
}

.cost-unit {
  opacity: 0.7;
}

/* ==================== 成就展示馆样式 ==================== */
.achievement-hall {
  background: linear-gradient(135deg,
    rgba(255, 193, 7, 0.9) 0%,
    rgba(255, 143, 0, 0.9) 100%
  );
  border: 2rpx solid rgba(255, 193, 7, 0.5);
}

.achievement-hall .quadrant-glow {
  box-shadow: 0 0 40rpx rgba(255, 193, 7, 0.6);
}

.honor-constellation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.constellation-star {
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  background: rgba(255, 193, 7, 0.8);
  border-radius: 50%;
  box-shadow: 0 0 10rpx rgba(255, 193, 7, 0.6);
  animation: starPulse 2s ease-in-out infinite;
}

.constellation-star.star-1 { top: 20%; left: 30%; animation-delay: 0s; }
.constellation-star.star-2 { top: 30%; left: 70%; animation-delay: 0.4s; }
.constellation-star.star-3 { top: 60%; left: 20%; animation-delay: 0.8s; }
.constellation-star.star-4 { top: 70%; left: 80%; animation-delay: 1.2s; }
.constellation-star.star-5 { top: 80%; left: 50%; animation-delay: 1.6s; }

@keyframes starPulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.5);
  }
}

.constellation-line {
  position: absolute;
  height: 2rpx;
  background: linear-gradient(90deg,
    rgba(255, 193, 7, 0.6) 0%,
    rgba(255, 193, 7, 0.3) 50%,
    rgba(255, 193, 7, 0.6) 100%
  );
  animation: lineGlow 3s ease-in-out infinite;
}

.constellation-line.line-1 {
  top: 25%;
  left: 30%;
  width: 40%;
  transform: rotate(15deg);
  animation-delay: 0s;
}

.constellation-line.line-2 {
  top: 45%;
  left: 20%;
  width: 50%;
  transform: rotate(-20deg);
  animation-delay: 1s;
}

.constellation-line.line-3 {
  top: 75%;
  left: 50%;
  width: 30%;
  transform: rotate(45deg);
  animation-delay: 2s;
}

@keyframes lineGlow {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 0.8; }
}

.achievement-preview {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 12rpx;
  font-size: 18rpx;
  width: 100%;
  text-align: center !important;
}

.recent-badge {
  width: 24rpx;
  height: 24rpx;
  background: var(--energy-gold);
  border-radius: 50%;
  box-shadow: 0 0 8rpx var(--energy-gold-glow);
}

.recent-text {
  opacity: 0.9;
}

/* ==================== 好友星际站样式 ==================== */
.friend-station {
  background: linear-gradient(135deg,
    rgba(0, 191, 165, 0.9) 0%,
    rgba(0, 150, 136, 0.9) 100%
  );
  border: 2rpx solid rgba(0, 191, 165, 0.5);
}

.friend-station .quadrant-glow {
  box-shadow: 0 0 40rpx rgba(0, 191, 165, 0.6);
}

.communication-array {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.comm-dish {
  position: absolute;
  border: 2rpx solid rgba(0, 191, 165, 0.6);
  border-radius: 50%;
  animation: dishRotate 6s linear infinite;
}

.comm-dish.dish-1 {
  top: 20%;
  left: 20%;
  width: 40rpx;
  height: 40rpx;
  animation-duration: 6s;
}

.comm-dish.dish-2 {
  top: 60%;
  right: 30%;
  width: 30rpx;
  height: 30rpx;
  animation-duration: 4s;
  animation-direction: reverse;
}

.comm-dish.dish-3 {
  bottom: 20%;
  left: 50%;
  width: 35rpx;
  height: 35rpx;
  animation-duration: 8s;
}

@keyframes dishRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.signal-wave {
  position: absolute;
  border: 2rpx solid rgba(0, 191, 165, 0.4);
  border-radius: 50%;
  animation: waveExpand 3s ease-out infinite;
}

.signal-wave.wave-1 {
  top: 30%;
  left: 30%;
  width: 60rpx;
  height: 60rpx;
  animation-delay: 0s;
}

.signal-wave.wave-2 {
  top: 40%;
  right: 20%;
  width: 80rpx;
  height: 80rpx;
  animation-delay: 1s;
}

.signal-wave.wave-3 {
  bottom: 30%;
  left: 40%;
  width: 70rpx;
  height: 70rpx;
  animation-delay: 2s;
}

@keyframes waveExpand {
  0% {
    opacity: 0.8;
    transform: scale(0.5);
  }
  100% {
    opacity: 0;
    transform: scale(2);
  }
}

.friend-avatars {
  display: flex !important;
  gap: 8rpx;
  align-items: center !important;
  justify-content: center !important;
  width: 100%;
  text-align: center !important;
}

.friend-avatar {
  position: relative;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(0, 191, 165, 0.8);
  border-radius: 50%;
  font-size: 16rpx;
}

.friend-icon {
  filter: drop-shadow(0 0 4rpx currentColor);
}

.online-indicator {
  position: absolute;
  top: -2rpx;
  right: -2rpx;
  width: 8rpx;
  height: 8rpx;
  background: var(--hologram-green);
  border: 1rpx solid white;
  border-radius: 50%;
  box-shadow: 0 0 6rpx var(--hologram-green-glow);
  animation: onlinePulse 2s ease-in-out infinite;
}

@keyframes onlinePulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* ==================== 个人设置中心样式 ==================== */
.settings-center {
  background: linear-gradient(135deg,
    rgba(96, 125, 139, 0.9) 0%,
    rgba(55, 71, 79, 0.9) 100%
  );
  border: 2rpx solid rgba(96, 125, 139, 0.5);
}

.settings-center .quadrant-glow {
  box-shadow: 0 0 40rpx rgba(96, 125, 139, 0.6);
}

.control-matrix {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.matrix-node {
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background: rgba(96, 125, 139, 0.8);
  border: 2rpx solid rgba(96, 125, 139, 0.6);
  border-radius: 50%;
  box-shadow: 0 0 8rpx rgba(96, 125, 139, 0.4);
  animation: nodeGlow 4s ease-in-out infinite;
}

.matrix-node.node-1 {
  top: 25%;
  left: 25%;
  animation-delay: 0s;
}

.matrix-node.node-2 {
  top: 25%;
  right: 25%;
  animation-delay: 1s;
}

.matrix-node.node-3 {
  bottom: 25%;
  left: 25%;
  animation-delay: 2s;
}

.matrix-node.node-4 {
  bottom: 25%;
  right: 25%;
  animation-delay: 3s;
}

@keyframes nodeGlow {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.matrix-connection {
  position: absolute;
  height: 2rpx;
  background: linear-gradient(90deg,
    rgba(96, 125, 139, 0.6) 0%,
    rgba(96, 125, 139, 0.3) 50%,
    rgba(96, 125, 139, 0.6) 100%
  );
  animation: connectionFlow 5s ease-in-out infinite;
}

.matrix-connection.conn-1 {
  top: 25%;
  left: 25%;
  width: 50%;
  animation-delay: 0s;
}

.matrix-connection.conn-2 {
  top: 25%;
  right: 25%;
  width: 50%;
  transform: rotate(90deg);
  transform-origin: left center;
  animation-delay: 1.5s;
}

.matrix-connection.conn-3 {
  bottom: 25%;
  left: 25%;
  width: 50%;
  animation-delay: 3s;
}

@keyframes connectionFlow {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 0.8; }
}

.settings-preview {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  gap: 8rpx;
  font-size: 18rpx;
  width: 100%;
  text-align: center !important;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.setting-label {
  opacity: 0.8;
}

.setting-value {
  font-weight: bold;
  color: var(--crystal-blue);
}

/* ==================== 能量流线连接系统 ==================== */
.energy-flow-system {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  pointer-events: none;
  opacity: 0;
  animation: flowSystemActivation 1s ease-out 2.1s forwards;
}

@keyframes flowSystemActivation {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

.flow-line {
  position: absolute;
  background: linear-gradient(90deg,
    transparent 0%,
    var(--crystal-blue) 50%,
    transparent 100%
  );
  height: 2rpx;
  box-shadow: 0 0 10rpx var(--crystal-blue-glow);
  animation: flowPulse 4s ease-in-out infinite;
}

.flow-line.line-1 {
  top: 30%;
  left: 20%;
  width: 25%;
  transform: rotate(45deg);
  animation-delay: 0s;
}

.flow-line.line-2 {
  top: 30%;
  right: 20%;
  width: 25%;
  transform: rotate(-45deg);
  animation-delay: 1s;
}

.flow-line.line-3 {
  bottom: 30%;
  left: 20%;
  width: 25%;
  transform: rotate(-45deg);
  animation-delay: 2s;
}

.flow-line.line-4 {
  bottom: 30%;
  right: 20%;
  width: 25%;
  transform: rotate(45deg);
  animation-delay: 3s;
}

@keyframes flowPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1) rotate(var(--rotation, 0deg));
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1) rotate(var(--rotation, 0deg));
  }
}

.flow-particle {
  position: absolute;
  width: 6rpx;
  height: 6rpx;
  background: var(--crystal-blue);
  border-radius: 50%;
  box-shadow: 0 0 8rpx var(--crystal-blue-glow);
  animation: particleFlow 3s linear infinite;
}

.flow-particle.particle-1 { animation-delay: 0s; }
.flow-particle.particle-2 { animation-delay: 0.5s; }
.flow-particle.particle-3 { animation-delay: 1s; }
.flow-particle.particle-4 { animation-delay: 1.5s; }
.flow-particle.particle-5 { animation-delay: 2s; }
.flow-particle.particle-6 { animation-delay: 2.5s; }
.flow-particle.particle-7 { animation-delay: 3s; }
.flow-particle.particle-8 { animation-delay: 3.5s; }

@keyframes particleFlow {
  0% {
    transform: translateX(0) scale(0);
    opacity: 0;
  }
  10% {
    transform: translateX(10%) scale(1);
    opacity: 1;
  }
  90% {
    transform: translateX(90%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(100%) scale(0);
    opacity: 0;
  }
}

/* ==================== 全息特效层 ==================== */
.hologram-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 6;
  pointer-events: none;
  opacity: 0;
  animation: effectsActivation 1s ease-out 2.5s forwards;
}

@keyframes effectsActivation {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

.hologram-particle {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: var(--hologram-green);
  border-radius: 50%;
  box-shadow: 0 0 8rpx var(--hologram-green-glow);
  animation: hologramFloat 8s ease-in-out infinite;
}

.hologram-particle.hp-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
  animation-duration: 8s;
}

.hologram-particle.hp-2 {
  top: 40%;
  left: 90%;
  animation-delay: 1s;
  animation-duration: 10s;
}

.hologram-particle.hp-3 {
  top: 60%;
  left: 15%;
  animation-delay: 2s;
  animation-duration: 12s;
}

.hologram-particle.hp-4 {
  top: 80%;
  left: 85%;
  animation-delay: 3s;
  animation-duration: 9s;
}

.hologram-particle.hp-5 {
  top: 30%;
  left: 50%;
  animation-delay: 4s;
  animation-duration: 11s;
}

.hologram-particle.hp-6 {
  top: 70%;
  left: 60%;
  animation-delay: 5s;
  animation-duration: 7s;
}

@keyframes hologramFloat {
  0%, 100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.3;
  }
  25% {
    transform: translate(20rpx, -30rpx) scale(1.2);
    opacity: 0.8;
  }
  50% {
    transform: translate(-10rpx, -60rpx) scale(0.8);
    opacity: 1;
  }
  75% {
    transform: translate(-30rpx, -30rpx) scale(1.1);
    opacity: 0.6;
  }
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 750rpx) {
  .hologram-center {
    width: 250rpx;
    height: 250rpx;
  }

  .function-quadrants {
    max-width: 600rpx;
    height: 400rpx;
    gap: 20rpx;
    padding: 0 30rpx;
    margin-top: 40rpx !important;
  }

  .quadrant-content {
    padding: 20rpx;
  }

  .function-icon {
    font-size: 40rpx;
  }

  .function-title {
    font-size: 24rpx;
  }

  .function-subtitle {
    font-size: 20rpx;
  }
}

@media (min-width: 1200rpx) {
  .hologram-center {
    width: 350rpx;
    height: 350rpx;
  }

  .function-quadrants {
    max-width: 800rpx;
    height: 600rpx;
    gap: 40rpx;
    padding: 0 50rpx;
    margin-top: 80rpx !important;
  }

  .quadrant-content {
    padding: 32rpx;
  }

  .function-icon {
    font-size: 56rpx;
  }

  .function-title {
    font-size: 32rpx;
  }

  .function-subtitle {
    font-size: 24rpx;
  }
}

/* ==================== 性能优化 ==================== */
.quadrant,
.hologram-center,
.energy-ring,
.flow-line {
  transform: translateZ(0);
  backface-visibility: hidden;
}

.nebula,
.star,
.hologram-particle {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* ==================== 微信小程序兼容性优化 ==================== */
/* 注意：微信小程序WXSS不支持某些CSS特性，如prefers-reduced-motion和prefers-color-scheme */
/* 性能优化已在上方的性能优化部分统一处理 */

/* ==================== 强制居中对齐修复 ==================== */
/* 确保所有四象限内容完美居中对齐 */
.quadrant-content .function-icon,
.quadrant-content .function-title,
.quadrant-content .function-subtitle {
  text-align: center !important;
}

.quadrant-content view,
.quadrant-content text {
  width: 100%;
  text-align: center !important;
}

/* 特殊处理flex容器 */
.quadrant-content .energy-cost,
.quadrant-content .achievement-preview,
.quadrant-content .friend-avatars,
.quadrant-content .settings-preview {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
}

/* 确保设置项的特殊布局 */
.setting-item {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  width: 100% !important;
}
