// pages/exploration/gameEngine/index.js
const explorationSystem = require('../../../utils/explorationSystem.js');
const storyData = require('../../../utils/storyComprehensionData.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 游戏状态: start, playing, complete
    gameState: 'start',

    // 游戏数据
    gameData: {
      id: '',
      name: '',
      planetName: '',
      description: '',
      difficulty: '',
      duration: 0,
      energyMin: 0,
      energyMax: 0
    },

    // 游戏进行数据
    gameTimer: 0,
    currentScore: 0,
    finalScore: 0,
    performance: '',
    performanceStars: '',
    energyReward: 0,
    newAchievements: [],

    // 游戏会话
    gameSession: null,
    timerInterval: null,

    // 故事理解游戏数据
    currentStory: null,
    currentQuestionIndex: 0,
    currentQuestion: null,
    selectedAnswer: null,
    correctAnswers: 0,
    totalQuestions: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('游戏引擎页面加载', options);

    const gameId = options.gameId;
    if (gameId) {
      this.initializeGame(gameId);
    } else {
      wx.showToast({
        title: '游戏参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 清理定时器
    if (this.data.timerInterval) {
      clearInterval(this.data.timerInterval);
    }
  },

  /**
   * 初始化游戏
   */
  initializeGame: function(gameId) {
    try {
      // 获取游戏配置
      const game = explorationSystem.games[gameId];
      if (!game) {
        throw new Error('游戏不存在');
      }

      // 获取星球信息
      const planet = explorationSystem.planets[game.type];

      // 获取年龄组配置
      const explorationData = explorationSystem.getExplorationData();
      const ageGroup = explorationData.playerAge || '4-5';
      const gameConfig = game.ageGroups[ageGroup];

      // 设置游戏数据
      this.setData({
        gameData: {
          id: gameId,
          name: game.name,
          planetName: planet.name,
          description: game.description,
          difficulty: this.getDifficultyStars(gameConfig.complexity),
          duration: gameConfig.duration,
          energyMin: game.energyReward.min,
          energyMax: game.energyReward.max
        }
      });

    } catch (error) {
      console.error('初始化游戏失败:', error);
      wx.showToast({
        title: '游戏加载失败',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 获取难度星级
   */
  getDifficultyStars: function(complexity) {
    switch (complexity) {
      case 'simple': return '⭐⭐';
      case 'medium': return '⭐⭐⭐';
      case 'complex': return '⭐⭐⭐⭐';
      default: return '⭐⭐⭐';
    }
  },

  /**
   * 开始游戏
   */
  onStartGame: function() {
    console.log('开始游戏');

    // 启动游戏会话
    const result = explorationSystem.startGame(this.data.gameData.id);

    if (result.success) {
      this.setData({
        gameState: 'playing',
        gameSession: result.session,
        gameTimer: 0,
        currentScore: 0
      });

      // 如果是故事理解游戏，初始化游戏数据
      if (this.data.gameData.id === 'story_comprehension') {
        this.initStoryGame();
      }

      // 启动计时器
      this.startGameTimer();

    } else {
      wx.showToast({
        title: result.error || '启动游戏失败',
        icon: 'none'
      });
    }
  },

  /**
   * 启动游戏计时器
   */
  startGameTimer: function() {
    const timerInterval = setInterval(() => {
      this.setData({
        gameTimer: this.data.gameTimer + 1
      });
    }, 1000);

    this.setData({
      timerInterval: timerInterval
    });
  },

  /**
   * 格式化时间显示
   */
  formatTime: function(seconds) {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  },

  /**
   * 演示游戏操作
   */
  onDemoAction: function(e) {
    const action = e.currentTarget.dataset.action;
    console.log('演示操作:', action);

    switch (action) {
      case 'correct':
        this.setData({
          currentScore: Math.min(this.data.currentScore + 20, 100)
        });
        wx.showToast({
          title: '回答正确！+20分',
          icon: 'success'
        });
        break;

      case 'wrong':
        this.setData({
          currentScore: Math.max(this.data.currentScore - 10, 0)
        });
        wx.showToast({
          title: '回答错误！-10分',
          icon: 'none'
        });
        break;

      case 'complete':
        this.completeGame();
        break;
    }
  },

  /**
   * 完成游戏
   */
  completeGame: function() {
    console.log('完成游戏');

    // 停止计时器
    if (this.data.timerInterval) {
      clearInterval(this.data.timerInterval);
      this.setData({ timerInterval: null });
    }

    const finalScore = this.data.currentScore;
    const performance = this.getPerformanceLevel(finalScore);
    const performanceStars = this.getPerformanceStars(performance);

    // 完成游戏会话
    const result = explorationSystem.completeGame(
      this.data.gameData.id,
      finalScore,
      performance
    );

    if (result.success) {
      this.setData({
        gameState: 'complete',
        finalScore: finalScore,
        performance: this.getPerformanceText(performance),
        performanceStars: performanceStars,
        energyReward: result.energyReward,
        newAchievements: result.newUnlocks || []
      });

      // 显示完成动画
      this.showCompleteAnimation();

    } else {
      wx.showToast({
        title: result.error || '完成游戏失败',
        icon: 'none'
      });
    }
  },

  /**
   * 获取表现等级
   */
  getPerformanceLevel: function(score) {
    if (score >= 90) return 'excellent';
    if (score >= 70) return 'good';
    if (score >= 50) return 'fair';
    return 'poor';
  },

  /**
   * 获取表现星级
   */
  getPerformanceStars: function(performance) {
    switch (performance) {
      case 'excellent': return '⭐⭐⭐⭐⭐';
      case 'good': return '⭐⭐⭐⭐';
      case 'fair': return '⭐⭐⭐';
      default: return '⭐⭐';
    }
  },

  /**
   * 获取表现文本
   */
  getPerformanceText: function(performance) {
    switch (performance) {
      case 'excellent': return '优秀';
      case 'good': return '良好';
      case 'fair': return '一般';
      default: return '需要努力';
    }
  },

  /**
   * 显示完成动画
   */
  showCompleteAnimation: function() {
    // 这里可以添加完成动画效果
    console.log('显示完成动画');
  },

  /**
   * 初始化故事理解游戏
   */
  initStoryGame: function() {
    try {
      // 获取年龄组
      const explorationData = explorationSystem.getExplorationData();
      const ageGroup = explorationData.playerAge || '4-5';

      // 获取适合年龄的题目
      const questions = storyData.getQuestionsByAge(ageGroup, 1);

      if (questions.length === 0) {
        throw new Error('没有找到合适的题目');
      }

      const story = questions[0];

      this.setData({
        currentStory: story,
        currentQuestionIndex: 0,
        currentQuestion: story.questions[0],
        selectedAnswer: null,
        correctAnswers: 0,
        totalQuestions: story.questions.length
      });

      console.log('故事理解游戏初始化成功', story);

    } catch (error) {
      console.error('初始化故事游戏失败:', error);
      wx.showToast({
        title: '游戏初始化失败',
        icon: 'none'
      });
    }
  },

  /**
   * 选择答案
   */
  onSelectAnswer: function(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      selectedAnswer: index
    });
  },

  /**
   * 确认答案
   */
  onConfirmAnswer: function() {
    if (this.data.selectedAnswer === null) return;

    const isCorrect = this.data.selectedAnswer === this.data.currentQuestion.answer;

    if (isCorrect) {
      this.setData({
        currentScore: this.data.currentScore + 25,
        correctAnswers: this.data.correctAnswers + 1
      });

      wx.showToast({
        title: '回答正确！+25分',
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '回答错误，正确答案是' +
               ['A', 'B', 'C', 'D'][this.data.currentQuestion.answer],
        icon: 'none',
        duration: 2000
      });
    }

    // 延迟进入下一题或完成游戏
    setTimeout(() => {
      this.nextQuestion();
    }, 1500);
  },

  /**
   * 下一题
   */
  nextQuestion: function() {
    const nextIndex = this.data.currentQuestionIndex + 1;

    if (nextIndex < this.data.currentStory.questions.length) {
      // 还有题目，继续下一题
      this.setData({
        currentQuestionIndex: nextIndex,
        currentQuestion: this.data.currentStory.questions[nextIndex],
        selectedAnswer: null
      });
    } else {
      // 所有题目完成，结束游戏
      this.completeGame();
    }
  },

  /**
   * 继续探索
   */
  onContinueExploration: function() {
    console.log('继续探索');

    // 获取推荐游戏
    const recommendedGame = explorationSystem.getRecommendedGame();

    if (recommendedGame) {
      // 重新初始化新游戏
      this.initializeGame(recommendedGame.id);
      this.setData({
        gameState: 'start'
      });
    } else {
      wx.showToast({
        title: '暂无更多游戏',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 返回星图
   */
  onBackToMap: function() {
    console.log('返回星图');
    wx.navigateBack();
  },

  /**
   * 分享结果
   */
  onShareResult: function() {
    console.log('分享游戏结果');

    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const gameData = this.data.gameData;
    const finalScore = this.data.finalScore;

    return {
      title: `我在《能量星球》的${gameData.planetName}获得了${finalScore}分！`,
      path: '/pages/exploration/index',
      imageUrl: '/images/share-game-result.png'
    };
  }
})