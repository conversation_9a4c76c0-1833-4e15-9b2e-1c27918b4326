/* 《能量星球》个人档案中心 - 世界500强级别UI设计 */

/* ==================== CSS变量系统 ==================== */
page {
  /* 深空紫色渐变系统 */
  --primary-gradient: linear-gradient(135deg, #1A0B3D 0%, #2D1B69 50%, #4A148C 100%);
  --primary-dark: #1A0B3D;
  --primary-medium: #2D1B69;
  --primary-light: #4A148C;
  
  /* 水晶蓝色系统 */
  --crystal-blue: #00E5FF;
  --crystal-blue-light: #62EFFF;
  --crystal-blue-dark: #00B8D4;
  --crystal-blue-glow: rgba(0, 229, 255, 0.3);
  
  /* 全息绿色系统 */
  --hologram-green: #00FF88;
  --hologram-green-light: #69F0AE;
  --hologram-green-dark: #00BFA5;
  --hologram-green-glow: rgba(0, 255, 136, 0.3);
  
  /* 能量金色系统 */
  --energy-gold: #FFD700;
  --energy-gold-light: #FFECB3;
  --energy-gold-dark: #FF8F00;
  --energy-gold-glow: rgba(255, 215, 0, 0.3);
  
  /* 中性色系统 */
  --space-gray: #37474F;
  --space-gray-light: #546E7A;
  --space-gray-dark: #263238;
  
  /* 透明度系统 */
  --alpha-high: 0.9;
  --alpha-medium: 0.6;
  --alpha-low: 0.3;
  --alpha-subtle: 0.1;
  
  /* 阴影系统 */
  --shadow-subtle: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
  --shadow-strong: 0 16rpx 48rpx rgba(0, 0, 0, 0.3);
  --shadow-dramatic: 0 32rpx 64rpx rgba(0, 0, 0, 0.4);
  
  /* 动画时长系统 */
  --duration-fast: 0.2s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;
  --duration-dramatic: 1s;
  
  /* 缓动函数系统 */
  --ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
  --ease-in-out-quart: cubic-bezier(0.76, 0, 0.24, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ==================== 基础页面样式 ==================== */
.page {
  width: 100%;
  height: 100vh;
  background: var(--primary-gradient);
  overflow-x: hidden;
  overflow-y: hidden;
  position: relative;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.profile-center {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* ==================== 深空背景系统 ==================== */
.space-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.star-field {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.star {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: white;
  border-radius: 50%;
  box-shadow: 0 0 10rpx rgba(255, 255, 255, 0.8);
  animation: starTwinkle 3s ease-in-out infinite;
}

.star-1 { top: 15%; left: 20%; animation-delay: 0s; }
.star-2 { top: 25%; left: 80%; animation-delay: 0.5s; }
.star-3 { top: 45%; left: 15%; animation-delay: 1s; }
.star-4 { top: 65%; left: 85%; animation-delay: 1.5s; }
.star-5 { top: 85%; left: 45%; animation-delay: 2s; }

@keyframes starTwinkle {
  0%, 100% { 
    opacity: 0.3; 
    transform: scale(1); 
  }
  50% { 
    opacity: 1; 
    transform: scale(1.5); 
  }
}

/* ==================== 主要内容区域 ==================== */
.content-scroll {
  position: relative;
  z-index: 2;
  flex: 1;
  padding: 0 30rpx 40rpx;
  box-sizing: border-box;
  width: 100%;
  margin: 0;
}

/* ==================== 区域标题样式 ==================== */
.section-header {
  margin-bottom: 40rpx;
  text-align: center;
}

.section-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 12rpx;
  text-shadow: 0 0 20rpx var(--crystal-blue-glow);
}

.section-subtitle {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  opacity: 0.8;
}

/* ==================== 船长形象编辑区域 ==================== */
.captain-editor-section {
  margin-bottom: 60rpx;
  width: 100%;
  box-sizing: border-box;
}

.captain-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40rpx;
}

.preview-platform {
  position: relative;
  width: 100%;
  max-width: 500rpx;
  height: 400rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-sizing: border-box;
}

.hologram-avatar {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3rpx solid var(--crystal-blue);
  border-radius: 50%;
  background: radial-gradient(circle, 
    rgba(0, 229, 255, 0.1) 0%, 
    rgba(0, 229, 255, 0.05) 50%, 
    transparent 100%
  );
  box-shadow: 
    0 0 30rpx var(--crystal-blue-glow),
    inset 0 0 20rpx rgba(0, 229, 255, 0.1);
  animation: hologramPulse 3s ease-in-out infinite;
  cursor: pointer;
  transition: transform var(--duration-normal) var(--ease-out-quart);
}

.hologram-avatar:hover {
  transform: scale(1.05);
}

@keyframes hologramPulse {
  0%, 100% { 
    box-shadow: 
      0 0 30rpx var(--crystal-blue-glow),
      inset 0 0 20rpx rgba(0, 229, 255, 0.1);
  }
  50% { 
    box-shadow: 
      0 0 50rpx var(--crystal-blue-glow),
      inset 0 0 30rpx rgba(0, 229, 255, 0.2);
  }
}

.avatar-display {
  font-size: 80rpx;
  filter: drop-shadow(0 0 10rpx var(--crystal-blue));
}

.edit-indicator {
  position: absolute;
  bottom: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background: var(--energy-gold);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 15rpx var(--energy-gold-glow);
  animation: editPulse 2s ease-in-out infinite;
}

@keyframes editPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.edit-icon {
  font-size: 20rpx;
  color: var(--primary-dark);
}

/* ==================== 装备展示 ==================== */
.equipment-display {
  width: 100%;
  margin-top: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 20rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.equipment-slot {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid var(--hologram-green);
  border-radius: 16rpx;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out-quart);
  backdrop-filter: blur(10rpx);
}

.equipment-slot:hover {
  background: rgba(0, 255, 136, 0.2);
  transform: scale(1.05);
  box-shadow: 0 0 20rpx var(--hologram-green-glow);
}

.equipment-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
  filter: drop-shadow(0 0 8rpx currentColor);
}

.equipment-label {
  font-size: 20rpx;
  color: white;
  opacity: 0.9;
  font-weight: bold;
  text-align: center;
}

/* ==================== 个人信息区域 ==================== */
.personal-info-section {
  margin-bottom: 60rpx;
  width: 100%;
  box-sizing: border-box;
}

.info-card {
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: var(--shadow-medium);
  box-sizing: border-box;
  width: 100%;
}

.level-card {
  border-color: var(--energy-gold);
  background: linear-gradient(135deg, 
    rgba(255, 215, 0, 0.1) 0%, 
    rgba(255, 143, 0, 0.1) 100%
  );
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.level-badge {
  width: 80rpx;
  height: 80rpx;
  background: radial-gradient(circle, 
    var(--energy-gold) 0%, 
    var(--energy-gold-dark) 100%
  );
  border: 3rpx solid var(--energy-gold-light);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 0 20rpx var(--energy-gold-glow);
}

.level-number {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--primary-dark);
}

.level-info {
  flex: 1;
}

.level-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
}

.level-subtitle {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}

.progress-section {
  margin-top: 24rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.progress-label {
  font-size: 24rpx;
  color: white;
}

.progress-value {
  font-size: 24rpx;
  color: var(--energy-gold);
  font-weight: bold;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 12rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, 
    var(--energy-gold) 0%, 
    var(--energy-gold-light) 100%
  );
  border-radius: 4rpx;
  transition: width var(--duration-slow) var(--ease-out-quart);
  box-shadow: 0 0 10rpx var(--energy-gold-glow);
}

.progress-tip {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
}

/* ==================== 统计数据网格 ==================== */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.stat-item {
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
  transition: all var(--duration-normal) var(--ease-out-quart);
  box-sizing: border-box;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-4rpx);
}

.stat-icon {
  font-size: 40rpx;
  margin-bottom: 12rpx;
  display: block;
}

.stat-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: var(--crystal-blue);
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* ==================== 个性化设置区域 ==================== */
.personalization-section {
  margin-bottom: 60rpx;
  width: 100%;
  box-sizing: border-box;
}

.setting-card {
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  backdrop-filter: blur(10rpx);
  transition: all var(--duration-normal) var(--ease-out-quart);
  box-sizing: border-box;
  width: 100%;
}

.setting-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2rpx);
}

.card-title {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.title-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.title-text {
  font-size: 26rpx;
  font-weight: bold;
  color: white;
}

.motto-editor,
.signature-editor {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12rpx;
  cursor: pointer;
  transition: background var(--duration-normal) var(--ease-out-quart);
}

.motto-editor:hover,
.signature-editor:hover {
  background: rgba(0, 0, 0, 0.3);
}

.motto-text,
.signature-text {
  flex: 1;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

.edit-button {
  padding: 8rpx 16rpx;
  background: var(--crystal-blue);
  border-radius: 8rpx;
  margin-left: 16rpx;
}

.edit-text {
  font-size: 20rpx;
  color: var(--primary-dark);
  font-weight: bold;
}

/* ==================== 兴趣标签 ==================== */
.interests-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  padding: 16rpx;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12rpx;
}

.interest-tag {
  padding: 8rpx 16rpx;
  background: var(--hologram-green);
  border-radius: 20rpx;
  box-shadow: 0 0 10rpx var(--hologram-green-glow);
}

.tag-text {
  font-size: 20rpx;
  color: var(--primary-dark);
  font-weight: bold;
}

.add-interest-btn {
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx dashed rgba(255, 255, 255, 0.5);
  border-radius: 20rpx;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out-quart);
}

.add-interest-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.8);
}

.add-text {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* ==================== 回忆时光区域 ==================== */
.memories-section {
  margin-bottom: 60rpx;
  width: 100%;
  box-sizing: border-box;
}

.memories-timeline {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.memory-item {
  display: flex;
  margin-bottom: 32rpx;
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

.memory-item::before {
  content: '';
  position: absolute;
  left: 80rpx;
  top: 40rpx;
  bottom: -32rpx;
  width: 2rpx;
  background: linear-gradient(180deg,
    var(--crystal-blue) 0%,
    rgba(0, 229, 255, 0.3) 100%
  );
}

.memory-item:last-child::before {
  display: none;
}

.memory-date {
  min-width: 120rpx;
  max-width: 160rpx;
  text-align: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.date-text {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
  padding: 8rpx 12rpx;
  border-radius: 12rpx;
}

.memory-content {
  flex: 1;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
  transition: all var(--duration-normal) var(--ease-out-quart);
  box-sizing: border-box;
  min-width: 0;
}

.memory-content:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(8rpx);
}

.memory-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  width: 48rpx;
  text-align: center;
}

.memory-info {
  flex: 1;
}

.memory-title {
  display: block;
  font-size: 24rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
}

.memory-description {
  display: block;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
}

/* 底部操作按钮已移除 - 使用自动保存机制 */

/* ==================== 响应式设计 ==================== */
@media (max-width: 750rpx) {
  .content-scroll {
    padding: 0 20rpx 30rpx;
  }

  .preview-platform {
    max-width: 400rpx;
    height: 350rpx;
  }

  .hologram-avatar {
    width: 120rpx;
    height: 120rpx;
  }

  .avatar-display {
    font-size: 60rpx;
  }

  .equipment-display {
    gap: 15rpx;
    padding: 0 15rpx;
    justify-content: center;
  }

  .equipment-slot {
    width: 100rpx;
    height: 100rpx;
  }

  .equipment-icon {
    font-size: 36rpx;
  }

  .equipment-label {
    font-size: 18rpx;
  }

  .stats-grid {
    gap: 15rpx;
  }

  .memory-date {
    min-width: 100rpx;
    max-width: 120rpx;
    margin-right: 16rpx;
  }
}

/* ==================== 性能优化 ==================== */
.hologram-avatar,
.equipment-slot,
.stat-item,
.setting-card,
.memory-content,
.action-button {
  transform: translateZ(0);
  backface-visibility: hidden;
}
