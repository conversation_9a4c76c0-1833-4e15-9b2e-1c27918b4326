/* 善意行为库样式 */

.kindness-library-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFF8E1 0%, #FFECB3 50%, #FFE082 100%);
  position: relative;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 背景装饰 */
.library-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.floating-star {
  position: absolute;
  font-size: 30rpx;
  opacity: 0.6;
  animation: starFloat 6s infinite ease-in-out;
}

.star-1 { top: 15%; left: 10%; animation-delay: 0s; }
.star-2 { top: 25%; right: 15%; animation-delay: 1.5s; }
.star-3 { top: 60%; left: 8%; animation-delay: 3s; }
.star-4 { top: 80%; right: 12%; animation-delay: 4.5s; }

@keyframes starFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20rpx) rotate(180deg);
    opacity: 0.9;
  }
}

/* 页面标题 */
.library-header {
  position: relative;
  z-index: 10;
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx 20rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 25rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 193, 7, 0.4);
}

.header-icon {
  font-size: 60rpx;
  margin-bottom: 15rpx;
  animation: headerPulse 3s infinite ease-in-out;
}

@keyframes headerPulse {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 15rpx rgba(255, 193, 7, 0.6));
  }
  50% {
    transform: scale(1.1);
    filter: drop-shadow(0 0 25rpx rgba(255, 193, 7, 0.8));
  }
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #F57F17;
  margin-bottom: 8rpx;
  text-shadow: 1rpx 1rpx 3rpx rgba(245, 127, 23, 0.3);
}

.header-subtitle {
  font-size: 24rpx;
  color: #FF8F00;
  opacity: 0.8;
}

/* 搜索栏 */
.search-section {
  position: relative;
  z-index: 10;
  margin-bottom: 30rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 25rpx;
  padding: 15rpx 20rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 193, 7, 0.3);
}

.search-icon {
  font-size: 28rpx;
  color: #FF8F00;
  margin-right: 15rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #F57F17;
  background: transparent;
}

.search-input::placeholder {
  color: #FFAB91;
  opacity: 0.7;
}

.search-clear {
  font-size: 24rpx;
  color: #FF8F00;
  padding: 5rpx;
  margin-left: 10rpx;
}

/* 筛选标签 */
.filter-section {
  position: relative;
  z-index: 10;
  margin-bottom: 30rpx;
}

.filter-title {
  font-size: 26rpx;
  color: #F57F17;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.filter-tags {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}

.filter-tag {
  padding: 8rpx 20rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #FF8F00;
  border: 2rpx solid rgba(255, 193, 7, 0.3);
  transition: all 0.3s ease;
}

.filter-tag.active {
  background: rgba(255, 193, 7, 0.6);
  color: white;
  border-color: #FFC107;
  transform: scale(1.05);
}

/* 分类网格 */
.categories-section {
  position: relative;
  z-index: 10;
  margin-bottom: 40rpx;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #F57F17;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.category-card {
  position: relative;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  backdrop-filter: blur(15rpx);
  border: 2rpx solid rgba(255, 193, 7, 0.3);
  transition: all 0.3s ease;
  overflow: hidden;
}

.category-card:active {
  transform: scale(0.95);
  box-shadow: 0 8rpx 32rpx rgba(255, 193, 7, 0.3);
}

.category-icon {
  font-size: 50rpx;
  margin-bottom: 15rpx;
  animation: categoryFloat 4s infinite ease-in-out;
}

@keyframes categoryFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8rpx);
  }
}

.category-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #F57F17;
  margin-bottom: 8rpx;
}

.category-description {
  font-size: 20rpx;
  color: #FF8F00;
  opacity: 0.8;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.category-count {
  font-size: 18rpx;
  color: #FFAB91;
  opacity: 0.9;
}

.category-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80%;
  height: 80%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.category-card:active .category-glow {
  opacity: 0.3;
  animation: categoryGlow 0.6s ease-out;
}

@keyframes categoryGlow {
  0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 0.5;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0;
  }
}

/* 行为列表 */
.actions-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.action-item {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 15rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 193, 7, 0.3);
  transition: all 0.3s ease;
}

.action-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.4);
}

.action-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.action-info {
  flex: 1;
}

.action-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #F57F17;
  margin-bottom: 5rpx;
}

.action-subtitle {
  font-size: 22rpx;
  color: #FF8F00;
  opacity: 0.8;
  margin-bottom: 8rpx;
}

.action-meta {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.action-difficulty {
  font-size: 20rpx;
  color: #FFAB91;
}

.action-points {
  font-size: 20rpx;
  color: #FF6F00;
  font-weight: bold;
}

.action-arrow {
  font-size: 24rpx;
  color: #FF8F00;
  opacity: 0.6;
}

/* 无结果状态 */
.no-results {
  text-align: center;
  padding: 60rpx 20rpx;
}

.no-results-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.no-results-text {
  font-size: 28rpx;
  color: #F57F17;
  margin-bottom: 10rpx;
}

.no-results-tip {
  font-size: 22rpx;
  color: #FF8F00;
  opacity: 0.7;
}

/* 推荐行为 */
.recommended-actions {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.recommended-item {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.3), rgba(255, 152, 0, 0.2));
  border-radius: 15rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 193, 7, 0.5);
  position: relative;
}

.recommended-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.recommended-info {
  flex: 1;
}

.recommended-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #F57F17;
  margin-bottom: 5rpx;
}

.recommended-reason {
  font-size: 22rpx;
  color: #FF8F00;
  opacity: 0.8;
}

.recommended-badge {
  position: absolute;
  top: -5rpx;
  right: 10rpx;
  background: #FF6F00;
  color: white;
  font-size: 18rpx;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  font-weight: bold;
}

/* 进度统计 */
.progress-stats {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 15rpx;
  padding: 25rpx;
  margin-bottom: 25rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 193, 7, 0.3);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #F57F17;
  margin-bottom: 5rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #FF8F00;
  opacity: 0.8;
}

/* 进度图表 */
.progress-chart {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 15rpx;
  padding: 25rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 193, 7, 0.3);
}

.chart-title {
  font-size: 24rpx;
  color: #F57F17;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-align: center;
}

.chart-bars {
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  height: 120rpx;
}

.chart-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 30rpx;
}

.bar-fill {
  width: 100%;
  background: linear-gradient(to top, #FF8F00, #FFC107);
  border-radius: 4rpx 4rpx 0 0;
  min-height: 10rpx;
  transition: height 0.5s ease;
}

.bar-label {
  font-size: 18rpx;
  color: #FF8F00;
  margin-top: 8rpx;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 248, 225, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5rpx);
}

.loading-spinner {
  text-align: center;
}

.spinner-icon {
  font-size: 60rpx;
  animation: spinnerRotate 1.5s infinite ease-in-out;
}

@keyframes spinnerRotate {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.2);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

.loading-text {
  font-size: 24rpx;
  color: #F57F17;
  margin-top: 15rpx;
  font-weight: bold;
}
