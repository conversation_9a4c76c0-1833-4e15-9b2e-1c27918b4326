// 《能量星球》个人档案中心 - 船长形象定制与个人信息管理
const personalSystem = require('../../../utils/personalSystem.js');

Page({
  data: {
    // 船长形象数据
    captainData: {
      avatar: '🧑‍🚀',
      helmet: '⛑️',
      suit: '🚀',
      accessory: '🎖️'
    },
    
    // 等级信息
    levelInfo: {
      level: 5,
      title: '星际探索者',
      experience: 750,
      nextLevelExp: 1000,
      progress: 0.75,
      expToNext: 250
    },
    
    // 统计数据
    statsData: {
      totalPlayTime: 25,
      joinDays: 30,
      achievementCount: 12,
      friendCount: 5
    },
    
    // 个人信息
    personalData: {
      motto: '探索无限宇宙，成就无限可能',
      signature: '我是一名勇敢的星际探索者',
      interests: ['宇宙探索', '科学实验', '善意传播']
    },
    
    // 回忆数据
    memoriesData: [
      {
        id: 1,
        date: '2025-01-20',
        icon: '🏆',
        title: '获得首个成就',
        description: '完成了第一个学习任务，获得"初出茅庐"徽章'
      },
      {
        id: 2,
        date: '2025-01-15',
        icon: '🤝',
        title: '结识第一位好友',
        description: '与小明成为好友，开始了友谊的星际之旅'
      },
      {
        id: 3,
        date: '2025-01-10',
        icon: '🚀',
        title: '加入能量星球',
        description: '开始了精彩的宇宙探索之旅'
      }
    ]
  },

  onLoad: function(options) {
    console.log('个人档案中心页面加载');
    this.loadProfileData();
  },

  onShow: function() {
    console.log('个人档案中心页面显示');
    this.refreshData();
  },

  onHide: function() {
    console.log('个人档案中心页面隐藏');
    this.autoSaveProfile();
  },

  onUnload: function() {
    console.log('个人档案中心页面卸载');
    this.autoSaveProfile();
  },

  // 加载个人档案数据
  loadProfileData: function() {
    try {
      // 加载个人基础数据
      const personalData = personalSystem.getPersonalData();
      
      // 加载等级信息
      const levelInfo = personalSystem.getCurrentLevel();
      
      // 加载统计数据
      const statsData = this.getStatsData();
      
      // 加载回忆数据
      const memoriesData = this.getMemoriesData();
      
      this.setData({
        captainData: {
          avatar: personalData.avatar || '🧑‍🚀',
          helmet: personalData.helmet || '⛑️',
          suit: personalData.suit || '🚀',
          accessory: personalData.accessory || '🎖️'
        },
        levelInfo: levelInfo,
        personalData: {
          motto: personalData.motto || '探索无限宇宙，成就无限可能',
          signature: personalData.signature || '我是一名勇敢的星际探索者',
          interests: personalData.interests || ['宇宙探索', '科学实验', '善意传播']
        },
        statsData: statsData,
        memoriesData: memoriesData
      });
      
    } catch (error) {
      console.error('加载个人档案数据失败:', error);
    }
  },

  // 获取统计数据
  getStatsData: function() {
    try {
      const personalData = personalSystem.getPersonalData();
      const achievementSummary = personalSystem.getAchievementSummary();
      
      // 计算加入天数
      const joinDate = new Date(personalData.joinDate || new Date());
      const now = new Date();
      const joinDays = Math.floor((now - joinDate) / (1000 * 60 * 60 * 24));
      
      return {
        totalPlayTime: Math.floor((personalData.totalPlayTime || 0) / 60), // 转换为小时
        joinDays: joinDays,
        achievementCount: achievementSummary.totalCount,
        friendCount: this.getFriendCount()
      };
    } catch (error) {
      console.error('获取统计数据失败:', error);
      return {
        totalPlayTime: 0,
        joinDays: 0,
        achievementCount: 0,
        friendCount: 0
      };
    }
  },

  // 获取好友数量
  getFriendCount: function() {
    try {
      const friendSystem = require('../../../utils/friendSystem.js');
      const friendSummary = friendSystem.getFriendSummary();
      return friendSummary.totalCount;
    } catch (error) {
      console.error('获取好友数量失败:', error);
      return 0;
    }
  },

  // 获取回忆数据
  getMemoriesData: function() {
    try {
      // 这里可以从本地存储或其他模块获取重要时刻
      const memories = wx.getStorageSync('memories') || [];
      return memories.slice(0, 5); // 只显示最近5个回忆
    } catch (error) {
      console.error('获取回忆数据失败:', error);
      return [];
    }
  },

  // 刷新数据
  refreshData: function() {
    this.loadProfileData();
  },

  // 编辑头像
  onEditAvatar: function() {
    console.log('编辑头像');
    const avatarOptions = ['🧑‍🚀', '👨‍🚀', '👩‍🚀', '🤖', '👽', '🦸‍♂️', '🦸‍♀️'];
    
    wx.showActionSheet({
      itemList: avatarOptions.map(avatar => `${avatar} 选择这个头像`),
      success: (res) => {
        if (res.tapIndex >= 0) {
          const selectedAvatar = avatarOptions[res.tapIndex];
          this.updateCaptainData('avatar', selectedAvatar);
        }
      }
    });
  },

  // 编辑头盔
  onEditHelmet: function() {
    console.log('编辑头盔');
    const helmetOptions = ['⛑️', '🪖', '👑', '🎩', '🧢', '👒'];
    
    wx.showActionSheet({
      itemList: helmetOptions.map(helmet => `${helmet} 选择这个头盔`),
      success: (res) => {
        if (res.tapIndex >= 0) {
          const selectedHelmet = helmetOptions[res.tapIndex];
          this.updateCaptainData('helmet', selectedHelmet);
        }
      }
    });
  },

  // 编辑宇航服
  onEditSuit: function() {
    console.log('编辑宇航服');
    const suitOptions = ['🚀', '🛸', '🌟', '⭐', '💫', '🌠'];
    
    wx.showActionSheet({
      itemList: suitOptions.map(suit => `${suit} 选择这个宇航服`),
      success: (res) => {
        if (res.tapIndex >= 0) {
          const selectedSuit = suitOptions[res.tapIndex];
          this.updateCaptainData('suit', selectedSuit);
        }
      }
    });
  },

  // 编辑配饰
  onEditAccessory: function() {
    console.log('编辑配饰');
    const accessoryOptions = ['🎖️', '🏅', '🥇', '💎', '⚡', '🔥'];
    
    wx.showActionSheet({
      itemList: accessoryOptions.map(accessory => `${accessory} 选择这个配饰`),
      success: (res) => {
        if (res.tapIndex >= 0) {
          const selectedAccessory = accessoryOptions[res.tapIndex];
          this.updateCaptainData('accessory', selectedAccessory);
        }
      }
    });
  },

  // 更新船长数据
  updateCaptainData: function(key, value) {
    const captainData = { ...this.data.captainData };
    captainData[key] = value;
    
    this.setData({
      [`captainData.${key}`]: value
    });
    
    // 保存到本地存储
    personalSystem.savePersonalData({ [key]: value });
    
    wx.showToast({
      title: '更新成功',
      icon: 'success'
    });
  },

  // 编辑座右铭
  onEditMotto: function() {
    console.log('编辑座右铭');
    wx.showModal({
      title: '编辑座右铭',
      editable: true,
      placeholderText: '请输入你的座右铭',
      content: this.data.personalData.motto,
      success: (res) => {
        if (res.confirm && res.content) {
          this.updatePersonalData('motto', res.content);
        }
      }
    });
  },

  // 编辑个性签名
  onEditSignature: function() {
    console.log('编辑个性签名');
    wx.showModal({
      title: '编辑个性签名',
      editable: true,
      placeholderText: '请输入你的个性签名',
      content: this.data.personalData.signature,
      success: (res) => {
        if (res.confirm && res.content) {
          this.updatePersonalData('signature', res.content);
        }
      }
    });
  },

  // 更新个人数据
  updatePersonalData: function(key, value) {
    this.setData({
      [`personalData.${key}`]: value
    });
    
    // 保存到本地存储
    personalSystem.savePersonalData({ [key]: value });
    
    wx.showToast({
      title: '更新成功',
      icon: 'success'
    });
  },

  // 添加兴趣标签
  onAddInterest: function() {
    console.log('添加兴趣标签');
    wx.showModal({
      title: '添加兴趣标签',
      editable: true,
      placeholderText: '请输入兴趣标签',
      success: (res) => {
        if (res.confirm && res.content) {
          const interests = [...this.data.personalData.interests];
          if (!interests.includes(res.content)) {
            interests.push(res.content);
            this.updatePersonalData('interests', interests);
          } else {
            wx.showToast({
              title: '标签已存在',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 自动保存设置
  autoSaveProfile: function() {
    console.log('自动保存个人档案设置');

    try {
      // 保存所有数据
      const allData = {
        ...this.data.captainData,
        ...this.data.personalData
      };

      personalSystem.savePersonalData(allData);
      console.log('个人档案数据已自动保存');

    } catch (error) {
      console.error('自动保存失败:', error);
    }
  }
});
