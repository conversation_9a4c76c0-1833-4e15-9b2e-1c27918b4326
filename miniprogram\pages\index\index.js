// 《能量星球》星际舰桥 - 主界面逻辑
Page({
  data: {
    // 页面状态
    loading: false,

    // 能量数据
    energyData: {
      wisdom: 150,
      love: 89
    },

    // 模块进度数据
    workshopProgress: 65,
    captainProgress: 80,
    parentProgress: 45,
    taskProgress: 30,

    // 状态信息
    lighthouseStatus: '善意传播中',
    todayTasksCount: 5,
    todayTasksCompleted: false
  },

  onLoad: function (options) {
    console.log('星际舰桥加载');
    this.initializePage();
  },

  onReady: function () {
    console.log('星际舰桥渲染完成');
  },

  onShow: function () {
    // 每次显示时刷新能量数据
    this.refreshEnergyData();
  },

  onHide: function () {
    // 页面隐藏时保存数据
    this.saveUserData();
  },

  onUnload: function () {
    console.log('星际舰桥卸载');
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    this.refreshEnergyData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  onShareAppMessage: function () {
    return {
      title: '来《能量星球》开始你的宇宙探索之旅！',
      path: '/pages/index/index',
      imageUrl: '/assets/images/share-cover.jpg'
    }
  },

  // 初始化页面
  initializePage() {
    // 加载用户数据
    this.loadUserData();

    // 播放背景音乐
    this.playBackgroundMusic();
  },

  // 加载用户数据
  loadUserData() {
    // 从本地存储加载用户数据
    const userData = wx.getStorageSync('userData') || {};
    const energyData = wx.getStorageSync('energyData') || { wisdom: 150, love: 89 };
    const progressData = wx.getStorageSync('progressData') || {};

    this.setData({
      captainName: userData.captainName || '小探索者',
      energyData: energyData,
      workshopProgress: progressData.workshop || 65,
      captainProgress: progressData.captain || 80,
      parentProgress: progressData.parent || 45,
      taskProgress: progressData.task || 30,
      todayTasksCount: progressData.todayTasksCount || 5,
      todayTasksCompleted: progressData.todayTasksCompleted || false
    });
  },

  // 保存用户数据
  saveUserData() {
    const userData = {
      captainName: this.data.captainName,
      lastSaveTime: Date.now()
    };

    const progressData = {
      workshop: this.data.workshopProgress,
      captain: this.data.captainProgress,
      parent: this.data.parentProgress,
      task: this.data.taskProgress,
      todayTasksCount: this.data.todayTasksCount,
      todayTasksCompleted: this.data.todayTasksCompleted
    };

    wx.setStorageSync('userData', userData);
    wx.setStorageSync('energyData', this.data.energyData);
    wx.setStorageSync('progressData', progressData);
  },

  // 刷新能量数据
  refreshEnergyData() {
    // 从其他页面同步能量数据
    const energyData = wx.getStorageSync('energyData') || this.data.energyData;
    const progressData = wx.getStorageSync('progressData') || {};

    this.setData({
      energyData: energyData,
      workshopProgress: progressData.workshop || this.data.workshopProgress,
      captainProgress: progressData.captain || this.data.captainProgress,
      parentProgress: progressData.parent || this.data.parentProgress,
      taskProgress: progressData.task || this.data.taskProgress
    });

    console.log('能量数据已刷新:', energyData);
  },

  // 播放背景音乐
  playBackgroundMusic() {
    // TODO: 实现背景音乐播放
    console.log('播放星际舰桥背景音乐');
  },

  // 显示开发中提示
  showDevelopmentToast(moduleName) {
    wx.showModal({
      title: '🚀 ' + moduleName,
      content: '功能正在开发中，敬请期待！\n\n我们正在为您打造最棒的宇宙探索体验。',
      showCancel: false,
      confirmText: '期待中',
      confirmColor: '#4D9FFF'
    });
  },

  // 导航到思维工坊
  onNavigateToWorkshop() {
    console.log('点击思维工坊');

    wx.navigateTo({
      url: '/pages/exploration/index'
    });
  },

  // 导航到船长舱室
  onNavigateToQuarters() {
    console.log('点击船长舱室');

    wx.navigateTo({
      url: '/pages/personalSpace/index'
    });
  },

  // 导航到宇宙灯塔
  onNavigateToBeacon() {
    console.log('点击宇宙灯塔');
    wx.navigateTo({
      url: '/pages/charity/index'
    });
  },

  // 导航到家长中心
  onNavigateToParent() {
    console.log('点击家长中心');

    // 保存当前活跃时间
    wx.setStorageSync('lastActiveTime', Date.now());

    // 导航到地球指挥部
    wx.navigateTo({
      url: '/pages/parent/index'
    });
  },

  // 开始今日任务
  onStartDailyMission() {
    console.log('点击今日任务');

    // 导航到今日任务页面
    wx.navigateTo({
      url: '/pages/dailyTasks/index'
    });
  },

  // 打开设置
  onOpenSettings() {
    console.log('点击设置');
    this.showDevelopmentToast('飞船系统设置');
  },

  // 打开通讯
  onOpenCommunication() {
    console.log('点击通讯');
    this.showDevelopmentToast('星际通讯系统');
  }
});
