# 探索星球模块测试指南

## 📋 测试概述

本文档提供了探索星球模块的完整测试指南，确保所有功能正常工作。

## 🎯 测试环境要求

### 开发工具
- 微信开发者工具 (最新版本)
- Node.js 环境
- 微信小程序基础库 2.0+

### 测试设备
- 开发者工具模拟器
- 真机测试 (iOS/Android)
- 不同屏幕尺寸设备

## 🧪 功能测试清单

### 1. 页面加载测试
- [ ] 从主界面点击"思维工坊"能正常跳转
- [ ] 探索星球页面能正常加载
- [ ] 星空背景动画正常显示
- [ ] 所有UI元素正确渲染

### 2. 探索者信息测试
- [ ] 探索者头像正常显示
- [ ] 等级信息正确显示
- [ ] 完成游戏数量正确统计
- [ ] 智慧能量数值正确显示

### 3. 星球交互测试
- [ ] 5个星球正确显示（音律、光影、智慧、语言、创造）
- [ ] 星球图标和名称正确
- [ ] 星球状态（解锁/锁定）正确显示
- [ ] 点击解锁星球能打开详情弹窗
- [ ] 点击锁定星球显示解锁提示

### 4. 星球详情弹窗测试
- [ ] 弹窗正常打开和关闭
- [ ] 星球信息正确显示
- [ ] 游戏列表正确显示
- [ ] "开始探索"按钮功能正常
- [ ] 锁定星球按钮显示为禁用状态

### 5. 游戏引擎测试
- [ ] 点击"开始探索"能跳转到游戏引擎
- [ ] 游戏启动界面正确显示
- [ ] 游戏信息（难度、时长、奖励）正确
- [ ] "开始游戏"按钮功能正常
- [ ] "返回"按钮能正常返回

### 6. 游戏流程测试
- [ ] 游戏进行界面正确显示
- [ ] 计时器正常工作
- [ ] 得分系统正常工作
- [ ] 演示按钮功能正常
- [ ] 游戏完成界面正确显示

### 7. 游戏完成测试
- [ ] 最终得分正确计算
- [ ] 表现评级正确显示
- [ ] 智慧能量奖励正确
- [ ] 成就解锁功能正常
- [ ] "继续探索"和"返回星图"按钮正常

### 8. 数据持久化测试
- [ ] 游戏进度正确保存
- [ ] 能量数据正确更新
- [ ] 星球解锁状态正确保存
- [ ] 页面刷新后数据保持

### 9. 动画效果测试
- [ ] 星空背景动画流畅
- [ ] 星球光晕呼吸效果正常
- [ ] 卡片浮动动画正常
- [ ] 3D拟物化效果正确
- [ ] 按钮点击反馈正常

### 10. 响应式测试
- [ ] 不同屏幕尺寸适配正常
- [ ] 横屏模式显示正常
- [ ] 字体大小适配正常
- [ ] 触摸区域大小合适

## 🐛 常见问题排查

### 问题1: 页面无法加载
**可能原因**: 
- explorationSystem.js文件路径错误
- 数据初始化失败

**解决方案**:
```javascript
// 检查utils/explorationSystem.js是否存在
// 检查require路径是否正确
const explorationSystem = require('../../utils/explorationSystem.js');
```

### 问题2: 星球状态显示错误
**可能原因**:
- 数据结构不匹配
- 状态计算逻辑错误

**解决方案**:
```javascript
// 检查planetStates数据结构
console.log('planetStates:', this.data.planetStates);
```

### 问题3: 游戏引擎跳转失败
**可能原因**:
- 页面路径错误
- 参数传递失败

**解决方案**:
```javascript
// 检查页面路径和参数
wx.navigateTo({
  url: `/pages/exploration/gameEngine/index?gameId=${gameId}`
});
```

### 问题4: 动画效果不流畅
**可能原因**:
- CSS动画过于复杂
- 设备性能限制

**解决方案**:
- 简化动画效果
- 使用transform代替position变化
- 添加will-change属性

### 问题5: 数据不同步
**可能原因**:
- 存储机制问题
- 页面间数据传递失败

**解决方案**:
```javascript
// 检查数据存储和读取
wx.setStorageSync('explorationData', data);
const data = wx.getStorageSync('explorationData');
```

## 📊 性能测试

### 1. 加载性能
- 页面首次加载时间 < 2秒
- 动画帧率保持 > 30fps
- 内存使用 < 50MB

### 2. 交互性能
- 点击响应时间 < 100ms
- 页面切换时间 < 500ms
- 动画流畅度 > 90%

### 3. 兼容性测试
- iOS 12+ 系统兼容
- Android 8+ 系统兼容
- 微信版本 7.0+ 兼容

## 🔧 调试工具

### 1. 控制台日志
```javascript
// 启用详细日志
console.log('探索星球页面加载');
console.log('星球状态:', planetStates);
console.log('游戏数据:', gameData);
```

### 2. 数据检查
```javascript
// 检查存储数据
console.log('探索数据:', wx.getStorageSync('explorationData'));
console.log('能量数据:', wx.getStorageSync('energyData'));
```

### 3. 性能监控
```javascript
// 监控页面性能
const startTime = Date.now();
// ... 执行操作
const endTime = Date.now();
console.log('操作耗时:', endTime - startTime, 'ms');
```

## ✅ 测试完成标准

### 基础功能 (必须通过)
- 所有页面正常加载
- 核心交互功能正常
- 数据正确保存和读取
- 无明显bug和错误

### 用户体验 (建议通过)
- 动画效果流畅
- 界面美观易用
- 响应速度快
- 兼容性良好

### 高级特性 (可选通过)
- 3D拟物化效果完美
- 性能优化到位
- 代码质量高
- 可维护性强

## 📝 测试报告模板

```
测试日期: [日期]
测试人员: [姓名]
测试环境: [设备/系统/微信版本]

功能测试结果:
- 页面加载: ✅/❌
- 星球交互: ✅/❌
- 游戏引擎: ✅/❌
- 数据同步: ✅/❌

性能测试结果:
- 加载时间: [X]秒
- 动画帧率: [X]fps
- 内存使用: [X]MB

发现问题:
1. [问题描述]
2. [问题描述]

建议改进:
1. [改进建议]
2. [改进建议]

总体评价: [优秀/良好/一般/需改进]
```

---

**测试完成后，探索星球模块将具备完整的功能和优秀的用户体验！**
