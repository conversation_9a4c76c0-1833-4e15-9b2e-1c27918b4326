// pages/exploration/cognitiveTraining/index.js
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: {},
    
    // 训练类型数据
    trainingTypes: [
      {
        id: 'language',
        name: '语言词汇',
        icon: '📚',
        color: '#FF9800',
        description: '提升语言表达和词汇理解能力',
        progress: 0,
        totalQuestions: 10,
        unlocked: true,
        difficulty: '初级',
        bestScore: 0
      },
      {
        id: 'attention',
        name: '专注力',
        icon: '🎯',
        color: '#2196F3',
        description: '训练注意力集中和持续专注',
        progress: 0,
        totalQuestions: 10,
        unlocked: true,
        difficulty: '初级',
        bestScore: 0
      },
      {
        id: 'visual',
        name: '视觉训练',
        icon: '👁️',
        color: '#4CAF50',
        description: '增强视觉认知和空间感知',
        progress: 0,
        totalQuestions: 10,
        unlocked: false,
        difficulty: '初级',
        bestScore: 0
      },
      {
        id: 'auditory',
        name: '听觉训练',
        icon: '🎵',
        color: '#9C27B0',
        description: '提升听觉理解和音乐感知',
        progress: 0,
        totalQuestions: 10,
        unlocked: false,
        difficulty: '初级',
        bestScore: 0
      }
    ],



    // 统计数据
    totalCompletedQuestions: 0,
    unlockedTrainingCount: 0,
    overallProgress: 0,

    // 动画状态
    animationData: {},
    cardAnimations: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('认知训练中心页面加载');
    this.initializeTrainingCenter();
    this.initializeAnimations();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadUserProgress();
  },

  /**
   * 初始化训练中心
   */
  initializeTrainingCenter() {
    // 获取用户信息
    const userInfo = app.globalData.userInfo || {};
    
    // 加载训练进度
    this.loadTrainingProgress();
    
    this.setData({
      userInfo: userInfo
    });
  },

  /**
   * 初始化动画
   */
  initializeAnimations() {
    const cardAnimations = [];
    
    // 为每个训练卡片创建动画
    for (let i = 0; i < 4; i++) {
      const animation = wx.createAnimation({
        duration: 800,
        timingFunction: 'ease-out',
        delay: i * 200
      });
      
      animation.translateY(50).opacity(0).step();
      animation.translateY(0).opacity(1).step();
      
      cardAnimations.push(animation.export());
    }
    
    this.setData({
      cardAnimations: cardAnimations
    });
  },

  /**
   * 加载训练进度
   */
  loadTrainingProgress() {
    try {
      const progressData = wx.getStorageSync('cognitiveTrainingProgress') || {};
      const trainingTypes = this.data.trainingTypes.map(type => {
        const progress = progressData[type.id] || { completed: 0, unlocked: type.unlocked };
        return {
          ...type,
          progress: progress.completed,
          unlocked: progress.unlocked
        };
      });
      
      // 计算统计数据
      const totalCompleted = trainingTypes.reduce((sum, type) => sum + type.progress, 0);
      const unlockedCount = trainingTypes.filter(type => type.unlocked).length;
      const overallProgress = Math.round((totalCompleted / 40) * 100); // 总共40道题

      this.setData({
        trainingTypes: trainingTypes,
        totalCompletedQuestions: totalCompleted,
        unlockedTrainingCount: unlockedCount,
        overallProgress: overallProgress
      });
    } catch (error) {
      console.error('加载训练进度失败:', error);
    }
  },

  /**
   * 加载用户进度
   */
  loadUserProgress() {
    // 重新加载进度数据
    this.loadTrainingProgress();
  },

  /**
   * 点击训练类型卡片
   */
  onTrainingTap(e) {
    const trainingId = e.currentTarget.dataset.training;
    const training = this.data.trainingTypes.find(t => t.id === trainingId);

    if (!training) return;

    console.log('选择训练类型:', training.name);

    // 检查是否解锁
    if (!training.unlocked) {
      wx.showModal({
        title: '训练未解锁',
        content: '请先完成前面的训练项目',
        showCancel: false,
        confirmText: '知道了'
      });
      return;
    }

    // 直接导航到训练游戏
    wx.navigateTo({
      url: `/pages/exploration/cognitiveTraining/game/index?type=${training.id}&name=${training.name}`
    });
  },





  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('下拉刷新');
    this.loadUserProgress();
    wx.stopPullDownRefresh();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '来《能量星球》挑战认知训练吧！',
      path: '/pages/exploration/cognitiveTraining/index',
      imageUrl: '/images/share-cognitive.png'
    };
  }
});
