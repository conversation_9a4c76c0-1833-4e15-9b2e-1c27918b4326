/**
 * 《能量星球》愿望系统管理工具类
 * 负责愿望创建、管理、家长协作等功能
 */

const wishSystem = {
  // 愿望分类配置
  wishCategories: {
    learning: {
      name: '学习目标',
      icon: '📚',
      color: '#4CAF50',
      energyCost: 30
    },
    hobby: {
      name: '兴趣爱好',
      icon: '🎨',
      color: '#FF9800',
      energyCost: 40
    },
    item: {
      name: '物品需求',
      icon: '🎁',
      color: '#E91E63',
      energyCost: 50
    },
    experience: {
      name: '体验愿望',
      icon: '🌟',
      color: '#9C27B0',
      energyCost: 60
    }
  },

  // 愿望状态
  wishStatus: {
    pending: '等待中',
    approved: '已批准',
    rejected: '需修改',
    completed: '已实现',
    cancelled: '已取消'
  },

  /**
   * 获取愿望汇总信息
   */
  getWishSummary: function() {
    try {
      const wishes = this.getAllWishes();
      const activeWishes = wishes.filter(w => 
        w.status === 'pending' || w.status === 'approved'
      );
      
      return {
        totalCount: wishes.length,
        activeCount: activeWishes.length,
        completedCount: wishes.filter(w => w.status === 'completed').length,
        nextCost: this.calculateNextWishCost(),
        recentWishes: wishes.slice(-3)
      };
    } catch (error) {
      console.error('获取愿望汇总失败:', error);
      return {
        totalCount: 0,
        activeCount: 0,
        completedCount: 0,
        nextCost: 50,
        recentWishes: []
      };
    }
  },

  /**
   * 获取所有愿望
   */
  getAllWishes: function() {
    try {
      return wx.getStorageSync('wishes') || [];
    } catch (error) {
      console.error('获取愿望列表失败:', error);
      return [];
    }
  },

  /**
   * 创建新愿望
   */
  createWish: function(wishData) {
    try {
      // 检查智慧能量是否足够
      const energyCost = this.calculateWishCost(wishData.category);
      if (!this.checkEnergyAvailable(energyCost)) {
        return {
          success: false,
          error: '智慧能量不足',
          required: energyCost
        };
      }

      // 生成愿望ID
      const wishId = 'wish_' + Date.now();
      
      // 创建愿望对象
      const wish = {
        id: wishId,
        title: wishData.title,
        description: wishData.description,
        category: wishData.category,
        priority: wishData.priority || 'normal',
        energyCost: energyCost,
        status: 'pending',
        createDate: new Date().toISOString(),
        targetDate: wishData.targetDate,
        progress: 0,
        parentResponse: null,
        tags: wishData.tags || []
      };

      // 保存愿望
      const wishes = this.getAllWishes();
      wishes.push(wish);
      wx.setStorageSync('wishes', wishes);

      // 消耗智慧能量
      this.consumeWisdomEnergy(energyCost);

      // 通知家长
      this.notifyParent(wish);

      return {
        success: true,
        wish: wish,
        energyUsed: energyCost
      };

    } catch (error) {
      console.error('创建愿望失败:', error);
      return {
        success: false,
        error: '创建失败，请重试'
      };
    }
  },

  /**
   * 计算愿望成本
   */
  calculateWishCost: function(category) {
    const baseCost = this.wishCategories[category]?.energyCost || 50;
    const activeWishes = this.getAllWishes().filter(w => 
      w.status === 'pending' || w.status === 'approved'
    );
    
    // 活跃愿望越多，成本越高
    const multiplier = 1 + (activeWishes.length * 0.1);
    return Math.floor(baseCost * multiplier);
  },

  /**
   * 计算下一个愿望的成本
   */
  calculateNextWishCost: function() {
    // 假设下一个愿望是普通类别
    return this.calculateWishCost('learning');
  },

  /**
   * 检查智慧能量是否足够
   */
  checkEnergyAvailable: function(required) {
    try {
      const app = getApp();
      const currentEnergy = app.globalData.wisdomEnergy || 0;
      return currentEnergy >= required;
    } catch (error) {
      console.error('检查能量失败:', error);
      return false;
    }
  },

  /**
   * 消耗智慧能量
   */
  consumeWisdomEnergy: function(amount) {
    try {
      const app = getApp();
      app.globalData.wisdomEnergy = Math.max(0, 
        (app.globalData.wisdomEnergy || 0) - amount
      );
      
      // 保存到本地存储
      wx.setStorageSync('wisdomEnergy', app.globalData.wisdomEnergy);
      
      return true;
    } catch (error) {
      console.error('消耗智慧能量失败:', error);
      return false;
    }
  },

  /**
   * 通知家长
   */
  notifyParent: function(wish) {
    try {
      // 获取家长通知列表
      const parentNotifications = wx.getStorageSync('parentNotifications') || [];
      
      // 创建通知
      const notification = {
        id: 'wish_notify_' + wish.id,
        type: 'new_wish',
        wishId: wish.id,
        title: '新愿望待审核',
        message: `孩子创建了新愿望：${wish.title}`,
        date: new Date().toISOString(),
        read: false
      };
      
      parentNotifications.push(notification);
      wx.setStorageSync('parentNotifications', parentNotifications);
      
      console.log('已通知家长新愿望:', wish.title);
      return true;
    } catch (error) {
      console.error('通知家长失败:', error);
      return false;
    }
  },

  /**
   * 更新愿望状态
   */
  updateWishStatus: function(wishId, status, parentResponse = null) {
    try {
      const wishes = this.getAllWishes();
      const wishIndex = wishes.findIndex(w => w.id === wishId);
      
      if (wishIndex === -1) {
        return false;
      }
      
      wishes[wishIndex].status = status;
      wishes[wishIndex].updateDate = new Date().toISOString();
      
      if (parentResponse) {
        wishes[wishIndex].parentResponse = parentResponse;
      }
      
      wx.setStorageSync('wishes', wishes);
      
      // 如果愿望被拒绝，退还部分能量
      if (status === 'rejected') {
        this.refundEnergy(wishes[wishIndex].energyCost * 0.5);
      }
      
      return true;
    } catch (error) {
      console.error('更新愿望状态失败:', error);
      return false;
    }
  },

  /**
   * 退还能量
   */
  refundEnergy: function(amount) {
    try {
      const app = getApp();
      app.globalData.wisdomEnergy = (app.globalData.wisdomEnergy || 0) + amount;
      wx.setStorageSync('wisdomEnergy', app.globalData.wisdomEnergy);
      return true;
    } catch (error) {
      console.error('退还能量失败:', error);
      return false;
    }
  },

  /**
   * 完成愿望
   */
  completeWish: function(wishId, completionNote = '') {
    try {
      const wishes = this.getAllWishes();
      const wishIndex = wishes.findIndex(w => w.id === wishId);
      
      if (wishIndex === -1) {
        return false;
      }
      
      wishes[wishIndex].status = 'completed';
      wishes[wishIndex].completionDate = new Date().toISOString();
      wishes[wishIndex].completionNote = completionNote;
      
      wx.setStorageSync('wishes', wishes);
      
      // 给予完成奖励
      this.grantCompletionReward(wishes[wishIndex]);
      
      return true;
    } catch (error) {
      console.error('完成愿望失败:', error);
      return false;
    }
  },

  /**
   * 给予完成奖励
   */
  grantCompletionReward: function(wish) {
    try {
      // 根据愿望类别给予不同奖励
      const rewards = {
        learning: { exp: 50, love: 20 },
        hobby: { exp: 30, love: 30 },
        item: { exp: 20, love: 10 },
        experience: { exp: 40, love: 40 }
      };
      
      const reward = rewards[wish.category] || rewards.learning;
      
      // 增加经验值
      const personalSystem = require('./personalSystem.js');
      personalSystem.addExperience(reward.exp);
      
      // 增加爱心能量
      const app = getApp();
      app.globalData.loveEnergy = (app.globalData.loveEnergy || 0) + reward.love;
      wx.setStorageSync('loveEnergy', app.globalData.loveEnergy);
      
      console.log('愿望完成奖励:', reward);
      return reward;
    } catch (error) {
      console.error('给予完成奖励失败:', error);
      return null;
    }
  },

  /**
   * 获取愿望详情
   */
  getWishById: function(wishId) {
    try {
      const wishes = this.getAllWishes();
      return wishes.find(w => w.id === wishId) || null;
    } catch (error) {
      console.error('获取愿望详情失败:', error);
      return null;
    }
  },

  /**
   * 删除愿望
   */
  deleteWish: function(wishId) {
    try {
      const wishes = this.getAllWishes();
      const filteredWishes = wishes.filter(w => w.id !== wishId);
      wx.setStorageSync('wishes', filteredWishes);
      return true;
    } catch (error) {
      console.error('删除愿望失败:', error);
      return false;
    }
  }
};

module.exports = wishSystem;
