/* pages/exploration/cognitiveTraining/index.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  padding: 0;
  position: relative;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255, 152, 0, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(33, 150, 243, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(76, 175, 80, 0.08) 0%, transparent 50%);
  pointer-events: none;
}



/* 总体进度展示 */
.progress-overview {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin: 40rpx 30rpx 40rpx;
  padding: 30rpx;
  background: linear-gradient(145deg, rgba(26, 26, 46, 0.9), rgba(22, 33, 62, 0.8));
  border-radius: 24rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.3),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -2rpx 0 rgba(0, 0, 0, 0.3),
    inset -2rpx 0 0 rgba(0, 0, 0, 0.2),
    inset 2rpx 0 0 rgba(255, 255, 255, 0.1);
}

.overview-item {
  text-align: center;
  flex: 1;
}

.overview-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 8rpx;
}

.overview-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}

.overview-divider {
  width: 2rpx;
  height: 50rpx;
  background: rgba(255, 255, 255, 0.2);
  margin: 0 20rpx;
}

/* 训练列表 */
.training-list {
  padding: 0 30rpx;
  margin-bottom: 60rpx;
}

/* 训练项目 */
.training-item {
  position: relative;
  background: linear-gradient(145deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.85));
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  display: flex;
  align-items: center;
  padding: 30rpx;
  min-height: 160rpx;
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.25),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.15),
    inset 0 -2rpx 0 rgba(0, 0, 0, 0.25),
    inset -2rpx 0 0 rgba(0, 0, 0, 0.15),
    inset 2rpx 0 0 rgba(255, 255, 255, 0.08);
}

.training-item:active {
  transform: scale(0.98);
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.4),
    inset 0 3rpx 6rpx rgba(0, 0, 0, 0.3),
    inset 0 -1rpx 0 rgba(255, 255, 255, 0.1),
    inset -2rpx 0 0 rgba(0, 0, 0, 0.2),
    inset 2rpx 0 0 rgba(255, 255, 255, 0.05);
}

.training-item.locked {
  opacity: 0.6;
  filter: grayscale(0.3);
}

.training-item.unlocked:hover {
  transform: translateY(-4rpx);
  box-shadow:
    0 16rpx 48rpx rgba(0, 0, 0, 0.3),
    inset 0 3rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -3rpx 0 rgba(0, 0, 0, 0.3),
    inset -3rpx 0 0 rgba(0, 0, 0, 0.2),
    inset 3rpx 0 0 rgba(255, 255, 255, 0.1);
}

/* 左侧图标区域 */
.item-left {
  margin-right: 24rpx;
  flex-shrink: 0;
}

.training-icon-wrapper {
  width: 100rpx;
  height: 100rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow:
    0 8rpx 24rpx rgba(0, 0, 0, 0.3),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.3),
    inset 0 -2rpx 0 rgba(0, 0, 0, 0.2),
    inset -2rpx 0 0 rgba(0, 0, 0, 0.15),
    inset 2rpx 0 0 rgba(255, 255, 255, 0.2);
}

.training-icon {
  font-size: 48rpx;
  color: #ffffff;
  z-index: 2;
  position: relative;
}

.icon-decoration {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 24rpx;
  height: 24rpx;
  border: 3rpx solid;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
}

/* 中间信息区域 */
.item-center {
  flex: 1;
  margin-right: 20rpx;
}

.training-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.training-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
}

.training-status {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.training-status.unlocked {
  background: linear-gradient(145deg, rgba(76, 175, 80, 0.25), rgba(76, 175, 80, 0.15));
  color: #4CAF50;
  border: 1rpx solid rgba(76, 175, 80, 0.3);
  box-shadow:
    0 2rpx 4rpx rgba(76, 175, 80, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.1);
}

.training-status.locked {
  background: linear-gradient(145deg, rgba(158, 158, 158, 0.25), rgba(158, 158, 158, 0.15));
  color: #9E9E9E;
  border: 1rpx solid rgba(158, 158, 158, 0.3);
  box-shadow:
    0 2rpx 4rpx rgba(158, 158, 158, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.1);
}

.training-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.training-details {
  display: flex;
  gap: 12rpx;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.detail-tag {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.08), rgba(0, 0, 0, 0.1));
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.15);
  box-shadow:
    0 2rpx 4rpx rgba(0, 0, 0, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.1);
}

.progress-container {
  margin-top: 8rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: linear-gradient(145deg, rgba(0, 0, 0, 0.3), rgba(255, 255, 255, 0.1));
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
  box-shadow:
    inset 0 2rpx 4rpx rgba(0, 0, 0, 0.3),
    inset 0 -1rpx 0 rgba(255, 255, 255, 0.1);
}

.progress-fill {
  height: 100%;
  border-radius: 4rpx;
  transition: width 0.3s ease;
  box-shadow:
    0 1rpx 2rpx rgba(0, 0, 0, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
}

.progress-text {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* 右侧操作区域 */
.item-right {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  flex-shrink: 0;
}

.action-btn {
  width: 120rpx;
  height: 60rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow:
    0 4rpx 12rpx rgba(0, 0, 0, 0.25),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.2);
}

.action-btn.enabled {
  color: #ffffff;
  font-weight: 500;
}

.action-btn.enabled:active {
  transform: scale(0.95);
  box-shadow:
    0 2rpx 6rpx rgba(0, 0, 0, 0.3),
    inset 0 2rpx 4rpx rgba(0, 0, 0, 0.2),
    inset 0 -1rpx 0 rgba(255, 255, 255, 0.1);
}

.action-btn.disabled {
  background: #666666 !important;
  color: #999999;
}

.btn-text {
  font-size: 22rpx;
}

.score-display {
  text-align: center;
}

.score-text {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.6);
}

/* 背景装饰 */
.item-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 24rpx;
  opacity: 0.3;
  pointer-events: none;
}



/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.training-modal {
  width: 600rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  overflow: hidden;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transform: scale(0.8) translateY(100rpx);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-overlay.show .training-modal {
  transform: scale(1) translateY(0);
}

.modal-header {
  padding: 40rpx;
  text-align: center;
  position: relative;
  color: #ffffff;
}

.modal-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
}

.close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #ffffff;
  transition: all 0.3s ease;
}

.close-btn:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.3);
}

.modal-content {
  padding: 40rpx;
  background: #ffffff;
}

.training-description {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 30rpx;
  text-align: center;
}

.modal-training-details {
  margin-bottom: 40rpx;
}

.modal-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-detail-item:last-child {
  border-bottom: none;
}

.modal-detail-label {
  font-size: 28rpx;
  color: #333333;
}

.modal-detail-value {
  font-size: 28rpx;
  color: #666666;
  font-weight: 500;
}

.modal-actions {
  display: flex;
  gap: 20rpx;
}

.modal-action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666666;
}

.start-btn {
  background: #4CAF50;
  color: #ffffff;
}

.start-btn.disabled {
  background: #cccccc;
  color: #999999;
}

.modal-action-btn:active {
  transform: scale(0.98);
}
