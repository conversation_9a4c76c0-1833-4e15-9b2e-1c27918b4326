<!--家庭善意计划页面-->
<view class="family-plan-container">
  
  <!-- 背景装饰 -->
  <view class="family-background">
    <view class="floating-heart heart-1">👨‍👩‍👧‍👦</view>
    <view class="floating-heart heart-2">💕</view>
    <view class="floating-heart heart-3">🏠</view>
    <view class="floating-heart heart-4">❤️</view>
    <view class="warm-glow glow-1"></view>
    <view class="warm-glow glow-2"></view>
    <view class="warm-glow glow-3"></view>
  </view>

  <!-- 页面标题 -->
  <view class="family-header">
    <view class="header-icon">👨‍👩‍👧‍👦</view>
    <view class="header-title">家庭善意计划</view>
    <view class="header-subtitle">全家一起传播善意与温暖</view>
  </view>

  <!-- 家庭状态面板 -->
  <view class="family-status-panel">
    <view class="status-card family-info">
      <view class="card-icon">🏠</view>
      <view class="card-content">
        <view class="card-title">我的家庭</view>
        <view class="card-value">{{familyMembers.length}}位成员</view>
        <view class="card-subtitle">共同参与善意计划</view>
      </view>
    </view>
    
    <view class="status-card plan-stats">
      <view class="card-icon">📋</view>
      <view class="card-content">
        <view class="card-title">活跃计划</view>
        <view class="card-value">{{activePlans.length}}个</view>
        <view class="card-subtitle">正在进行中</view>
      </view>
    </view>
    
    <view class="status-card completion-rate">
      <view class="card-icon">🎯</view>
      <view class="card-content">
        <view class="card-title">完成率</view>
        <view class="card-value">{{completionRate}}%</view>
        <view class="card-subtitle">本月家庭计划</view>
      </view>
    </view>
  </view>

  <!-- 快速创建计划 -->
  <view class="quick-create-section">
    <view class="section-title">
      <view class="title-icon">⚡</view>
      <view class="title-text">快速创建计划</view>
    </view>
    
    <view class="quick-templates">
      <view class="template-card" 
            wx:for="{{quickTemplates}}" 
            wx:key="id"
            bindtap="onSelectTemplate" 
            data-template="{{item}}">
        <view class="template-icon">{{item.icon}}</view>
        <view class="template-info">
          <view class="template-title">{{item.title}}</view>
          <view class="template-description">{{item.description}}</view>
          <view class="template-duration">预计{{item.duration}}</view>
        </view>
        <view class="template-arrow">→</view>
        <view class="template-glow"></view>
      </view>
    </view>
  </view>

  <!-- 当前计划 -->
  <view class="current-plans-section">
    <view class="section-title">
      <view class="title-icon">📋</view>
      <view class="title-text">当前计划</view>
      <view class="create-btn" bindtap="onCreateCustomPlan">
        <view class="btn-icon">➕</view>
        <view class="btn-text">自定义</view>
      </view>
    </view>
    
    <view class="plans-list" wx:if="{{activePlans.length > 0}}">
      <view class="plan-item" 
            wx:for="{{activePlans}}" 
            wx:key="id"
            bindtap="onViewPlan" 
            data-plan="{{item}}">
        <view class="plan-header">
          <view class="plan-icon">{{item.icon}}</view>
          <view class="plan-info">
            <view class="plan-title">{{item.title}}</view>
            <view class="plan-subtitle">{{item.subtitle}}</view>
          </view>
          <view class="plan-status {{item.status}}">
            <text wx:if="{{item.status === 'active'}}">进行中</text>
            <text wx:if="{{item.status === 'pending'}}">待开始</text>
            <text wx:if="{{item.status === 'completed'}}">已完成</text>
          </view>
        </view>
        
        <view class="plan-progress">
          <view class="progress-info">
            <view class="progress-text">进度：{{item.completedTasks}}/{{item.totalTasks}}</view>
            <view class="progress-percentage">{{item.progressPercentage}}%</view>
          </view>
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{item.progressPercentage}}%;"></view>
          </view>
        </view>
        
        <view class="plan-participants">
          <view class="participants-label">参与成员：</view>
          <view class="participants-list">
            <view class="participant" 
                  wx:for="{{item.participants}}" 
                  wx:key="id"
                  wx:for-item="participant">
              <view class="participant-avatar">{{participant.avatar}}</view>
              <view class="participant-name">{{participant.name}}</view>
            </view>
          </view>
        </view>
        
        <view class="plan-actions">
          <view class="action-btn update-btn" 
                bindtap="onUpdateProgress" 
                data-plan="{{item}}">
            <view class="btn-icon">📝</view>
            <view class="btn-text">更新进度</view>
          </view>
          <view class="action-btn complete-btn" 
                wx:if="{{item.progressPercentage === 100}}"
                bindtap="onCompletePlan" 
                data-plan="{{item}}">
            <view class="btn-icon">✅</view>
            <view class="btn-text">完成计划</view>
          </view>
        </view>
      </view>
    </view>
    
    <view class="no-plans" wx:else>
      <view class="no-plans-icon">📝</view>
      <view class="no-plans-text">暂无活跃的家庭计划</view>
      <view class="no-plans-tip">点击上方模板或自定义按钮创建新计划</view>
    </view>
  </view>

  <!-- 家庭成员 -->
  <view class="family-members-section">
    <view class="section-title">
      <view class="title-icon">👥</view>
      <view class="title-text">家庭成员</view>
      <view class="manage-btn" bindtap="onManageMembers">管理</view>
    </view>
    
    <view class="members-grid">
      <view class="member-card" 
            wx:for="{{familyMembers}}" 
            wx:key="id"
            bindtap="onViewMember" 
            data-member="{{item}}">
        <view class="member-avatar">{{item.avatar}}</view>
        <view class="member-info">
          <view class="member-name">{{item.name}}</view>
          <view class="member-role">{{item.role}}</view>
          <view class="member-contribution">贡献：{{item.contribution}}分</view>
        </view>
        <view class="member-status {{item.status}}">
          <view class="status-dot"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 计划历史 -->
  <view class="plan-history-section">
    <view class="section-title">
      <view class="title-icon">📚</view>
      <view class="title-text">计划历史</view>
      <view class="view-all-btn" bindtap="onViewAllHistory">查看全部</view>
    </view>
    
    <view class="history-timeline">
      <view class="timeline-item" 
            wx:for="{{recentHistory}}" 
            wx:key="id">
        <view class="timeline-marker">
          <view class="marker-icon">{{item.icon}}</view>
        </view>
        <view class="timeline-content">
          <view class="timeline-title">{{item.title}}</view>
          <view class="timeline-description">{{item.description}}</view>
          <view class="timeline-time">{{item.time}}</view>
          <view class="timeline-participants">
            参与者：
            <text wx:for="{{item.participants}}" wx:key="name" wx:for-item="participant">
              {{participant}}{{index < item.participants.length - 1 ? '、' : ''}}
            </text>
          </view>
        </view>
        <view class="timeline-line" wx:if="{{index < recentHistory.length - 1}}"></view>
      </view>
    </view>
  </view>

  <!-- 家庭成就 -->
  <view class="family-achievements-section">
    <view class="section-title">
      <view class="title-icon">🏆</view>
      <view class="title-text">家庭成就</view>
    </view>
    
    <view class="achievements-grid">
      <view class="achievement-card {{item.unlocked ? 'unlocked' : 'locked'}}" 
            wx:for="{{familyAchievements}}" 
            wx:key="id"
            bindtap="onViewAchievement" 
            data-achievement="{{item}}">
        <view class="achievement-icon">
          {{item.unlocked ? item.icon : '🔒'}}
        </view>
        <view class="achievement-info">
          <view class="achievement-name">{{item.name}}</view>
          <view class="achievement-description">{{item.description}}</view>
          <view class="achievement-progress" wx:if="{{!item.unlocked}}">
            <view class="progress-bar">
              <view class="progress-fill" style="width: {{item.progressPercentage}}%;"></view>
            </view>
            <view class="progress-text">{{item.current}}/{{item.target}}</view>
          </view>
          <view class="achievement-date" wx:else>
            {{item.unlockedDate}}
          </view>
        </view>
        <view class="achievement-glow" wx:if="{{item.unlocked}}"></view>
      </view>
    </view>
  </view>

  <!-- 家庭统计 -->
  <view class="family-stats-section">
    <view class="section-title">
      <view class="title-icon">📊</view>
      <view class="title-text">家庭统计</view>
    </view>
    
    <view class="stats-cards">
      <view class="stat-card">
        <view class="stat-icon">📋</view>
        <view class="stat-number">{{familyStats.totalPlans}}</view>
        <view class="stat-label">总计划数</view>
      </view>
      <view class="stat-card">
        <view class="stat-icon">✅</view>
        <view class="stat-number">{{familyStats.completedPlans}}</view>
        <view class="stat-label">已完成</view>
      </view>
      <view class="stat-card">
        <view class="stat-icon">⭐</view>
        <view class="stat-number">{{familyStats.totalPoints}}</view>
        <view class="stat-label">家庭积分</view>
      </view>
      <view class="stat-card">
        <view class="stat-icon">🏆</view>
        <view class="stat-number">{{familyStats.achievements}}</view>
        <view class="stat-label">家庭成就</view>
      </view>
    </view>
    
    <view class="monthly-chart">
      <view class="chart-title">本月完成情况</view>
      <view class="chart-bars">
        <view class="chart-bar" 
              wx:for="{{monthlyData}}" 
              wx:key="week">
          <view class="bar-fill" style="height: {{item.percentage}}%;"></view>
          <view class="bar-label">第{{item.week}}周</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-spinner">
      <view class="spinner-icon">👨‍👩‍👧‍👦</view>
      <view class="loading-text">加载家庭计划...</view>
    </view>
  </view>

  <!-- 计划创建成功动画 -->
  <view class="plan-success-overlay" wx:if="{{showPlanSuccess}}">
    <view class="success-content">
      <view class="success-icon">🎉</view>
      <view class="success-message">{{planSuccessMessage}}</view>
      <view class="success-family">
        <view class="family-member member-1">👨</view>
        <view class="family-member member-2">👩</view>
        <view class="family-member member-3">👧</view>
        <view class="family-member member-4">👦</view>
        <view class="family-heart">❤️</view>
      </view>
    </view>
  </view>

</view>
