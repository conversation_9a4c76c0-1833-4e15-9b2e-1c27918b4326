<!--《能量星球》沉浸式飞船舰桥 - 主界面-->
<view class="page spaceship-cockpit">
  <!-- 动态背景粒子系统 -->
  <view class="particle-system">
    <view class="floating-particle p1"></view>
    <view class="floating-particle p2"></view>
    <view class="floating-particle p3"></view>
    <view class="floating-particle p4"></view>
    <view class="floating-particle p5"></view>
    <view class="floating-particle p6"></view>
    <view class="floating-particle p7"></view>
    <view class="floating-particle p8"></view>
  </view>

  <!-- HUD 抬头显示器 -->
  <view class="hud-display">
    <!-- 左侧：能量状态 -->
    <view class="energy-status">
      <view class="energy-orb">
        <view class="orb-core"></view>
        <view class="orb-ring"></view>
        <text class="energy-value">{{energyData.wisdom}}</text>
      </view>
      <text class="energy-label">💎 智慧能量</text>
    </view>

    <!-- 中央：宇宙标语横幅 -->
    <view class="cosmic-banner">
      <view class="banner-glow"></view>
      <view class="banner-content">
        <view class="banner-line top"></view>
        <text class="cosmic-slogan">寓教于乐、寓善于乐</text>
        <view class="banner-line bottom"></view>
      </view>
      <view class="banner-particles">
        <view class="particle p1"></view>
        <view class="particle p2"></view>
        <view class="particle p3"></view>
      </view>
    </view>

    <!-- 右侧：爱心状态 -->
    <view class="love-status">
      <view class="love-orb">
        <view class="orb-core love"></view>
        <view class="orb-ring love"></view>
        <text class="love-value">{{energyData.love}}</text>
      </view>
      <text class="love-label">❤️ 爱心能量</text>
    </view>
  </view>

  <!-- 主舷窗区域 -->
  <view class="main-viewport">
    <!-- 宇宙背景 -->
    <view class="space-background">
      <!-- 星空层 -->
      <view class="stars-layer">
        <view class="star star-1"></view>
        <view class="star star-2"></view>
        <view class="star star-3"></view>
        <view class="star star-4"></view>
        <view class="star star-5"></view>
        <view class="star star-6"></view>
      </view>

      <!-- 星云层 -->
      <view class="nebula-layer">
        <view class="nebula nebula-1"></view>
        <view class="nebula nebula-2"></view>
      </view>

    <!-- 流星层 -->
    <view class="meteor-layer">
      <view class="meteor meteor-1"></view>
      <view class="meteor meteor-2"></view>
      <view class="meteor meteor-3"></view>
      <view class="meteor meteor-4"></view>
      <view class="meteor meteor-5"></view>
      <view class="meteor meteor-6"></view>
    </view>
    </view>

    <!-- 全息星图 (思维工坊入口) -->
    <view class="holographic-starmap" bindtap="onNavigateToWorkshop">
      <view class="starmap-core">
        <view class="starmap-ring ring-1"></view>
        <view class="starmap-ring ring-2"></view>
        <view class="starmap-ring ring-3"></view>
        <view class="starmap-center">
          <view class="center-glow"></view>
          <text class="starmap-icon">🌟</text>
          <text class="starmap-title">探索星球</text>
          <text class="starmap-subtitle">思维工坊</text>
          <view class="progress-ring">
            <view class="progress-fill" style="transform: rotate({{workshopProgress * 3.6}}deg);"></view>
          </view>
        </view>
        <view class="starmap-satellites">
          <view class="satellite sat-1">🎵</view>
          <view class="satellite sat-2">👁️</view>
          <view class="satellite sat-3">🧠</view>
          <view class="satellite sat-4">💬</view>
          <view class="satellite sat-5">🎨</view>
        </view>
      </view>
    </view>

    <!-- 宇宙灯塔星云 -->
    <view class="cosmic-lighthouse" bindtap="onNavigateToBeacon">
      <view class="lighthouse-glow"></view>
      <view class="lighthouse-beam"></view>
      <view class="lighthouse-core">
        <view class="lighthouse-inner">
          <text class="lighthouse-icon">💫</text>
        </view>
        <view class="lighthouse-rings">
          <view class="ring r1"></view>
          <view class="ring r2"></view>
          <view class="ring r3"></view>
        </view>
      </view>
      <text class="lighthouse-label">宇宙灯塔</text>
      <view class="lighthouse-status">
        <text class="status-text">{{lighthouseStatus}}</text>
      </view>
    </view>

    <!-- 船长舱室 - 能量水晶 -->
    <view class="energy-crystal quarters" bindtap="onNavigateToQuarters">
      <view class="crystal-base">
        <view class="crystal-facet f1"></view>
        <view class="crystal-facet f2"></view>
        <view class="crystal-facet f3"></view>
        <view class="crystal-facet f4"></view>
      </view>
      <view class="crystal-core">
        <text class="crystal-icon">🏠</text>
      </view>
      <view class="crystal-energy-flow">
        <view class="energy-beam b1"></view>
        <view class="energy-beam b2"></view>
        <view class="energy-beam b3"></view>
      </view>
      <text class="crystal-label">船长舱室</text>
    </view>

    <!-- 今日任务 - 数据终端 -->
    <view class="data-terminal mission" bindtap="onStartDailyMission">
      <view class="terminal-screen">
        <view class="screen-glow"></view>
        <view class="screen-content">
          <view class="data-line l1"></view>
          <view class="data-line l2"></view>
          <view class="data-line l3"></view>
          <text class="terminal-icon">📋</text>
        </view>
      </view>
      <view class="terminal-base"></view>
      <text class="terminal-label">今日任务</text>
    </view>

    <!-- 家长中心 - 传送门 -->
    <view class="portal-gate parent" bindtap="onNavigateToParent">
      <view class="portal-ring outer"></view>
      <view class="portal-ring middle"></view>
      <view class="portal-ring inner"></view>
      <view class="portal-core">
        <view class="portal-vortex"></view>
      </view>
      <view class="portal-energy">
        <view class="energy-spark s1"></view>
        <view class="energy-spark s2"></view>
        <view class="energy-spark s3"></view>
        <view class="energy-spark s4"></view>
      </view>
      <text class="portal-label">地球指挥部</text>
    </view>
  </view>


</view>
