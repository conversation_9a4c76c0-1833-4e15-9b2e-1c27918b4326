/**
 * 《能量星球》探索系统管理工具类
 * 负责认知训练游戏、星球管理、智慧能量奖励等功能
 */

const explorationSystem = {
  // 星球配置 - 5大认知训练系列
  planets: {
    auditory: {
      id: 'auditory',
      name: '音律星球',
      icon: '🎵',
      theme: '听觉认知训练',
      color: '#9C27B0',
      description: '在这里训练你的听觉和理解能力',
      unlockLevel: 1,
      games: ['story_comprehension', 'instruction_following', 'music_rhythm', 'sound_recognition']
    },
    visual: {
      id: 'visual', 
      name: '光影星球',
      icon: '👁️',
      theme: '视觉认知训练',
      color: '#2196F3',
      description: '锻炼你的观察力和视觉记忆',
      unlockLevel: 1,
      games: ['shape_matching', 'color_sorting', 'spot_difference', 'sequence_memory']
    },
    logic: {
      id: 'logic',
      name: '智慧星球', 
      icon: '🧠',
      theme: '逻辑思维训练',
      color: '#FF9800',
      description: '提升逻辑推理和问题解决能力',
      unlockLevel: 2,
      games: ['simple_reasoning', 'classification', 'cause_effect', 'pattern_recognition']
    },
    language: {
      id: 'language',
      name: '语言星球',
      icon: '💬', 
      theme: '语言表达训练',
      color: '#4CAF50',
      description: '增强语言理解和表达能力',
      unlockLevel: 2,
      games: ['vocabulary_learning', 'sentence_building', 'story_creation', 'word_association']
    },
    creative: {
      id: 'creative',
      name: '创造星球',
      icon: '🎨',
      theme: '创造性游戏',
      color: '#E91E63',
      description: '发挥想象力，创造属于你的宇宙',
      unlockLevel: 3,
      games: ['ar_exploration', 'space_building', 'character_design', 'story_maker']
    }
  },

  // 游戏配置
  games: {
    // 听觉训练游戏
    story_comprehension: {
      id: 'story_comprehension',
      name: '故事理解',
      type: 'auditory',
      description: '听故事回答问题',
      energyReward: { min: 5, max: 15 },
      ageGroups: {
        '3-4': { duration: 2, complexity: 'simple' },
        '4-5': { duration: 3, complexity: 'medium' },
        '5-6': { duration: 5, complexity: 'complex' }
      }
    },
    instruction_following: {
      id: 'instruction_following',
      name: '指令跟随',
      type: 'auditory',
      description: '听指令完成任务',
      energyReward: { min: 3, max: 12 },
      ageGroups: {
        '3-4': { duration: 2, complexity: 'simple' },
        '4-5': { duration: 3, complexity: 'medium' },
        '5-6': { duration: 4, complexity: 'complex' }
      }
    },
    
    // 视觉训练游戏
    shape_matching: {
      id: 'shape_matching',
      name: '图形匹配',
      type: 'visual',
      description: '找到相同的图形',
      energyReward: { min: 4, max: 10 },
      ageGroups: {
        '3-4': { duration: 2, complexity: 'simple' },
        '4-5': { duration: 3, complexity: 'medium' },
        '5-6': { duration: 4, complexity: 'complex' }
      }
    },
    color_sorting: {
      id: 'color_sorting',
      name: '颜色分类',
      type: 'visual',
      description: '将物品按颜色分类',
      energyReward: { min: 3, max: 8 },
      ageGroups: {
        '3-4': { duration: 2, complexity: 'simple' },
        '4-5': { duration: 3, complexity: 'medium' },
        '5-6': { duration: 4, complexity: 'complex' }
      }
    },

    // 补充其他游戏配置
    music_rhythm: {
      id: 'music_rhythm',
      name: '音乐节拍',
      type: 'auditory',
      description: '跟随音乐节拍',
      energyReward: { min: 4, max: 12 },
      ageGroups: {
        '3-4': { duration: 2, complexity: 'simple' },
        '4-5': { duration: 3, complexity: 'medium' },
        '5-6': { duration: 4, complexity: 'complex' }
      }
    },
    sound_recognition: {
      id: 'sound_recognition',
      name: '声音识别',
      type: 'auditory',
      description: '识别不同的声音',
      energyReward: { min: 3, max: 10 },
      ageGroups: {
        '3-4': { duration: 2, complexity: 'simple' },
        '4-5': { duration: 3, complexity: 'medium' },
        '5-6': { duration: 4, complexity: 'complex' }
      }
    },
    spot_difference: {
      id: 'spot_difference',
      name: '找不同',
      type: 'visual',
      description: '发现图片中的不同',
      energyReward: { min: 5, max: 12 },
      ageGroups: {
        '3-4': { duration: 3, complexity: 'simple' },
        '4-5': { duration: 4, complexity: 'medium' },
        '5-6': { duration: 5, complexity: 'complex' }
      }
    },
    sequence_memory: {
      id: 'sequence_memory',
      name: '序列记忆',
      type: 'visual',
      description: '记住图形序列',
      energyReward: { min: 6, max: 15 },
      ageGroups: {
        '3-4': { duration: 2, complexity: 'simple' },
        '4-5': { duration: 3, complexity: 'medium' },
        '5-6': { duration: 4, complexity: 'complex' }
      }
    },
    simple_reasoning: {
      id: 'simple_reasoning',
      name: '简单推理',
      type: 'logic',
      description: '基础逻辑推理',
      energyReward: { min: 7, max: 18 },
      ageGroups: {
        '3-4': { duration: 3, complexity: 'simple' },
        '4-5': { duration: 4, complexity: 'medium' },
        '5-6': { duration: 6, complexity: 'complex' }
      }
    },
    classification: {
      id: 'classification',
      name: '分类整理',
      type: 'logic',
      description: '物品分类和整理',
      energyReward: { min: 5, max: 15 },
      ageGroups: {
        '3-4': { duration: 3, complexity: 'simple' },
        '4-5': { duration: 4, complexity: 'medium' },
        '5-6': { duration: 5, complexity: 'complex' }
      }
    }
  },

  // 年龄组配置
  ageGroups: {
    '3-4': {
      name: '启蒙探索者',
      description: '刚开始探索宇宙的小朋友',
      maxGameDuration: 3, // 分钟
      recommendedGames: ['shape_matching', 'color_sorting', 'instruction_following'],
      energyMultiplier: 1.0
    },
    '4-5': {
      name: '初级宇航员', 
      description: '有一定探索经验的小朋友',
      maxGameDuration: 5,
      recommendedGames: ['story_comprehension', 'spot_difference', 'simple_reasoning'],
      energyMultiplier: 1.2
    },
    '5-6': {
      name: '高级探险家',
      description: '经验丰富的宇宙探险家',
      maxGameDuration: 8,
      recommendedGames: ['pattern_recognition', 'story_creation', 'cause_effect'],
      energyMultiplier: 1.5
    }
  },

  /**
   * 初始化探索系统
   */
  initialize: function() {
    try {
      // 获取或创建探索数据
      let explorationData = wx.getStorageSync('explorationData');
      if (!explorationData) {
        explorationData = {
          currentLevel: 1,
          unlockedPlanets: ['auditory', 'visual'], // 默认解锁前两个星球
          gameProgress: {},
          totalGamesPlayed: 0,
          totalWisdomEarned: 0,
          achievements: [],
          lastPlayDate: null,
          playerAge: '4-5' // 默认年龄组
        };
        wx.setStorageSync('explorationData', explorationData);
      }
      return explorationData;
    } catch (error) {
      console.error('初始化探索系统失败:', error);
      return null;
    }
  },

  /**
   * 获取探索数据
   */
  getExplorationData: function() {
    try {
      return wx.getStorageSync('explorationData') || this.initialize();
    } catch (error) {
      console.error('获取探索数据失败:', error);
      return this.initialize();
    }
  },

  /**
   * 保存探索数据
   */
  saveExplorationData: function(data) {
    try {
      wx.setStorageSync('explorationData', data);
      return true;
    } catch (error) {
      console.error('保存探索数据失败:', error);
      return false;
    }
  },

  /**
   * 获取可用星球列表
   */
  getAvailablePlanets: function() {
    const explorationData = this.getExplorationData();
    const availablePlanets = [];
    
    Object.values(this.planets).forEach(planet => {
      const isUnlocked = explorationData.unlockedPlanets.includes(planet.id);
      const canUnlock = explorationData.currentLevel >= planet.unlockLevel;
      
      availablePlanets.push({
        ...planet,
        isUnlocked: isUnlocked,
        canUnlock: canUnlock,
        status: isUnlocked ? 'unlocked' : (canUnlock ? 'available' : 'locked')
      });
    });
    
    return availablePlanets;
  },

  /**
   * 获取推荐游戏
   */
  getRecommendedGame: function() {
    const explorationData = this.getExplorationData();
    const ageGroup = explorationData.playerAge || '4-5';
    const recommendedGames = this.ageGroups[ageGroup].recommendedGames;
    
    // 随机选择一个推荐游戏
    const randomIndex = Math.floor(Math.random() * recommendedGames.length);
    const gameId = recommendedGames[randomIndex];
    
    return this.games[gameId];
  },

  /**
   * 开始游戏
   */
  startGame: function(gameId) {
    try {
      const game = this.games[gameId];
      if (!game) {
        return { success: false, error: '游戏不存在' };
      }
      
      const explorationData = this.getExplorationData();
      const ageGroup = explorationData.playerAge || '4-5';
      const gameConfig = game.ageGroups[ageGroup];
      
      // 记录游戏开始
      const gameSession = {
        gameId: gameId,
        startTime: Date.now(),
        ageGroup: ageGroup,
        config: gameConfig
      };
      
      wx.setStorageSync('currentGameSession', gameSession);
      
      return {
        success: true,
        game: game,
        config: gameConfig,
        session: gameSession
      };
    } catch (error) {
      console.error('开始游戏失败:', error);
      return { success: false, error: '开始游戏失败' };
    }
  },

  /**
   * 完成游戏
   */
  completeGame: function(gameId, score, performance) {
    try {
      const gameSession = wx.getStorageSync('currentGameSession');
      if (!gameSession || gameSession.gameId !== gameId) {
        return { success: false, error: '游戏会话无效' };
      }
      
      const game = this.games[gameId];
      const explorationData = this.getExplorationData();
      
      // 计算奖励
      const energyReward = this.calculateEnergyReward(game, score, performance);
      
      // 更新游戏进度
      if (!explorationData.gameProgress[gameId]) {
        explorationData.gameProgress[gameId] = {
          timesPlayed: 0,
          bestScore: 0,
          totalEnergy: 0
        };
      }
      
      const gameProgress = explorationData.gameProgress[gameId];
      gameProgress.timesPlayed += 1;
      gameProgress.bestScore = Math.max(gameProgress.bestScore, score);
      gameProgress.totalEnergy += energyReward;
      
      // 更新总体数据
      explorationData.totalGamesPlayed += 1;
      explorationData.totalWisdomEarned += energyReward;
      explorationData.lastPlayDate = new Date().toISOString();
      
      // 检查解锁新星球
      this.checkPlanetUnlocks(explorationData);
      
      // 保存数据
      this.saveExplorationData(explorationData);
      
      // 给予智慧能量奖励
      this.grantWisdomEnergy(energyReward);
      
      // 清除游戏会话
      wx.removeStorageSync('currentGameSession');
      
      return {
        success: true,
        energyReward: energyReward,
        gameProgress: gameProgress,
        newUnlocks: this.checkAchievements(explorationData, gameId, score)
      };
    } catch (error) {
      console.error('完成游戏失败:', error);
      return { success: false, error: '完成游戏失败' };
    }
  },

  /**
   * 计算能量奖励
   */
  calculateEnergyReward: function(game, score, performance) {
    const baseReward = game.energyReward.min + 
      (game.energyReward.max - game.energyReward.min) * (score / 100);
    
    // 根据表现调整奖励
    let multiplier = 1.0;
    if (performance === 'excellent') multiplier = 1.5;
    else if (performance === 'good') multiplier = 1.2;
    else if (performance === 'fair') multiplier = 1.0;
    else multiplier = 0.8;
    
    return Math.floor(baseReward * multiplier);
  },

  /**
   * 给予智慧能量
   */
  grantWisdomEnergy: function(amount) {
    try {
      // 更新全局数据
      const app = getApp();
      app.globalData.wisdomEnergy = (app.globalData.wisdomEnergy || 0) + amount;
      
      // 保存到本地存储
      wx.setStorageSync('wisdomEnergy', app.globalData.wisdomEnergy);
      
      // 更新能量数据
      const energyData = wx.getStorageSync('energyData') || { wisdom: 0, love: 0 };
      energyData.wisdom += amount;
      wx.setStorageSync('energyData', energyData);
      
      return true;
    } catch (error) {
      console.error('给予智慧能量失败:', error);
      return false;
    }
  },

  /**
   * 检查星球解锁
   */
  checkPlanetUnlocks: function(explorationData) {
    const totalGames = explorationData.totalGamesPlayed;
    
    // 解锁逻辑星球 (需要完成10个游戏)
    if (totalGames >= 10 && !explorationData.unlockedPlanets.includes('logic')) {
      explorationData.unlockedPlanets.push('logic');
      explorationData.currentLevel = Math.max(explorationData.currentLevel, 2);
    }
    
    // 解锁语言星球 (需要完成15个游戏)
    if (totalGames >= 15 && !explorationData.unlockedPlanets.includes('language')) {
      explorationData.unlockedPlanets.push('language');
      explorationData.currentLevel = Math.max(explorationData.currentLevel, 2);
    }
    
    // 解锁创造星球 (需要完成25个游戏)
    if (totalGames >= 25 && !explorationData.unlockedPlanets.includes('creative')) {
      explorationData.unlockedPlanets.push('creative');
      explorationData.currentLevel = Math.max(explorationData.currentLevel, 3);
    }
  },

  /**
   * 检查成就
   */
  checkAchievements: function(explorationData, gameId, score) {
    const achievements = [];
    
    // 首次游戏成就
    if (explorationData.totalGamesPlayed === 1) {
      achievements.push({
        id: 'first_exploration',
        name: '初次探索',
        description: '完成第一个探索游戏',
        icon: '🚀',
        type: 'exploration'
      });
    }
    
    // 完美分数成就
    if (score === 100) {
      achievements.push({
        id: 'perfect_score',
        name: '完美探索者',
        description: '获得满分成绩',
        icon: '⭐',
        type: 'exploration'
      });
    }
    
    // 保存成就
    achievements.forEach(achievement => {
      const personalSystem = require('./personalSystem.js');
      personalSystem.addAchievement(achievement);
    });
    
    return achievements;
  }
};

module.exports = explorationSystem;
