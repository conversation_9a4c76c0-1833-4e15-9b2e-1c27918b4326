/**
 * 《能量星球》好友系统管理工具类
 * 负责好友管理、社交互动、安全监管等功能
 */

const friendSystem = {
  // 好友状态
  friendStatus: {
    pending: '待确认',
    accepted: '已接受',
    blocked: '已屏蔽'
  },

  // 互动类型
  interactionTypes: {
    like: '点赞',
    visit: '访问',
    gift: '礼物',
    message: '消息'
  },

  /**
   * 获取好友汇总信息
   */
  getFriendSummary: function() {
    try {
      const friends = this.getAllFriends();
      const onlineFriends = this.getOnlineFriends();
      
      return {
        totalCount: friends.length,
        onlineCount: onlineFriends.length,
        onlineList: onlineFriends.slice(0, 3), // 只显示前3个在线好友
        recentInteractions: this.getRecentInteractions(5)
      };
    } catch (error) {
      console.error('获取好友汇总失败:', error);
      return {
        totalCount: 0,
        onlineCount: 0,
        onlineList: [],
        recentInteractions: []
      };
    }
  },

  /**
   * 获取所有好友
   */
  getAllFriends: function() {
    try {
      return wx.getStorageSync('friends') || [];
    } catch (error) {
      console.error('获取好友列表失败:', error);
      return [];
    }
  },

  /**
   * 获取在线好友（模拟数据）
   */
  getOnlineFriends: function() {
    try {
      const friends = this.getAllFriends();
      // 模拟在线状态（实际应用中需要服务器支持）
      return friends.filter(friend => {
        // 简单的模拟逻辑：随机50%的好友在线
        return Math.random() > 0.5;
      }).map(friend => ({
        ...friend,
        lastSeen: new Date().toISOString()
      }));
    } catch (error) {
      console.error('获取在线好友失败:', error);
      return [];
    }
  },

  /**
   * 添加好友
   */
  addFriend: function(friendData) {
    try {
      // 检查是否需要家长审核
      if (!this.checkParentApproval()) {
        return {
          success: false,
          error: '需要家长审核才能添加好友'
        };
      }

      // 检查是否已经是好友
      const friends = this.getAllFriends();
      const exists = friends.some(f => f.id === friendData.id);
      if (exists) {
        return {
          success: false,
          error: '该用户已经是您的好友'
        };
      }

      // 创建好友记录
      const friend = {
        id: friendData.id,
        name: friendData.name,
        avatar: friendData.avatar || '🧒',
        addDate: new Date().toISOString(),
        status: 'accepted',
        relationship: friendData.relationship || 'classmate', // 同学、邻居、亲戚等
        parentApproved: true,
        interactionCount: 0,
        lastInteraction: null
      };

      friends.push(friend);
      wx.setStorageSync('friends', friends);

      // 记录添加好友的互动
      this.recordInteraction(friend.id, 'friend_added', '成为好友');

      return {
        success: true,
        friend: friend
      };

    } catch (error) {
      console.error('添加好友失败:', error);
      return {
        success: false,
        error: '添加好友失败，请重试'
      };
    }
  },

  /**
   * 检查家长审核
   */
  checkParentApproval: function() {
    try {
      // 检查家长设置
      const parentSettings = wx.getStorageSync('parentSettings') || {};
      return parentSettings.allowAddFriends !== false; // 默认允许
    } catch (error) {
      console.error('检查家长审核失败:', error);
      return false;
    }
  },

  /**
   * 访问好友舱室
   */
  visitFriend: function(friendId) {
    try {
      const friend = this.getFriendById(friendId);
      if (!friend) {
        return {
          success: false,
          error: '好友不存在'
        };
      }

      // 记录访问互动
      this.recordInteraction(friendId, 'visit', '访问舱室');

      // 模拟获取好友舱室数据
      const friendQuarters = this.getFriendQuarters(friendId);

      return {
        success: true,
        friend: friend,
        quarters: friendQuarters
      };

    } catch (error) {
      console.error('访问好友舱室失败:', error);
      return {
        success: false,
        error: '访问失败，请重试'
      };
    }
  },

  /**
   * 获取好友舱室数据（模拟）
   */
  getFriendQuarters: function(friendId) {
    // 模拟好友舱室数据
    return {
      decorations: ['星空壁纸', '水晶灯', '宇宙地毯'],
      achievements: [
        { name: '学习达人', icon: '📚' },
        { name: '善意天使', icon: '😇' },
        { name: '友谊使者', icon: '🤝' }
      ],
      level: Math.floor(Math.random() * 10) + 1,
      motto: '探索宇宙，传播善意！'
    };
  },

  /**
   * 给好友点赞
   */
  likeFriend: function(friendId, target = 'quarters') {
    try {
      const friend = this.getFriendById(friendId);
      if (!friend) {
        return {
          success: false,
          error: '好友不存在'
        };
      }

      // 检查今日是否已点赞
      if (this.hasLikedToday(friendId, target)) {
        return {
          success: false,
          error: '今日已点赞过了'
        };
      }

      // 记录点赞互动
      this.recordInteraction(friendId, 'like', `点赞${target}`);

      // 增加好友的点赞数（模拟）
      this.increaseFriendLikes(friendId, target);

      return {
        success: true,
        message: '点赞成功！'
      };

    } catch (error) {
      console.error('点赞失败:', error);
      return {
        success: false,
        error: '点赞失败，请重试'
      };
    }
  },

  /**
   * 检查今日是否已点赞
   */
  hasLikedToday: function(friendId, target) {
    try {
      const interactions = this.getInteractions();
      const today = new Date().toDateString();
      
      return interactions.some(interaction => 
        interaction.friendId === friendId &&
        interaction.type === 'like' &&
        interaction.details.includes(target) &&
        new Date(interaction.date).toDateString() === today
      );
    } catch (error) {
      console.error('检查点赞状态失败:', error);
      return false;
    }
  },

  /**
   * 赠送礼物
   */
  sendGift: function(friendId, giftData) {
    try {
      const friend = this.getFriendById(friendId);
      if (!friend) {
        return {
          success: false,
          error: '好友不存在'
        };
      }

      // 检查礼物成本
      const cost = giftData.cost || 10;
      if (!this.checkEnergyAvailable(cost)) {
        return {
          success: false,
          error: '智慧能量不足'
        };
      }

      // 消耗能量
      this.consumeWisdomEnergy(cost);

      // 记录礼物互动
      this.recordInteraction(friendId, 'gift', `赠送${giftData.name}`);

      // 创建礼物记录
      const gift = {
        id: 'gift_' + Date.now(),
        fromId: 'self',
        toId: friendId,
        name: giftData.name,
        icon: giftData.icon,
        message: giftData.message || '',
        date: new Date().toISOString(),
        cost: cost
      };

      // 保存礼物记录
      const gifts = wx.getStorageSync('gifts') || [];
      gifts.push(gift);
      wx.setStorageSync('gifts', gifts);

      return {
        success: true,
        gift: gift
      };

    } catch (error) {
      console.error('赠送礼物失败:', error);
      return {
        success: false,
        error: '赠送失败，请重试'
      };
    }
  },

  /**
   * 记录互动
   */
  recordInteraction: function(friendId, type, details) {
    try {
      const interactions = this.getInteractions();
      
      const interaction = {
        id: 'interaction_' + Date.now(),
        friendId: friendId,
        type: type,
        details: details,
        date: new Date().toISOString()
      };

      interactions.push(interaction);
      
      // 只保留最近100条互动记录
      if (interactions.length > 100) {
        interactions.splice(0, interactions.length - 100);
      }

      wx.setStorageSync('friendInteractions', interactions);

      // 更新好友的互动计数
      this.updateFriendInteractionCount(friendId);

      return true;
    } catch (error) {
      console.error('记录互动失败:', error);
      return false;
    }
  },

  /**
   * 获取互动记录
   */
  getInteractions: function() {
    try {
      return wx.getStorageSync('friendInteractions') || [];
    } catch (error) {
      console.error('获取互动记录失败:', error);
      return [];
    }
  },

  /**
   * 获取最近互动
   */
  getRecentInteractions: function(limit = 5) {
    try {
      const interactions = this.getInteractions();
      return interactions
        .sort((a, b) => new Date(b.date) - new Date(a.date))
        .slice(0, limit)
        .map(interaction => {
          const friend = this.getFriendById(interaction.friendId);
          return {
            ...interaction,
            friendName: friend?.name || '未知好友'
          };
        });
    } catch (error) {
      console.error('获取最近互动失败:', error);
      return [];
    }
  },

  /**
   * 获取好友详情
   */
  getFriendById: function(friendId) {
    try {
      const friends = this.getAllFriends();
      return friends.find(f => f.id === friendId) || null;
    } catch (error) {
      console.error('获取好友详情失败:', error);
      return null;
    }
  },

  /**
   * 更新好友互动计数
   */
  updateFriendInteractionCount: function(friendId) {
    try {
      const friends = this.getAllFriends();
      const friendIndex = friends.findIndex(f => f.id === friendId);
      
      if (friendIndex !== -1) {
        friends[friendIndex].interactionCount = (friends[friendIndex].interactionCount || 0) + 1;
        friends[friendIndex].lastInteraction = new Date().toISOString();
        wx.setStorageSync('friends', friends);
      }
    } catch (error) {
      console.error('更新好友互动计数失败:', error);
    }
  },

  /**
   * 检查智慧能量是否足够
   */
  checkEnergyAvailable: function(required) {
    try {
      const app = getApp();
      const currentEnergy = app.globalData.wisdomEnergy || 0;
      return currentEnergy >= required;
    } catch (error) {
      console.error('检查能量失败:', error);
      return false;
    }
  },

  /**
   * 消耗智慧能量
   */
  consumeWisdomEnergy: function(amount) {
    try {
      const app = getApp();
      app.globalData.wisdomEnergy = Math.max(0, 
        (app.globalData.wisdomEnergy || 0) - amount
      );
      wx.setStorageSync('wisdomEnergy', app.globalData.wisdomEnergy);
      return true;
    } catch (error) {
      console.error('消耗智慧能量失败:', error);
      return false;
    }
  }
};

module.exports = friendSystem;
