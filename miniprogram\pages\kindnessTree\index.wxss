/* 善意成长树样式 */

.growth-tree-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 50%, #4CAF50 100%);
  position: relative;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 背景装饰 */
.tree-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.floating-leaf {
  position: absolute;
  font-size: 30rpx;
  opacity: 0.6;
  animation: leafFloat 10s infinite ease-in-out;
}

.leaf-1 { top: 15%; left: 10%; animation-delay: 0s; }
.leaf-2 { top: 25%; right: 15%; animation-delay: 2.5s; }
.leaf-3 { top: 65%; left: 8%; animation-delay: 5s; }
.leaf-4 { top: 80%; right: 12%; animation-delay: 7.5s; }

@keyframes leafFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-20rpx) rotate(90deg);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-30rpx) rotate(180deg);
    opacity: 1;
  }
  75% {
    transform: translateY(-20rpx) rotate(270deg);
    opacity: 0.8;
  }
}

.sunlight-ray {
  position: absolute;
  width: 4rpx;
  height: 200rpx;
  background: linear-gradient(to bottom, rgba(255, 235, 59, 0.6), transparent);
  transform-origin: top center;
  animation: sunlightShine 8s infinite ease-in-out;
}

.ray-1 {
  top: 5%;
  left: 20%;
  transform: rotate(15deg);
  animation-delay: 0s;
}

.ray-2 {
  top: 8%;
  left: 50%;
  transform: rotate(-10deg);
  animation-delay: 2s;
}

.ray-3 {
  top: 12%;
  right: 25%;
  transform: rotate(20deg);
  animation-delay: 4s;
}

@keyframes sunlightShine {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1) rotate(var(--rotation, 0deg));
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1) rotate(var(--rotation, 0deg));
  }
}

/* 页面标题 */
.tree-header {
  position: relative;
  z-index: 10;
  text-align: center;
  margin-bottom: 30rpx;
  padding: 25rpx 20rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 25rpx;
  backdrop-filter: blur(15rpx);
  border: 2rpx solid rgba(76, 175, 80, 0.4);
}

.header-icon {
  font-size: 60rpx;
  margin-bottom: 15rpx;
  animation: headerGrow 4s infinite ease-in-out;
}

@keyframes headerGrow {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 15rpx rgba(76, 175, 80, 0.6));
  }
  50% {
    transform: scale(1.1);
    filter: drop-shadow(0 0 25rpx rgba(76, 175, 80, 0.8));
  }
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2E7D32;
  margin-bottom: 8rpx;
  text-shadow: 1rpx 1rpx 3rpx rgba(46, 125, 50, 0.3);
}

.header-subtitle {
  font-size: 24rpx;
  color: #4CAF50;
  opacity: 0.8;
}

/* 成长树主体 */
.tree-main-section {
  position: relative;
  z-index: 10;
  margin-bottom: 40rpx;
}

/* 成长树可视化 */
.tree-visualization {
  position: relative;
  height: 600rpx;
  margin-bottom: 30rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 25rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(76, 175, 80, 0.3);
  overflow: hidden;
}

/* 树冠 */
.tree-crown {
  position: absolute;
  top: 50rpx;
  left: 50%;
  transform: translateX(-50%);
  width: calc(200rpx * var(--tree-size, 1));
  height: calc(200rpx * var(--tree-size, 1));
  transition: all 0.5s ease;
}

.crown-base {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: crownSway 6s infinite ease-in-out;
}

.crown-sprout {
  background: radial-gradient(circle, #81C784 0%, #66BB6A 50%, #4CAF50 100%);
  box-shadow: 0 0 30rpx rgba(76, 175, 80, 0.6);
}

.crown-flower {
  background: radial-gradient(circle, #F8BBD9 0%, #F48FB1 50%, #E91E63 100%);
  box-shadow: 0 0 30rpx rgba(233, 30, 99, 0.6);
}

.crown-tree {
  background: radial-gradient(circle, #A5D6A7 0%, #81C784 50%, #66BB6A 100%);
  box-shadow: 0 0 40rpx rgba(102, 187, 106, 0.8);
}

.crown-star {
  background: radial-gradient(circle, #FFF59D 0%, #FFEB3B 50%, #FFC107 100%);
  box-shadow: 0 0 50rpx rgba(255, 193, 7, 1);
}

@keyframes crownSway {
  0%, 100% {
    transform: rotate(-2deg);
  }
  50% {
    transform: rotate(2deg);
  }
}

.crown-icon {
  font-size: calc(80rpx * var(--tree-size, 1));
  animation: crownIconFloat 4s infinite ease-in-out;
}

@keyframes crownIconFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

/* 装饰层 */
.decorations-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.decoration {
  position: absolute;
  font-size: 25rpx;
  animation: decorationTwinkle 3s infinite ease-in-out;
  cursor: pointer;
  transition: all 0.3s ease;
}

.decoration:active {
  transform: scale(1.3);
}

.decoration-flower {
  animation-delay: 0s;
}

.decoration-butterfly {
  animation-delay: 1s;
}

.decoration-rainbow {
  animation-delay: 2s;
}

.decoration-star {
  animation-delay: 0.5s;
}

@keyframes decorationTwinkle {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

/* 成就徽章层 */
.badges-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.achievement-badge {
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 193, 7, 0.9);
  border: 2rpx solid #FFC107;
  animation: badgeGlow 4s infinite ease-in-out;
}

.badge-icon {
  font-size: 20rpx;
}

.badge-glow {
  position: absolute;
  top: -5rpx;
  left: -5rpx;
  right: -5rpx;
  bottom: -5rpx;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 193, 7, 0.4) 0%, transparent 70%);
  animation: badgeGlowPulse 2s infinite ease-in-out;
}

@keyframes badgeGlow {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes badgeGlowPulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 树干 */
.tree-trunk {
  position: absolute;
  bottom: 150rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 200rpx;
}

.trunk-base {
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, #8D6E63 0%, #6D4C41 50%, #5D4037 100%);
  border-radius: 30rpx 30rpx 15rpx 15rpx;
  box-shadow: inset -10rpx 0 20rpx rgba(0, 0, 0, 0.3);
}

.trunk-rings {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.growth-ring {
  position: absolute;
  left: -20rpx;
  width: 100rpx;
  height: 4rpx;
  background: rgba(255, 193, 7, 0.8);
  border-radius: 2rpx;
  animation: ringGlow 3s infinite ease-in-out;
}

.ring-marker {
  position: absolute;
  left: -5rpx;
  top: -3rpx;
  width: 10rpx;
  height: 10rpx;
  background: #FFC107;
  border-radius: 50%;
}

.ring-label {
  position: absolute;
  left: 110rpx;
  top: -10rpx;
  font-size: 18rpx;
  color: #2E7D32;
  white-space: nowrap;
}

@keyframes ringGlow {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* 树根 */
.tree-roots {
  position: absolute;
  bottom: 120rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 200rpx;
  height: 50rpx;
}

.root {
  position: absolute;
  height: 8rpx;
  background: linear-gradient(to right, #8D6E63, #6D4C41);
  border-radius: 4rpx;
  animation: rootGrow 8s infinite ease-in-out;
}

.root-left {
  bottom: 0;
  left: 0;
  width: 80rpx;
  transform: rotate(-15deg);
  animation-delay: 0s;
}

.root-right {
  bottom: 0;
  right: 0;
  width: 80rpx;
  transform: rotate(15deg);
  animation-delay: 2s;
}

.root-center {
  bottom: 10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  animation-delay: 4s;
}

@keyframes rootGrow {
  0%, 100% {
    transform: scaleX(1) rotate(var(--rotation, 0deg));
  }
  50% {
    transform: scaleX(1.1) rotate(var(--rotation, 0deg));
  }
}

/* 土壤 */
.tree-soil {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 120rpx;
  border-radius: 0 0 25rpx 25rpx;
  overflow: hidden;
}

.soil-layer {
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, #8D6E63 0%, #6D4C41 50%, #5D4037 100%);
}

.soil-nutrients {
  position: absolute;
  top: 20rpx;
  left: 0;
  width: 100%;
  height: 40rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.nutrient {
  font-size: 20rpx;
  opacity: 0.7;
  animation: nutrientFloat 4s infinite ease-in-out;
}

.nutrient:nth-child(1) { animation-delay: 0s; }
.nutrient:nth-child(2) { animation-delay: 1s; }
.nutrient:nth-child(3) { animation-delay: 2s; }
.nutrient:nth-child(4) { animation-delay: 3s; }

@keyframes nutrientFloat {
  0%, 100% {
    transform: translateY(0);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-5rpx);
    opacity: 1;
  }
}

/* 成长信息面板 */
.growth-info-panel {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  backdrop-filter: blur(15rpx);
  border: 2rpx solid rgba(76, 175, 80, 0.3);
  overflow: hidden;
}

.info-tabs {
  display: flex;
  background: rgba(76, 175, 80, 0.2);
}

.info-tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 10rpx;
  transition: all 0.3s ease;
}

.info-tab.active {
  background: rgba(76, 175, 80, 0.4);
  transform: scale(1.05);
}

.tab-icon {
  font-size: 28rpx;
  margin-bottom: 5rpx;
}

.tab-text {
  font-size: 20rpx;
  color: #2E7D32;
  font-weight: bold;
}

.tab-content {
  padding: 30rpx 25rpx;
}

/* 统计内容 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stat-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 15rpx;
  padding: 20rpx;
  border: 1rpx solid rgba(76, 175, 80, 0.3);
}

.stat-icon {
  font-size: 35rpx;
  margin-bottom: 10rpx;
}

.stat-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #2E7D32;
  margin-bottom: 5rpx;
}

.stat-label {
  font-size: 20rpx;
  color: #4CAF50;
  opacity: 0.8;
}

.level-progress {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 15rpx;
  padding: 25rpx;
  border: 1rpx solid rgba(76, 175, 80, 0.3);
}

.progress-title {
  font-size: 24rpx;
  color: #2E7D32;
  font-weight: bold;
  margin-bottom: 15rpx;
  text-align: center;
}

.progress-bar {
  height: 12rpx;
  background: rgba(76, 175, 80, 0.3);
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  border-radius: 6rpx;
  transition: width 0.5s ease;
  box-shadow: 0 0 10rpx rgba(76, 175, 80, 0.6);
}

.progress-text {
  font-size: 20rpx;
  color: #2E7D32;
  text-align: center;
  opacity: 0.8;
}

/* 装饰内容 */
.decoration-categories {
  display: flex;
  gap: 15rpx;
  margin-bottom: 25rpx;
  overflow-x: auto;
  padding-bottom: 10rpx;
}

.decoration-category {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80rpx;
  padding: 15rpx 10rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 15rpx;
  border: 2rpx solid rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
}

.decoration-category.active {
  background: rgba(76, 175, 80, 0.4);
  border-color: #4CAF50;
  transform: scale(1.05);
}

.category-icon {
  font-size: 30rpx;
  margin-bottom: 5rpx;
}

.category-name {
  font-size: 18rpx;
  color: #2E7D32;
  text-align: center;
}

.decoration-items {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.decoration-item {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 15rpx;
  padding: 20rpx;
  border: 1rpx solid rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
}

.decoration-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.4);
}

.item-icon {
  font-size: 35rpx;
  margin-right: 20rpx;
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #2E7D32;
  margin-bottom: 5rpx;
}

.item-description {
  font-size: 20rpx;
  color: #4CAF50;
  opacity: 0.8;
  margin-bottom: 8rpx;
}

.item-cost {
  font-size: 20rpx;
  color: #FF9800;
  font-weight: bold;
}

.item-status {
  display: flex;
  align-items: center;
}

.add-btn {
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
  color: white;
  font-size: 20rpx;
  padding: 8rpx 20rpx;
  border-radius: 15rpx;
  font-weight: bold;
}

.owned-badge {
  background: rgba(76, 175, 80, 0.8);
  color: white;
  font-size: 18rpx;
  padding: 6rpx 15rpx;
  border-radius: 12rpx;
}

.insufficient-badge {
  background: rgba(158, 158, 158, 0.6);
  color: white;
  font-size: 18rpx;
  padding: 6rpx 15rpx;
  border-radius: 12rpx;
}

/* 成就内容 */
.achievement-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.achievement-item {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 15rpx;
  padding: 20rpx;
  border: 1rpx solid rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
}

.achievement-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.4);
}

.achievement-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.achievement-icon.locked {
  opacity: 0.4;
  filter: grayscale(100%);
}

.achievement-icon.unlocked {
  animation: achievementGlow 3s infinite ease-in-out;
}

@keyframes achievementGlow {
  0%, 100% {
    filter: drop-shadow(0 0 5rpx rgba(255, 193, 7, 0.6));
  }
  50% {
    filter: drop-shadow(0 0 15rpx rgba(255, 193, 7, 1));
  }
}

.achievement-info {
  flex: 1;
}

.achievement-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #2E7D32;
  margin-bottom: 5rpx;
}

.achievement-description {
  font-size: 20rpx;
  color: #4CAF50;
  opacity: 0.8;
  margin-bottom: 8rpx;
}

.achievement-progress {
  margin-top: 10rpx;
}

.achievement-progress .progress-bar {
  height: 8rpx;
  margin-bottom: 5rpx;
}

.achievement-progress .progress-text {
  font-size: 16rpx;
  text-align: left;
}

.achievement-date {
  font-size: 18rpx;
  color: #FF9800;
  font-weight: bold;
}

.achievement-reward {
  display: flex;
  align-items: center;
}

.reward-icon {
  font-size: 30rpx;
  animation: rewardBounce 2s infinite ease-in-out;
}

@keyframes rewardBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5rpx);
  }
}

/* 快速操作 */
.quick-actions {
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(76, 175, 80, 0.3);
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx;
  border-radius: 15rpx;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.9);
  background: rgba(76, 175, 80, 0.3);
}

.btn-icon {
  font-size: 35rpx;
  margin-bottom: 8rpx;
  animation: btnIconFloat 4s infinite ease-in-out;
}

.btn-icon:nth-child(1) { animation-delay: 0s; }
.btn-icon:nth-child(2) { animation-delay: 1s; }
.btn-icon:nth-child(3) { animation-delay: 2s; }
.btn-icon:nth-child(4) { animation-delay: 3s; }

@keyframes btnIconFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5rpx);
  }
}

.btn-text {
  font-size: 20rpx;
  color: #2E7D32;
  font-weight: bold;
}

/* 成长里程碑 */
.milestones-section {
  position: relative;
  z-index: 10;
  margin-bottom: 40rpx;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #2E7D32;
}

.milestones-timeline {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  padding: 30rpx 25rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(76, 175, 80, 0.3);
}

.milestone-item {
  position: relative;
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.milestone-item:last-child {
  margin-bottom: 0;
}

.milestone-marker {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  border: 3rpx solid #E0E0E0;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.milestone-item.achieved .milestone-marker {
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
  border-color: #4CAF50;
  animation: milestoneAchieved 2s infinite ease-in-out;
}

@keyframes milestoneAchieved {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 10rpx rgba(76, 175, 80, 0.6);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 20rpx rgba(76, 175, 80, 0.8);
  }
}

.marker-icon {
  font-size: 24rpx;
  color: #2E7D32;
}

.milestone-item.achieved .marker-icon {
  color: white;
}

.milestone-content {
  flex: 1;
}

.milestone-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #2E7D32;
  margin-bottom: 5rpx;
}

.milestone-description {
  font-size: 20rpx;
  color: #4CAF50;
  opacity: 0.8;
  margin-bottom: 8rpx;
}

.milestone-reward {
  font-size: 18rpx;
  color: #FF9800;
  font-weight: bold;
}

.milestone-line {
  position: absolute;
  left: 24rpx;
  top: 50rpx;
  width: 2rpx;
  height: 30rpx;
  background: #E0E0E0;
}

.milestone-item.achieved .milestone-line {
  background: #4CAF50;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(232, 245, 232, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5rpx);
}

.loading-spinner {
  text-align: center;
}

.spinner-icon {
  font-size: 80rpx;
  animation: spinnerGrow 2s infinite ease-in-out;
}

@keyframes spinnerGrow {
  0% {
    transform: scale(1) rotate(0deg);
    filter: drop-shadow(0 0 10rpx rgba(76, 175, 80, 0.6));
  }
  50% {
    transform: scale(1.3) rotate(180deg);
    filter: drop-shadow(0 0 30rpx rgba(76, 175, 80, 1));
  }
  100% {
    transform: scale(1) rotate(360deg);
    filter: drop-shadow(0 0 10rpx rgba(76, 175, 80, 0.6));
  }
}

.loading-text {
  font-size: 28rpx;
  color: #2E7D32;
  margin-top: 20rpx;
  font-weight: bold;
}

/* 装饰添加成功动画 */
.decoration-success-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(232, 245, 232, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  backdrop-filter: blur(10rpx);
}

.success-content {
  text-align: center;
  position: relative;
}

.success-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  animation: successTreeGrow 1s ease-out;
}

@keyframes successTreeGrow {
  0% {
    transform: scale(0) rotate(0deg);
    filter: drop-shadow(0 0 0 rgba(76, 175, 80, 0));
  }
  30% {
    transform: scale(1.5) rotate(120deg);
    filter: drop-shadow(0 0 40rpx rgba(76, 175, 80, 1));
  }
  60% {
    transform: scale(0.8) rotate(240deg);
    filter: drop-shadow(0 0 60rpx rgba(255, 193, 7, 1));
  }
  100% {
    transform: scale(1) rotate(360deg);
    filter: drop-shadow(0 0 30rpx rgba(76, 175, 80, 0.8));
  }
}

.success-message {
  font-size: 36rpx;
  font-weight: bold;
  color: #2E7D32;
  margin-bottom: 40rpx;
  text-shadow: 1rpx 1rpx 3rpx rgba(46, 125, 50, 0.3);
  animation: messageGlow 2s infinite ease-in-out;
}

@keyframes messageGlow {
  0%, 100% {
    text-shadow: 1rpx 1rpx 3rpx rgba(46, 125, 50, 0.3);
  }
  50% {
    text-shadow: 2rpx 2rpx 8rpx rgba(46, 125, 50, 0.6), 0 0 20rpx rgba(76, 175, 80, 0.4);
  }
}

.success-sparkles {
  position: absolute;
  top: -80rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 400rpx;
  height: 400rpx;
}

.sparkle {
  position: absolute;
  font-size: 40rpx;
  animation: sparkleTreeBurst 3s ease-out forwards;
}

.sparkle.sparkle-1 {
  top: 50%;
  left: 50%;
  animation-delay: 0.2s;
}

.sparkle.sparkle-2 {
  top: 25%;
  left: 25%;
  animation-delay: 0.4s;
}

.sparkle.sparkle-3 {
  top: 25%;
  right: 25%;
  animation-delay: 0.6s;
}

.sparkle.sparkle-4 {
  bottom: 25%;
  left: 25%;
  animation-delay: 0.8s;
}

.sparkle.sparkle-5 {
  bottom: 25%;
  right: 25%;
  animation-delay: 1s;
}

@keyframes sparkleTreeBurst {
  0% {
    transform: translate(-50%, -50%) scale(0) rotate(0deg);
    opacity: 1;
    filter: drop-shadow(0 0 0 rgba(76, 175, 80, 0));
  }
  20% {
    transform: translate(-50%, -50%) scale(1.5) rotate(90deg);
    opacity: 1;
    filter: drop-shadow(0 0 30rpx rgba(76, 175, 80, 1));
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
    opacity: 0.8;
    filter: drop-shadow(0 0 40rpx rgba(255, 193, 7, 1));
  }
  80% {
    transform: translate(-50%, -250rpx) scale(0.8) rotate(270deg);
    opacity: 0.4;
    filter: drop-shadow(0 0 20rpx rgba(76, 175, 80, 0.8));
  }
  100% {
    transform: translate(-50%, -350rpx) scale(0.3) rotate(360deg);
    opacity: 0;
    filter: drop-shadow(0 0 0 rgba(76, 175, 80, 0));
  }
}
