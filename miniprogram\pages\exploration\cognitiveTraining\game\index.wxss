/* pages/exploration/cognitiveTraining/game/index.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  position: relative;
  overflow: hidden;
  padding: 0;
  margin: 0;
  width: 100%;
  box-sizing: border-box;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 30% 20%, rgba(255, 152, 0, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(33, 150, 243, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.hidden {
  display: none !important;
}



/* 游戏界面样式 */
.game-screen {
  min-height: 100vh;
  padding: 20rpx 30rpx;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
  margin: 0;
}

.game-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 24rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  margin-bottom: 30rpx;
  width: 100%;
  box-sizing: border-box;
}

.progress-info {
  flex: 1;
  margin-right: 30rpx;
}

.question-number {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 10rpx;
  display: block;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.timer-section {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-right: 30rpx;
}

.timer {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 3rpx solid;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.timer.warning {
  animation: pulse 1s infinite;
  border-color: #f44336 !important;
  color: #f44336;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.timer-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
}

.score-section {
  text-align: center;
}

.score-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
  display: block;
  margin-bottom: 5rpx;
}

.score-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
}

/* 题目内容样式 */
.question-content {
  flex: 1;
  background: linear-gradient(145deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.9));
  border-radius: 32rpx;
  padding: 40rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.15);
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
  margin: 0;
  position: relative;
  box-shadow:
    0 16rpx 48rpx rgba(0, 0, 0, 0.4),
    0 8rpx 16rpx rgba(0, 0, 0, 0.3),
    inset 0 3rpx 0 rgba(255, 255, 255, 0.15),
    inset 0 -3rpx 0 rgba(0, 0, 0, 0.3),
    inset -3rpx 0 0 rgba(0, 0, 0, 0.2),
    inset 3rpx 0 0 rgba(255, 255, 255, 0.08);
}

.question-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 32rpx;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 25%,
    transparent 75%,
    rgba(0, 0, 0, 0.1) 100%);
  pointer-events: none;
}

/* 题目卡片 - 简洁现代风格 */
.question-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.1),
    0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20rpx);
}

.question-number {
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 20rpx;
  font-weight: 500;
}

.question-text {
  font-size: 32rpx;
  color: #2c3e50;
  line-height: 1.6;
  font-weight: 500;
  text-align: left;
}

/* 选项容器 - 简洁现代风格 */
.options-container {
  flex: 1;
  margin-bottom: 40rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* 选项项目 - 简洁现代风格 */
.option-item {
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 16rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.option-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.option-item:active {
  transform: translateY(0rpx);
}

.option-item.selected {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  transform: translateY(-2rpx);
}

.option-prefix {
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  color: #ffffff;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.option-text {
  flex: 1;
  font-size: 28rpx;
  color: #2c3e50;
  line-height: 1.5;
  font-weight: 400;
}

.option-check {
  width: 24rpx;
  height: 24rpx;
  font-size: 18rpx;
  font-weight: bold;
  opacity: 0;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.option-check.checked {
  opacity: 1;
}



/* 输入框容器 - 简洁现代风格 */
.input-container {
  flex: 1;
  margin-bottom: 40rpx;
}

.input-label {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 16rpx;
  font-weight: 500;
}

.answer-input {
  width: 100%;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #2c3e50;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.answer-input:focus {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transform: translateY(-2rpx);
}

.answer-input::placeholder {
  color: rgba(44, 62, 80, 0.5);
}

/* 提交按钮 - 简洁现代风格 */
.submit-container {
  margin-top: auto;
}

.submit-btn {
  width: 100%;
  height: 80rpx;
  background: #4CAF50;
  border: none;
  border-radius: 16rpx;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.3);
  backdrop-filter: blur(10rpx);
}

.submit-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(76, 175, 80, 0.4);
}

.submit-btn:active {
  transform: translateY(0rpx);
}

.submit-btn.disabled {
  background: rgba(0, 0, 0, 0.2);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.submit-btn.disabled:hover {
  transform: none;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 结果页面样式 */
.result-screen {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.result-content {
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  max-width: 600rpx;
  width: 100%;
}

.result-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.result-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20rpx;
}

.result-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 50rpx;
}

.result-stats {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-bottom: 50rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.stat-divider {
  width: 2rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
}

.energy-reward {
  margin-bottom: 50rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20rpx;
}

.reward-title {
  font-size: 32rpx;
  color: #ffffff;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.reward-items {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.reward-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 15rpx 25rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15rpx;
}

.reward-icon {
  font-size: 28rpx;
}

.reward-text {
  font-size: 24rpx;
  color: #ffffff;
}

.result-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.secondary-btn {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.primary-btn {
  color: #ffffff;
}

.action-btn:active {
  transform: scale(0.98);
}

/* 反馈弹窗样式 */
.feedback-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
}

.feedback-overlay.show {
  opacity: 1;
  visibility: visible;
}

.feedback-modal {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 50rpx;
  text-align: center;
  max-width: 500rpx;
  width: 80%;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transform: scale(0.8) translateY(100rpx);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.feedback-overlay.show .feedback-modal {
  transform: scale(1) translateY(0);
}

.feedback-modal.correct {
  border-left: 8rpx solid #4CAF50;
}

.feedback-modal.wrong {
  border-left: 8rpx solid #f44336;
}

.feedback-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.feedback-message {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.feedback-explanation {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}
