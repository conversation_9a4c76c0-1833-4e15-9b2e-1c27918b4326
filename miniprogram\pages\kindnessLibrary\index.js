// 善意行为库主页面
const kindnessData = require('../../utils/kindnessData');

Page({
  data: {
    // 基础数据
    categories: [],
    allActions: [],
    
    // 搜索相关
    searchMode: false,
    searchKeyword: '',
    searchResults: [],
    
    // 筛选相关
    selectedDifficulty: 0, // 0-全部, 1-简单, 2-中等, 3-困难
    
    // 推荐行为
    recommendedActions: [],
    
    // 用户进度
    userProgress: {
      completedActions: 0,
      totalPoints: 0,
      streak: 0
    },
    
    // 本周进度
    weeklyProgress: [],
    
    // 页面状态
    loading: false
  },

  onLoad: function (options) {
    console.log('善意行为库加载');
    this.initializeLibrary();
  },

  onReady: function () {
    console.log('善意行为库渲染完成');
  },

  onShow: function () {
    // 每次显示时刷新用户进度
    this.loadUserProgress();
  },

  onPullDownRefresh: function () {
    this.refreshLibraryData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  // 初始化行为库
  initializeLibrary() {
    this.setData({ loading: true });

    try {
      // 加载分类数据
      this.loadCategories();
      
      // 加载所有行为
      this.loadAllActions();
      
      // 生成推荐行为
      this.generateRecommendations();
      
      // 加载用户进度
      this.loadUserProgress();
      
      // 生成本周进度
      this.generateWeeklyProgress();
      
    } catch (error) {
      console.error('初始化行为库失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载分类数据
  loadCategories() {
    const categories = kindnessData.getAllCategories();
    
    // 为每个分类添加行为数量
    const categoriesWithCount = categories.map(category => {
      const actions = kindnessData.getActionsByCategory(category.id);
      return {
        ...category,
        actionCount: actions.length
      };
    });
    
    this.setData({
      categories: categoriesWithCount
    });
  },

  // 加载所有行为
  loadAllActions() {
    const allActions = kindnessData.kindnessActions;
    this.setData({
      allActions
    });
  },

  // 生成推荐行为
  generateRecommendations() {
    const allActions = this.data.allActions;
    const completedActions = wx.getStorageSync('completedKindnessActions') || [];
    
    // 筛选未完成的行为
    const uncompletedActions = allActions.filter(action => 
      !completedActions.includes(action.id)
    );
    
    // 随机选择3个推荐行为
    const shuffled = uncompletedActions.sort(() => 0.5 - Math.random());
    const recommended = shuffled.slice(0, 3).map(action => ({
      ...action,
      recommendReason: this.getRecommendReason(action)
    }));
    
    this.setData({
      recommendedActions: recommended
    });
  },

  // 获取推荐理由
  getRecommendReason(action) {
    const reasons = {
      1: '简单易做，适合新手',
      2: '挑战适中，收获满满',
      3: '高难度挑战，成就感强'
    };
    
    const seasonReasons = [
      '适合当前季节',
      '热门推荐',
      '积分丰厚',
      '意义深远'
    ];
    
    return reasons[action.difficulty] || seasonReasons[Math.floor(Math.random() * seasonReasons.length)];
  },

  // 加载用户进度
  loadUserProgress() {
    const completedActions = wx.getStorageSync('completedKindnessActions') || [];
    const kindnessPoints = wx.getStorageSync('kindnessPoints') || 0;
    const lastActionDate = wx.getStorageSync('lastKindnessActionDate') || '';
    
    // 计算连续天数
    const streak = this.calculateStreak(lastActionDate);
    
    this.setData({
      userProgress: {
        completedActions: completedActions.length,
        totalPoints: kindnessPoints,
        streak: streak
      }
    });
  },

  // 计算连续天数
  calculateStreak(lastActionDate) {
    if (!lastActionDate) return 0;
    
    const today = new Date().toDateString();
    const lastDate = new Date(lastActionDate).toDateString();
    
    if (today === lastDate) {
      // 今天有行为，检查连续性
      const streakData = wx.getStorageSync('kindnessStreak') || { count: 1, date: today };
      return streakData.count;
    } else {
      // 今天没有行为，重置连续天数
      return 0;
    }
  },

  // 生成本周进度
  generateWeeklyProgress() {
    const weekDays = ['一', '二', '三', '四', '五', '六', '日'];
    const today = new Date();
    const weeklyData = [];
    
    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() - (6 - i));
      
      // 模拟数据，实际应该从存储中获取
      const actionsCount = Math.floor(Math.random() * 5);
      const percentage = Math.min((actionsCount / 5) * 100, 100);
      
      weeklyData.push({
        day: weekDays[i],
        count: actionsCount,
        percentage: percentage
      });
    }
    
    this.setData({
      weeklyProgress: weeklyData
    });
  },

  // 搜索输入
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword
    });
    
    if (keyword.trim()) {
      this.performSearch(keyword);
    } else {
      this.setData({
        searchMode: false,
        searchResults: []
      });
    }
  },

  // 搜索确认
  onSearchConfirm(e) {
    const keyword = e.detail.value;
    if (keyword.trim()) {
      this.performSearch(keyword);
    }
  },

  // 执行搜索
  performSearch(keyword) {
    const results = kindnessData.searchActions(keyword);
    
    this.setData({
      searchMode: true,
      searchResults: results
    });
  },

  // 清除搜索
  onClearSearch() {
    this.setData({
      searchKeyword: '',
      searchMode: false,
      searchResults: []
    });
  },

  // 选择难度筛选
  onSelectDifficulty(e) {
    const difficulty = parseInt(e.currentTarget.dataset.difficulty);
    this.setData({
      selectedDifficulty: difficulty
    });
    
    if (this.data.searchMode) {
      // 在搜索模式下应用筛选
      this.applyDifficultyFilter();
    }
  },

  // 应用难度筛选
  applyDifficultyFilter() {
    let results = this.data.searchResults;
    
    if (this.data.selectedDifficulty > 0) {
      results = results.filter(action => action.difficulty === this.data.selectedDifficulty);
    }
    
    this.setData({
      searchResults: results
    });
  },

  // 选择分类
  onSelectCategory(e) {
    const categoryId = e.currentTarget.dataset.category;
    console.log('选择分类:', categoryId);
    
    // 跳转到分类详情页面
    wx.navigateTo({
      url: `/pages/kindnessCategory/index?categoryId=${categoryId}`
    });
  },

  // 选择行为
  onSelectAction(e) {
    const actionId = e.currentTarget.dataset.action;
    console.log('选择行为:', actionId);
    
    // 跳转到行为详情页面
    wx.navigateTo({
      url: `/pages/kindnessAction/index?actionId=${actionId}`
    });
  },

  // 刷新数据
  refreshLibraryData() {
    this.setData({ loading: true });
    
    setTimeout(() => {
      this.loadCategories();
      this.loadAllActions();
      this.generateRecommendations();
      this.loadUserProgress();
      this.generateWeeklyProgress();
      this.setData({ loading: false });
    }, 1000);
  }
});
