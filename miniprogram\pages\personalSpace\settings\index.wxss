/* 《能量星球》个人设置 - 控制台主题 */

/* ==================== CSS变量系统 ==================== */
page {
  /* 控制台主题色彩 */
  --console-gradient: linear-gradient(135deg, #0F1419 0%, #1E3A8A 30%, #1E40AF 60%, #3B82F6 100%);
  --console-dark: #0F1419;
  --console-medium: #1E3A8A;
  --console-light: #3B82F6;
  
  /* 电路线颜色 */
  --circuit-blue: #00D4FF;
  --circuit-green: #00FF88;
  --circuit-orange: #FF8C00;
  --circuit-purple: #8B5CF6;
  
  /* 状态颜色 */
  --status-active: #10B981;
  --status-warning: #F59E0B;
  --status-danger: #EF4444;
  --status-info: #3B82F6;
  
  /* 主题预览色彩 */
  --theme-space: linear-gradient(135deg, #1E3A8A 0%, #3B82F6 100%);
  --theme-nebula: linear-gradient(135deg, #4A148C 0%, #8E24AA 100%);
  --theme-energy: linear-gradient(135deg, #FF8F00 0%, #FFD700 100%);
  
  /* 阴影和动画 */
  --shadow-subtle: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  --shadow-medium: 0 8rpx 24rpx rgba(0, 0, 0, 0.25);
  --shadow-strong: 0 16rpx 48rpx rgba(0, 0, 0, 0.35);
  
  --duration-fast: 0.2s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;
  
  --ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
}

/* ==================== 基础页面样式 ==================== */
.page {
  width: 100vw;
  height: 100vh;
  background: var(--console-gradient);
  overflow: hidden;
  position: relative;
}

.settings-console {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* ==================== 控制台背景 ==================== */
.console-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.circuit-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.circuit-line {
  position: absolute;
  height: 2rpx;
  background: linear-gradient(90deg, 
    transparent 0%, 
    var(--circuit-blue) 20%, 
    var(--circuit-blue) 80%, 
    transparent 100%
  );
  box-shadow: 0 0 8rpx var(--circuit-blue);
  animation: circuitFlow 6s linear infinite;
}

.circuit-line.line-1 {
  top: 20%;
  left: 0;
  width: 100%;
  animation-delay: 0s;
}

.circuit-line.line-2 {
  top: 45%;
  left: 0;
  width: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    var(--circuit-green) 20%, 
    var(--circuit-green) 80%, 
    transparent 100%
  );
  box-shadow: 0 0 8rpx var(--circuit-green);
  animation-delay: 2s;
}

.circuit-line.line-3 {
  top: 70%;
  left: 0;
  width: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    var(--circuit-orange) 20%, 
    var(--circuit-orange) 80%, 
    transparent 100%
  );
  box-shadow: 0 0 8rpx var(--circuit-orange);
  animation-delay: 4s;
}

.circuit-line.line-4 {
  top: 85%;
  left: 0;
  width: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    var(--circuit-purple) 20%, 
    var(--circuit-purple) 80%, 
    transparent 100%
  );
  box-shadow: 0 0 8rpx var(--circuit-purple);
  animation-delay: 1s;
}

@keyframes circuitFlow {
  0% { 
    opacity: 0.3; 
    transform: translateX(-100%);
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% { 
    opacity: 0.3; 
    transform: translateX(100%);
  }
}

.data-nodes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.data-node {
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background: var(--circuit-blue);
  border-radius: 50%;
  box-shadow: 0 0 15rpx var(--circuit-blue);
  animation: nodePulse 3s ease-in-out infinite;
}

.data-node.node-1 {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.data-node.node-2 {
  top: 45%;
  right: 25%;
  background: var(--circuit-green);
  box-shadow: 0 0 15rpx var(--circuit-green);
  animation-delay: 1s;
}

.data-node.node-3 {
  top: 70%;
  left: 15%;
  background: var(--circuit-orange);
  box-shadow: 0 0 15rpx var(--circuit-orange);
  animation-delay: 2s;
}

.data-node.node-4 {
  top: 85%;
  right: 30%;
  background: var(--circuit-purple);
  box-shadow: 0 0 15rpx var(--circuit-purple);
  animation-delay: 1.5s;
}

@keyframes nodePulse {
  0%, 100% { 
    opacity: 0.6; 
    transform: scale(1); 
  }
  50% { 
    opacity: 1; 
    transform: scale(1.5); 
  }
}

/* ==================== 设置HUD ==================== */
.settings-hud {
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  background: linear-gradient(90deg, 
    rgba(0, 212, 255, 0.1) 0%, 
    rgba(59, 130, 246, 0.1) 100%
  );
  border-bottom: 2rpx solid rgba(0, 212, 255, 0.3);
  backdrop-filter: blur(20rpx);
}

.hud-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.title-icon {
  font-size: 28rpx;
  filter: drop-shadow(0 0 8rpx var(--circuit-blue));
  animation: iconRotate 8s linear infinite;
}

@keyframes iconRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.title-text {
  font-size: 26rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 0 15rpx var(--circuit-blue);
}

.hud-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status-indicator {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: var(--status-active);
  box-shadow: 0 0 10rpx var(--status-active);
  animation: statusBlink 2s ease-in-out infinite;
}

@keyframes statusBlink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.status-text {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* ==================== 主要内容区域 ==================== */
.content-scroll {
  position: relative;
  z-index: 2;
  flex: 1;
  padding: 0 30rpx 40rpx;
  box-sizing: border-box;
  width: 100%;
}

/* ==================== 设置区域 ==================== */
.settings-section {
  margin-bottom: 40rpx;
  background: rgba(255, 255, 255, 0.05);
  border: 2rpx solid rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 24rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: var(--shadow-medium);
}

.settings-section.danger {
  border-color: rgba(239, 68, 68, 0.3);
  background: rgba(239, 68, 68, 0.05);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.section-icon {
  font-size: 24rpx;
  filter: drop-shadow(0 0 8rpx currentColor);
}

.section-title {
  font-size: 24rpx;
  font-weight: bold;
  color: white;
}

/* ==================== 设置项 ==================== */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.05);
  transition: all var(--duration-normal) var(--ease-out-quart);
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item.clickable {
  cursor: pointer;
}

.setting-item.clickable:hover {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12rpx;
  padding-left: 16rpx;
  padding-right: 16rpx;
}

.item-info {
  flex: 1;
}

.item-title {
  display: block;
  font-size: 22rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 4rpx;
}

.item-title.danger-text {
  color: var(--status-danger);
}

.item-description {
  display: block;
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.3;
}

.item-arrow {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.5);
  font-weight: bold;
}

.item-arrow.danger-text {
  color: var(--status-danger);
}

/* ==================== 开关控件 ==================== */
.setting-switch {
  transform: scale(0.8);
}

/* ==================== 选择器 ==================== */
.picker-display {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  padding: 12rpx 16rpx;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out-quart);
}

.picker-display:hover {
  border-color: var(--circuit-blue);
  box-shadow: 0 0 15rpx rgba(0, 212, 255, 0.3);
}

.picker-text {
  font-size: 20rpx;
  color: white;
}

.picker-arrow {
  font-size: 16rpx;
  color: rgba(255, 255, 255, 0.6);
}

/* ==================== 缓存大小显示 ==================== */
.cache-size {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8rpx;
  padding: 6rpx 12rpx;
}

.size-text {
  font-size: 18rpx;
  color: var(--circuit-orange);
  font-weight: bold;
}

/* ==================== 主题选择器 ==================== */
.theme-selector {
  display: flex;
  gap: 16rpx;
  margin-top: 16rpx;
}

.theme-option {
  flex: 1;
  text-align: center;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out-quart);
}

.theme-option.selected {
  transform: translateY(-4rpx);
}

.theme-preview {
  width: 100%;
  height: 80rpx;
  border-radius: 12rpx;
  margin-bottom: 8rpx;
  border: 3rpx solid transparent;
  transition: all var(--duration-normal) var(--ease-out-quart);
  position: relative;
  overflow: hidden;
}

.theme-preview.space {
  background: var(--theme-space);
}

.theme-preview.nebula {
  background: var(--theme-nebula);
}

.theme-preview.energy {
  background: var(--theme-energy);
}

.theme-option.selected .theme-preview {
  border-color: white;
  box-shadow: 0 0 20rpx rgba(255, 255, 255, 0.5);
}

.theme-preview::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%
  );
  animation: themeShimmer 3s linear infinite;
}

@keyframes themeShimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.theme-name {
  font-size: 18rpx;
  color: white;
  font-weight: bold;
}

.theme-option.selected .theme-name {
  color: var(--circuit-blue);
  text-shadow: 0 0 8rpx var(--circuit-blue);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 750rpx) {
  .content-scroll {
    padding: 0 20rpx 30rpx;
  }

  .settings-section {
    padding: 20rpx;
    margin-bottom: 30rpx;
  }

  .setting-item {
    padding: 16rpx 0;
  }

  .theme-selector {
    flex-direction: column;
    gap: 12rpx;
  }

  .theme-option {
    display: flex;
    align-items: center;
    gap: 16rpx;
    text-align: left;
  }

  .theme-preview {
    width: 60rpx;
    height: 60rpx;
    margin-bottom: 0;
  }
}
