/* 《能量星球》成就展示馆 - 荣耀星座主题 */

/* ==================== CSS变量系统 ==================== */
page {
  /* 星座金色系统 */
  --constellation-gold: #FFD700;
  --constellation-gold-light: #FFECB3;
  --constellation-gold-dark: #FF8F00;
  --constellation-gold-glow: rgba(255, 215, 0, 0.4);
  
  /* 稀有度颜色系统 */
  --rarity-common: #9E9E9E;
  --rarity-rare: #2196F3;
  --rarity-epic: #9C27B0;
  --rarity-legendary: #FF9800;
  
  /* 分类颜色系统 */
  --category-learning: #2196F3;
  --category-kindness: #E91E63;
  --category-social: #4CAF50;
  --category-personal: #FF9800;
  
  /* 深空背景 */
  --space-gradient: linear-gradient(135deg, #0D1421 0%, #1A237E 30%, #283593 60%, #3F51B5 100%);
  --space-dark: #0D1421;
  --space-medium: #1A237E;
  --space-light: #3F51B5;
  
  /* 阴影和动画 */
  --shadow-subtle: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  --shadow-medium: 0 8rpx 24rpx rgba(0, 0, 0, 0.25);
  --shadow-strong: 0 16rpx 48rpx rgba(0, 0, 0, 0.35);
  
  --duration-fast: 0.2s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;
  
  --ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ==================== 基础页面样式 ==================== */
.page {
  width: 100%;
  height: 100vh;
  background: var(--space-gradient);
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
}

.achievement-hall {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* ==================== 星座背景 ==================== */
.constellation-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.star-field {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.star {
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  background: var(--constellation-gold);
  border-radius: 50%;
  box-shadow: 0 0 20rpx var(--constellation-gold-glow);
  animation: starPulse 3s ease-in-out infinite;
}

.star-1 { top: 15%; left: 20%; animation-delay: 0s; }
.star-2 { top: 25%; left: 80%; animation-delay: 0.5s; }
.star-3 { top: 40%; left: 15%; animation-delay: 1s; }
.star-4 { top: 55%; left: 85%; animation-delay: 1.5s; }
.star-5 { top: 70%; left: 25%; animation-delay: 2s; }
.star-6 { top: 80%; left: 75%; animation-delay: 2.5s; }
.star-7 { top: 35%; left: 50%; animation-delay: 1.2s; }
.star-8 { top: 60%; left: 60%; animation-delay: 1.8s; }

@keyframes starPulse {
  0%, 100% { 
    opacity: 0.6; 
    transform: scale(1); 
  }
  50% { 
    opacity: 1; 
    transform: scale(1.5); 
  }
}

/* ==================== 星座连线 ==================== */
.constellation-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.constellation-line {
  position: absolute;
  height: 2rpx;
  background: linear-gradient(90deg, 
    transparent 0%, 
    var(--constellation-gold) 20%, 
    var(--constellation-gold) 80%, 
    transparent 100%
  );
  box-shadow: 0 0 10rpx var(--constellation-gold-glow);
  animation: lineGlow 4s ease-in-out infinite;
}

.constellation-line.line-1 {
  top: 20%;
  left: 20%;
  width: 60%;
  transform: rotate(15deg);
  animation-delay: 0s;
}

.constellation-line.line-2 {
  top: 45%;
  left: 15%;
  width: 70%;
  transform: rotate(-10deg);
  animation-delay: 1s;
}

.constellation-line.line-3 {
  top: 65%;
  left: 25%;
  width: 50%;
  transform: rotate(25deg);
  animation-delay: 2s;
}

.constellation-line.line-4 {
  top: 30%;
  left: 50%;
  width: 35%;
  transform: rotate(60deg);
  animation-delay: 1.5s;
}

.constellation-line.line-5 {
  top: 55%;
  left: 60%;
  width: 25%;
  transform: rotate(-45deg);
  animation-delay: 2.5s;
}

@keyframes lineGlow {
  0%, 100% { 
    opacity: 0.3; 
    box-shadow: 0 0 10rpx var(--constellation-gold-glow);
  }
  50% { 
    opacity: 0.8; 
    box-shadow: 0 0 25rpx var(--constellation-gold-glow);
  }
}

/* ==================== 成就统计HUD ==================== */
.achievement-hud {
  position: relative;
  z-index: 10;
  padding: 30rpx 40rpx;
  background: linear-gradient(90deg, 
    rgba(255, 215, 0, 0.1) 0%, 
    rgba(63, 81, 181, 0.1) 100%
  );
  border-bottom: 2rpx solid rgba(255, 215, 0, 0.3);
  backdrop-filter: blur(20rpx);
}

.stats-overview {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-bottom: 20rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: var(--constellation-gold);
  text-shadow: 0 0 15rpx var(--constellation-gold-glow);
  animation: numberGlow 2s ease-in-out infinite;
}

@keyframes numberGlow {
  0%, 100% { 
    text-shadow: 0 0 15rpx var(--constellation-gold-glow);
  }
  50% { 
    text-shadow: 0 0 30rpx var(--constellation-gold-glow);
  }
}

.stat-label {
  display: block;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 4rpx;
}

.stat-divider {
  width: 2rpx;
  height: 40rpx;
  background: rgba(255, 215, 0, 0.3);
  box-shadow: 0 0 8rpx var(--constellation-gold-glow);
}

.latest-achievement {
  display: flex;
  align-items: center;
  gap: 16rpx;
  background: rgba(255, 215, 0, 0.15);
  border: 2rpx solid var(--constellation-gold);
  border-radius: 20rpx;
  padding: 16rpx 20rpx;
  box-shadow: 0 0 20rpx var(--constellation-gold-glow);
  animation: latestGlow 3s ease-in-out infinite;
}

@keyframes latestGlow {
  0%, 100% { 
    box-shadow: 0 0 20rpx var(--constellation-gold-glow);
  }
  50% { 
    box-shadow: 0 0 35rpx var(--constellation-gold-glow);
  }
}

.latest-badge {
  width: 60rpx;
  height: 60rpx;
  background: var(--constellation-gold);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 15rpx var(--constellation-gold-glow);
}

.badge-icon {
  font-size: 28rpx;
  filter: drop-shadow(0 0 8rpx rgba(0, 0, 0, 0.5));
}

.latest-info {
  flex: 1;
}

.latest-title {
  display: block;
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 4rpx;
}

.latest-name {
  display: block;
  font-size: 22rpx;
  font-weight: bold;
  color: white;
}

/* ==================== 主要内容区域 ==================== */
.content-scroll {
  position: relative;
  z-index: 2;
  flex: 1;
  padding: 0 30rpx 40rpx;
  box-sizing: border-box;
  width: 100%;
}

/* ==================== 成就分类 ==================== */
.achievement-categories {
  margin-bottom: 30rpx;
}

.category-tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 16rpx;
  padding: 8rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.15);
}

.tab-item {
  position: relative;
  flex: 1;
  text-align: center;
  padding: 16rpx 8rpx;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out-quart);
  border-radius: 12rpx;
}

.tab-item.active {
  background: rgba(255, 215, 0, 0.2);
  box-shadow: 0 0 15rpx var(--constellation-gold-glow);
}

.tab-text {
  font-size: 22rpx;
  color: white;
  font-weight: bold;
  position: relative;
  z-index: 2;
}

.tab-item.active .tab-text {
  color: var(--constellation-gold);
}

.tab-indicator {
  position: absolute;
  bottom: 4rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 3rpx;
  background: var(--constellation-gold);
  border-radius: 2rpx;
  transition: width var(--duration-normal) var(--ease-out-quart);
  box-shadow: 0 0 8rpx var(--constellation-gold-glow);
}

.tab-item.active .tab-indicator {
  width: 60%;
}

/* ==================== 成就网格 ==================== */
.achievements-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.achievement-card {
  position: relative;
  background: rgba(255, 255, 255, 0.08);
  border: 2rpx solid rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  padding: 24rpx;
  backdrop-filter: blur(10rpx);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out-quart);
  overflow: hidden;
}

.achievement-card:hover {
  background: rgba(255, 255, 255, 0.12);
  transform: translateY(-4rpx);
  box-shadow: var(--shadow-medium);
}

.achievement-card.locked {
  opacity: 0.6;
}

.achievement-card.locked:hover {
  transform: translateY(-2rpx);
}

.card-glow {
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  width: calc(100% + 8rpx);
  height: calc(100% + 8rpx);
  border-radius: 24rpx;
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-out-quart);
}

.achievement-card.unlocked:hover .card-glow.common {
  opacity: 0.3;
  box-shadow: 0 0 20rpx var(--rarity-common);
}

.achievement-card.unlocked:hover .card-glow.rare {
  opacity: 0.3;
  box-shadow: 0 0 20rpx var(--rarity-rare);
}

.achievement-card.unlocked:hover .card-glow.epic {
  opacity: 0.3;
  box-shadow: 0 0 20rpx var(--rarity-epic);
}

.achievement-card.unlocked:hover .card-glow.legendary {
  opacity: 0.3;
  box-shadow: 0 0 20rpx var(--rarity-legendary);
}

.achievement-icon-container {
  position: relative;
  text-align: center;
  margin-bottom: 16rpx;
}

.achievement-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 3rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  transition: all var(--duration-normal) var(--ease-out-quart);
}

.achievement-icon.locked {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.1);
}

.icon-text {
  font-size: 36rpx;
  filter: drop-shadow(0 0 8rpx currentColor);
}

.achievement-icon.locked .icon-text {
  filter: grayscale(100%) brightness(0.5);
}

.rarity-indicator {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  border: 2rpx solid white;
}

.rarity-indicator.common { background: var(--rarity-common); }
.rarity-indicator.rare { background: var(--rarity-rare); }
.rarity-indicator.epic { background: var(--rarity-epic); }
.rarity-indicator.legendary { background: var(--rarity-legendary); }

.achievement-info {
  text-align: center;
}

.achievement-name {
  display: block;
  font-size: 24rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
}

.achievement-description {
  display: block;
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
  margin-bottom: 16rpx;
}

.achievement-meta {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.category-tag {
  align-self: center;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 16rpx;
  font-weight: bold;
  color: white;
}

.category-tag.learning { background: var(--category-learning); }
.category-tag.kindness { background: var(--category-kindness); }
.category-tag.social { background: var(--category-social); }
.category-tag.personal { background: var(--category-personal); }

.unlock-date {
  font-size: 16rpx;
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
  justify-content: center;
}

.progress-text {
  font-size: 16rpx;
  color: rgba(255, 255, 255, 0.7);
}

.mini-progress {
  width: 60rpx;
  height: 6rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3rpx;
  overflow: hidden;
}

.mini-fill {
  height: 100%;
  background: var(--constellation-gold);
  border-radius: 3rpx;
  transition: width var(--duration-slow) var(--ease-out-quart);
}

.rarity-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120%;
  height: 120%;
  border-radius: 50%;
  opacity: 0;
  animation: rarityPulse 3s ease-in-out infinite;
}

.rarity-glow.legendary {
  background: radial-gradient(circle,
    rgba(255, 152, 0, 0.3) 0%,
    transparent 70%
  );
  animation-delay: 0s;
}

.rarity-glow.epic {
  background: radial-gradient(circle,
    rgba(156, 39, 176, 0.3) 0%,
    transparent 70%
  );
  animation-delay: 1s;
}

.rarity-glow.rare {
  background: radial-gradient(circle,
    rgba(33, 150, 243, 0.3) 0%,
    transparent 70%
  );
  animation-delay: 2s;
}

@keyframes rarityPulse {
  0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
}

/* ==================== 空状态 ==================== */
.empty-achievements {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60rpx 40rpx;
  opacity: 0.8;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  filter: drop-shadow(0 0 20rpx var(--constellation-gold-glow));
  animation: emptyFloat 3s ease-in-out infinite;
}

@keyframes emptyFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10rpx); }
}

.empty-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 12rpx;
}

.empty-subtitle {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* ==================== 时间线区域 ==================== */
.timeline-section {
  margin-bottom: 40rpx;
}

.section-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
  text-shadow: 0 0 20rpx var(--constellation-gold-glow);
}

.section-subtitle {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}

.timeline-container {
  position: relative;
}

.timeline-item {
  display: flex;
  margin-bottom: 40rpx;
  position: relative;
}

.timeline-date {
  width: 100rpx;
  text-align: center;
  margin-right: 20rpx;
}

.date-month {
  display: block;
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 4rpx;
}

.date-day {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: var(--constellation-gold);
  text-shadow: 0 0 15rpx var(--constellation-gold-glow);
}

.timeline-connector {
  position: relative;
  width: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20rpx;
}

.connector-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  border: 3rpx solid white;
  box-shadow: 0 0 15rpx currentColor;
  z-index: 2;
}

.connector-dot.learning { background: var(--category-learning); }
.connector-dot.kindness { background: var(--category-kindness); }
.connector-dot.social { background: var(--category-social); }
.connector-dot.personal { background: var(--category-personal); }

.connector-line {
  position: absolute;
  top: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 3rpx;
  height: 60rpx;
  background: linear-gradient(180deg,
    var(--constellation-gold) 0%,
    rgba(255, 215, 0, 0.3) 100%
  );
  box-shadow: 0 0 8rpx var(--constellation-gold-glow);
}

.timeline-content {
  flex: 1;
  background: rgba(255, 255, 255, 0.08);
  border: 2rpx solid rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
  transition: all var(--duration-normal) var(--ease-out-quart);
}

.timeline-content:hover {
  background: rgba(255, 255, 255, 0.12);
  transform: translateX(8rpx);
  box-shadow: var(--shadow-medium);
}

.event-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.event-icon {
  font-size: 24rpx;
  filter: drop-shadow(0 0 8rpx currentColor);
}

.event-title {
  font-size: 24rpx;
  font-weight: bold;
  color: white;
}

.event-description {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
  margin-bottom: 16rpx;
}

.event-achievements {
  display: flex;
  gap: 8rpx;
}

.mini-badge {
  width: 32rpx;
  height: 32rpx;
  background: var(--constellation-gold);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 10rpx var(--constellation-gold-glow);
}

.mini-icon {
  font-size: 16rpx;
}

/* ==================== 分享功能 ==================== */
.share-section {
  margin-bottom: 40rpx;
}

.share-card {
  background: rgba(255, 255, 255, 0.08);
  border: 2rpx solid rgba(255, 215, 0, 0.3);
  border-radius: 20rpx;
  padding: 30rpx;
  backdrop-filter: blur(15rpx);
  box-shadow: 0 0 25rpx var(--constellation-gold-glow);
}

.share-header {
  text-align: center;
  margin-bottom: 24rpx;
}

.share-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
}

.share-subtitle {
  display: block;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
}

.share-preview {
  background: rgba(255, 215, 0, 0.1);
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 24rpx;
  text-align: center;
}

.preview-stats {
  margin-bottom: 16rpx;
}

.preview-text {
  display: block;
  font-size: 20rpx;
  color: white;
  margin-bottom: 4rpx;
}

.preview-badges {
  display: flex;
  justify-content: center;
  gap: 12rpx;
}

.preview-badge {
  width: 40rpx;
  height: 40rpx;
  background: var(--constellation-gold);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 12rpx var(--constellation-gold-glow);
  animation: badgeFloat 2s ease-in-out infinite;
}

.preview-badge:nth-child(1) { animation-delay: 0s; }
.preview-badge:nth-child(2) { animation-delay: 0.5s; }
.preview-badge:nth-child(3) { animation-delay: 1s; }

@keyframes badgeFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-8rpx); }
}

.preview-icon {
  font-size: 20rpx;
}

.share-buttons {
  display: flex;
  gap: 16rpx;
}

.share-button {
  flex: 1;
  background: linear-gradient(135deg,
    var(--constellation-gold) 0%,
    var(--constellation-gold-dark) 100%
  );
  border: 2rpx solid var(--constellation-gold-light);
  border-radius: 16rpx;
  padding: 16rpx;
  text-align: center;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out-quart);
  box-shadow: 0 0 15rpx var(--constellation-gold-glow);
}

.share-button:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 25rpx var(--constellation-gold-glow);
}

.button-text {
  font-size: 22rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 750rpx) {
  .content-scroll {
    padding: 0 20rpx 30rpx;
  }

  .achievements-grid {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }

  .achievement-card {
    padding: 20rpx;
  }

  .timeline-item {
    margin-bottom: 30rpx;
  }

  .share-card {
    padding: 24rpx;
  }

  .share-buttons {
    flex-direction: column;
  }
}
