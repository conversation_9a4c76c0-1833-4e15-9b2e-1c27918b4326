// 《能量星球》地球指挥部 - 家长中心逻辑
const { rewardSystem } = require('../../utils/rewardSystem');
const { aiAnalysisEngine } = require('../../utils/aiAnalysis');
const { cooperationSystem } = require('../../utils/cooperationSystem');
const { reportGenerator } = require('../../utils/reportGenerator');

Page({
  data: {
    // 孩子基础数据
    childData: {
      captainName: '小探索者',
      level: 3
    },
    
    // 今日统计
    todayStats: {
      studyTime: 45,
      gamesCompleted: 3,
      energyGained: 25
    },
    
    // 活跃度百分比
    activityPercentage: 75,
    
    // 能力预览数据
    abilityPreview: [
      { name: '逻辑思维', score: 75 },
      { name: '创意表达', score: 60 },
      { name: '记忆能力', score: 80 }
    ],
    
    // AI分析进度
    aiAnalysisProgress: 85,
    
    // 奖励统计
    rewardStats: {
      active: 5,
      pending: 2
    },
    rewardStatusText: '有新奖励可兑换',
    
    // 设置预览
    settings: {
      dailyLimit: 60,
      notifications: true
    },

    // 亲子协作数据
    cooperationStats: {
      active: 2,
      completed: 5
    },
    cooperationStatusText: '有新的协作任务',

    // 学习报告数据
    weeklyStats: {
      totalTime: 8.5,
      improvement: 15
    },
    chartData: [60, 75, 45, 80, 65, 90, 70], // 7天的数据

    // 页面状态
    loading: false,
    refreshing: false,
    lastRefreshTime: 0,

    // 动画状态
    backAnimating: false,
    refreshAnimating: false
  },

  onLoad: function (options) {
    console.log('地球指挥部加载');
    this.initializeCommandCenter();
  },

  onReady: function () {
    console.log('地球指挥部渲染完成');
  },

  onShow: function () {
    // 只在必要时刷新数据，避免每次都重新加载
    const lastRefresh = this.data.lastRefreshTime || 0;
    const now = Date.now();

    // 如果距离上次刷新超过5分钟，才重新加载数据
    if (now - lastRefresh > 5 * 60 * 1000) {
      this.lightRefreshData();
    }
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    this.refreshAllData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  // 初始化指挥中心（优化版本）
  initializeCommandCenter() {
    this.setData({ loading: true });

    // 第一阶段：加载基础数据（立即执行）
    this.loadChildData();
    this.loadTodayStats();

    // 第二阶段：加载轻量级数据（100ms后）
    setTimeout(() => {
      this.loadSettingsData();
      this.loadRewardData();
    }, 100);

    // 第三阶段：加载重量级数据（300ms后）
    setTimeout(() => {
      this.loadCooperationData();
    }, 300);

    // 第四阶段：加载AI分析和报告（500ms后）
    setTimeout(() => {
      this.loadAIAnalysis();
      this.loadReportData();
      this.setData({ loading: false });
    }, 500);
  },

  // 加载孩子数据
  loadChildData() {
    // 从本地存储获取孩子的基础数据
    const userData = wx.getStorageSync('userData') || {};

    this.setData({
      childData: {
        captainName: userData.captainName || '小探索者',
        level: userData.level || 3,
        // 保留能量数据供其他功能使用，但不在界面显示
        wisdomEnergy: userData.wisdomEnergy || 150,
        loveEnergy: userData.loveEnergy || 89
      }
    });
  },

  // 加载今日统计
  loadTodayStats() {
    const today = new Date().toDateString();
    const todayData = wx.getStorageSync(`stats_${today}`) || {
      studyTime: 0,
      gamesCompleted: 0,
      energyGained: 0
    };
    
    this.setData({ 
      todayStats: todayData,
      activityPercentage: Math.min(100, (todayData.studyTime / 60) * 100)
    });
  },

  // 加载AI分析数据
  loadAIAnalysis() {
    // 获取或生成AI分析数据
    let aiReport = aiAnalysisEngine.getLatestReport();

    if (!aiReport) {
      // 如果没有现有报告，生成新的分析
      const mockGameData = aiAnalysisEngine.generateMockGameData();
      aiReport = aiAnalysisEngine.generateAnalysisReport(this.data.childData, mockGameData);
    }

    // 转换为预览格式
    const abilityPreview = Object.entries(aiReport.abilities).slice(0, 3).map(([name, data]) => ({
      name: this.getAbilityDisplayName(name),
      score: data.score
    }));

    this.setData({
      abilityPreview,
      aiAnalysisProgress: aiReport.progress,
      fullAIReport: aiReport
    });
  },

  // 生成AI分析数据（模拟）
  generateAIAnalysis() {
    const userData = this.data.childData;
    
    // 基于能量值和游戏数据生成能力评估
    const baseLogic = Math.min(100, (userData.wisdomEnergy / 200) * 100);
    const baseCreativity = Math.min(100, ((userData.wisdomEnergy + userData.loveEnergy) / 300) * 80);
    const baseMemory = Math.min(100, (userData.wisdomEnergy / 150) * 90);
    const baseEmpathy = Math.min(100, (userData.loveEnergy / 150) * 100);
    
    return {
      abilities: [
        { name: '逻辑思维', score: Math.round(baseLogic) },
        { name: '创意表达', score: Math.round(baseCreativity) },
        { name: '记忆能力', score: Math.round(baseMemory) },
        { name: '共情能力', score: Math.round(baseEmpathy) }
      ],
      progress: 85
    };
  },

  // 加载奖励数据
  loadRewardData() {
    const rewardStats = rewardSystem.getRewardStats();

    this.setData({
      rewardStats,
      rewardStatusText: rewardStats.pending > 0 ? '有新奖励可兑换' : '暂无待兑换奖励'
    });
  },

  // 加载设置数据
  loadSettingsData() {
    const settingsData = wx.getStorageSync('parentSettings') || {
      dailyLimit: 60,
      notifications: true
    };

    this.setData({ settings: settingsData });
  },

  // 加载协作任务数据
  loadCooperationData() {
    const cooperationStats = cooperationSystem.getTaskStats();
    const activeTasks = cooperationSystem.getAllTasks().filter(t => t.status === 'active');

    let statusText = '暂无进行中的任务';
    if (activeTasks.length > 0) {
      statusText = `有${activeTasks.length}个任务进行中`;
    } else if (cooperationStats.available > 0) {
      statusText = '有新的协作任务可开始';
    }

    this.setData({
      cooperationStats: {
        active: cooperationStats.active,
        completed: cooperationStats.completed
      },
      cooperationStatusText: statusText
    });
  },

  // 加载学习报告数据
  loadReportData() {
    // 获取或生成最新的周度报告
    let weeklyReport = reportGenerator.getLatestReport('weekly');

    if (!weeklyReport) {
      // 如果没有报告，生成一个新的
      const aiReport = this.data.fullAIReport || aiAnalysisEngine.getLatestReport();
      weeklyReport = reportGenerator.generateWeeklyReport(this.data.childData, aiReport);
    }

    this.setData({
      weeklyStats: {
        totalTime: weeklyReport.summary.totalStudyTime / 60, // 转换为小时
        improvement: weeklyReport.summary.improvementRate || 15
      },
      chartData: weeklyReport.chartData,
      fullWeeklyReport: weeklyReport
    });
  },

  // 轻量级数据刷新
  lightRefreshData() {
    this.setData({ refreshing: true });

    // 只刷新关键数据
    this.loadChildData();
    this.loadTodayStats();
    this.loadRewardData();

    setTimeout(() => {
      this.setData({
        refreshing: false,
        lastRefreshTime: Date.now()
      });
    }, 500);
  },

  // 刷新所有数据
  refreshAllData() {
    this.setData({ refreshing: true });

    // 分阶段刷新，避免一次性加载太多
    this.loadChildData();
    this.loadTodayStats();

    setTimeout(() => {
      this.loadRewardData();
      this.loadSettingsData();
    }, 200);

    setTimeout(() => {
      this.loadCooperationData();
    }, 400);

    setTimeout(() => {
      this.loadAIAnalysis();
      this.loadReportData();
      this.setData({
        refreshing: false,
        lastRefreshTime: Date.now()
      });
    }, 600);
  },

  // 打开实时监控台
  onOpenMonitor() {
    console.log('打开实时监控台');
    wx.showModal({
      title: '📊 实时监控台',
      content: '监控功能正在开发中，敬请期待！\n\n将提供详细的学习数据分析和实时状态监控。',
      showCancel: false,
      confirmText: '期待中',
      confirmColor: '#4D9FFF'
    });
  },

  // 打开AI分析中心
  onOpenAnalysis() {
    console.log('打开AI分析中心');

    const report = this.data.fullAIReport;
    if (!report) {
      wx.showToast({
        title: '正在生成分析报告...',
        icon: 'loading',
        duration: 2000
      });
      return;
    }

    // 显示详细的AI分析报告
    const suggestions = report.suggestions.slice(0, 3).map(s => s.suggestion).join('\n\n');
    const weakPoints = report.weakPoints.map(w => `${this.getAbilityDisplayName(w.ability)}: ${w.score}分`).join('\n');

    wx.showModal({
      title: '🧠 AI分析报告',
      content: `总体评分: ${report.overallScore}分\n\n薄弱环节:\n${weakPoints}\n\n改进建议:\n${suggestions}`,
      showCancel: true,
      cancelText: '关闭',
      confirmText: '查看详情',
      confirmColor: '#63E2B7',
      success: (res) => {
        if (res.confirm) {
          // 这里可以跳转到详细的AI分析页面
          wx.showToast({
            title: '详细分析页面开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  // 打开奖励管理
  onOpenRewards() {
    console.log('打开奖励管理');

    const rewards = rewardSystem.getAllRewards();
    const activeRewards = rewards.filter(r => r.status === 'active');

    if (activeRewards.length === 0) {
      wx.showModal({
        title: '🎁 奖励管理',
        content: '暂无活跃奖励。\n\n您可以为孩子设置个性化奖励，激励他们的学习和成长！',
        showCancel: true,
        cancelText: '稍后设置',
        confirmText: '立即设置',
        confirmColor: '#FFD76A',
        success: (res) => {
          if (res.confirm) {
            this.showAddRewardDialog();
          }
        }
      });
      return;
    }

    // 显示奖励列表
    const rewardList = activeRewards.slice(0, 3).map(r => {
      const eligibility = rewardSystem.checkRewardEligibility(r.id, this.data.childData);
      const status = eligibility.eligible ? '✅可兑换' : '⏳未达成';
      return `${r.icon} ${r.name} - ${status}`;
    }).join('\n');

    wx.showModal({
      title: '🎁 奖励管理系统',
      content: `当前奖励:\n${rewardList}\n\n点击"管理奖励"可以添加、编辑或删除奖励。`,
      showCancel: true,
      cancelText: '关闭',
      confirmText: '管理奖励',
      confirmColor: '#FFD76A',
      success: (res) => {
        if (res.confirm) {
          // 这里可以跳转到奖励管理页面
          wx.showToast({
            title: '奖励管理页面开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  // 打开设置中心
  onOpenSettings() {
    console.log('打开设置中心');
    wx.showModal({
      title: '⚙️ 设置控制中心',
      content: '设置功能正在开发中，敬请期待！\n\n将提供完整的个性化设置和权限管理。',
      showCancel: false,
      confirmText: '期待中',
      confirmColor: '#FF6B6B'
    });
  },

  // 打开亲子协作中心
  onOpenCooperation() {
    console.log('打开亲子协作中心');

    const tasks = cooperationSystem.getAllTasks();
    const activeTasks = tasks.filter(t => t.status === 'active');
    const availableTasks = tasks.filter(t => t.status === 'available');

    let content = '';
    if (activeTasks.length > 0) {
      const taskList = activeTasks.slice(0, 2).map(t => `• ${t.title}`).join('\n');
      content = `进行中的任务:\n${taskList}\n\n`;
    }

    if (availableTasks.length > 0) {
      const availableList = availableTasks.slice(0, 2).map(t => `• ${t.title}`).join('\n');
      content += `可开始的任务:\n${availableList}`;
    }

    if (!content) {
      content = '暂无可用任务。\n\n您可以创建自定义的亲子协作任务！';
    }

    wx.showModal({
      title: '🤝 星际协作指挥台',
      content: content,
      showCancel: true,
      cancelText: '关闭',
      confirmText: '管理任务',
      confirmColor: '#FF9F43',
      success: (res) => {
        if (res.confirm) {
          // 这里可以跳转到任务管理页面
          wx.showToast({
            title: '任务管理页面开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  // 打开学习报告
  onOpenReports() {
    console.log('打开学习报告');

    const report = this.data.fullWeeklyReport;
    if (!report) {
      wx.showToast({
        title: '正在生成报告...',
        icon: 'loading',
        duration: 2000
      });
      return;
    }

    const summary = report.summary;
    const content = `本周学习总结:\n\n` +
      `📚 学习时长: ${Math.round(summary.totalStudyTime / 60 * 10) / 10}小时\n` +
      `🎮 完成游戏: ${summary.gamesCompleted}个\n` +
      `📈 进步幅度: ${summary.improvementRate > 0 ? '+' : ''}${summary.improvementRate}%\n` +
      `⚡ 获得能量: 智慧${summary.energyGained.wisdom} 爱心${summary.energyGained.love}`;

    wx.showModal({
      title: '📈 舰长成长档案',
      content: content,
      showCancel: true,
      cancelText: '关闭',
      confirmText: '查看详情',
      confirmColor: '#63E2B7',
      success: (res) => {
        if (res.confirm) {
          // 这里可以跳转到详细报告页面
          wx.showToast({
            title: '详细报告页面开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  // 返回主界面
  onGoBack() {
    // 启动火箭推进器动画
    this.setData({ backAnimating: true });

    // 播放动画效果
    setTimeout(() => {
      // 动画完成后导航
      wx.navigateBack({
        delta: 1
      });
    }, 800); // 0.8秒动画时长
  },

  // 刷新数据
  onRefreshData() {
    // 启动雷达扫描动画
    this.setData({ refreshAnimating: true });

    // 开始数据刷新
    this.refreshAllData();

    // 动画完成后显示成功提示
    setTimeout(() => {
      this.setData({ refreshAnimating: false });
      wx.showToast({
        title: '数据同步完成',
        icon: 'success',
        duration: 1500
      });
    }, 1200); // 1.2秒动画时长
  },

  // 获取能力显示名称
  getAbilityDisplayName(ability) {
    const nameMap = {
      logic: '逻辑思维',
      creativity: '创意表达',
      memory: '记忆能力',
      empathy: '共情能力',
      problemSolving: '问题解决',
      attention: '注意力'
    };
    return nameMap[ability] || ability;
  },

  // 显示添加奖励对话框
  showAddRewardDialog() {
    wx.showModal({
      title: '添加新奖励',
      content: '奖励设置功能正在开发中！\n\n将支持:\n• 自定义奖励名称和描述\n• 设置触发条件\n• 管理有效期\n• 奖励类型选择',
      showCancel: false,
      confirmText: '期待中',
      confirmColor: '#FFD76A'
    });
  },

  // 生成AI分析数据（兼容旧版本）
  generateAIAnalysis() {
    const userData = this.data.childData;

    // 基于能量值和游戏数据生成能力评估
    const baseLogic = Math.min(100, (userData.wisdomEnergy / 200) * 100);
    const baseCreativity = Math.min(100, ((userData.wisdomEnergy + userData.loveEnergy) / 300) * 80);
    const baseMemory = Math.min(100, (userData.wisdomEnergy / 150) * 90);
    const baseEmpathy = Math.min(100, (userData.loveEnergy / 150) * 100);

    return {
      abilities: [
        { name: '逻辑思维', score: Math.round(baseLogic) },
        { name: '创意表达', score: Math.round(baseCreativity) },
        { name: '记忆能力', score: Math.round(baseMemory) },
        { name: '共情能力', score: Math.round(baseEmpathy) }
      ],
      progress: 85
    };
  }
});
