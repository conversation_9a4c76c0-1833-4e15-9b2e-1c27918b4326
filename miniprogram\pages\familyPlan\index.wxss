/* 家庭善意计划样式 */

.family-plan-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 50%, #FF9800 100%);
  position: relative;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 背景装饰 */
.family-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.floating-heart {
  position: absolute;
  font-size: 40rpx;
  opacity: 0.7;
  animation: familyFloat 12s infinite ease-in-out;
}

.heart-1 { top: 10%; left: 15%; animation-delay: 0s; }
.heart-2 { top: 30%; right: 20%; animation-delay: 3s; }
.heart-3 { top: 65%; left: 10%; animation-delay: 6s; }
.heart-4 { top: 80%; right: 15%; animation-delay: 9s; }

@keyframes familyFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-20rpx) rotate(90deg);
    opacity: 0.9;
  }
  50% {
    transform: translateY(-30rpx) rotate(180deg);
    opacity: 1;
  }
  75% {
    transform: translateY(-20rpx) rotate(270deg);
    opacity: 0.9;
  }
}

.warm-glow {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 152, 0, 0.2) 0%, transparent 70%);
  animation: warmGlowPulse 8s infinite ease-out;
}

.glow-1 {
  width: 500rpx;
  height: 500rpx;
  top: 15%;
  left: -150rpx;
  animation-delay: 0s;
}

.glow-2 {
  width: 400rpx;
  height: 400rpx;
  top: 50%;
  right: -100rpx;
  animation-delay: 3s;
}

.glow-3 {
  width: 600rpx;
  height: 600rpx;
  bottom: 15%;
  left: 20%;
  animation-delay: 6s;
}

@keyframes warmGlowPulse {
  0% {
    transform: scale(0.8);
    opacity: 0.4;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}

/* 页面标题 */
.family-header {
  position: relative;
  z-index: 10;
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx 20rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 25rpx;
  backdrop-filter: blur(15rpx);
  border: 2rpx solid rgba(255, 152, 0, 0.4);
}

.header-icon {
  font-size: 60rpx;
  margin-bottom: 15rpx;
  animation: familyHeaderPulse 4s infinite ease-in-out;
}

@keyframes familyHeaderPulse {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 15rpx rgba(255, 152, 0, 0.6));
  }
  50% {
    transform: scale(1.1);
    filter: drop-shadow(0 0 25rpx rgba(255, 152, 0, 0.8));
  }
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #E65100;
  margin-bottom: 8rpx;
  text-shadow: 1rpx 1rpx 3rpx rgba(230, 81, 0, 0.3);
}

.header-subtitle {
  font-size: 24rpx;
  color: #FF9800;
  opacity: 0.8;
}

/* 家庭状态面板 */
.family-status-panel {
  position: relative;
  z-index: 10;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15rpx;
  margin-bottom: 40rpx;
}

.status-card {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  padding: 25rpx 20rpx;
  text-align: center;
  backdrop-filter: blur(15rpx);
  border: 2rpx solid rgba(255, 152, 0, 0.3);
  transition: all 0.3s ease;
}

.status-card:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.4);
}

.card-icon {
  font-size: 40rpx;
  margin-bottom: 15rpx;
  animation: cardIconFloat 5s infinite ease-in-out;
}

@keyframes cardIconFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8rpx);
  }
}

.card-title {
  font-size: 22rpx;
  color: #E65100;
  margin-bottom: 8rpx;
  font-weight: bold;
}

.card-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF6F00;
  margin-bottom: 5rpx;
  text-shadow: 1rpx 1rpx 3rpx rgba(255, 111, 0, 0.3);
}

.card-subtitle {
  font-size: 18rpx;
  color: #FF9800;
  opacity: 0.8;
}

/* 快速创建计划 */
.quick-create-section {
  position: relative;
  z-index: 10;
  margin-bottom: 40rpx;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 25rpx;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #E65100;
  flex: 1;
}

.create-btn, .manage-btn, .view-all-btn {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #FF9800;
  opacity: 0.8;
}

.btn-icon {
  font-size: 20rpx;
  margin-right: 5rpx;
}

.btn-text {
  font-size: 20rpx;
}

.quick-templates {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.template-card {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 15rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 152, 0, 0.3);
  transition: all 0.3s ease;
  overflow: hidden;
}

.template-card:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.4);
}

.template-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  animation: templateIconBounce 4s infinite ease-in-out;
}

@keyframes templateIconBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5rpx);
  }
}

.template-info {
  flex: 1;
}

.template-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #E65100;
  margin-bottom: 5rpx;
}

.template-description {
  font-size: 20rpx;
  color: #FF9800;
  opacity: 0.8;
  margin-bottom: 8rpx;
}

.template-duration {
  font-size: 18rpx;
  color: #FF6F00;
  font-weight: bold;
}

.template-arrow {
  font-size: 24rpx;
  color: #FF9800;
  opacity: 0.6;
  margin-left: 15rpx;
}

.template-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
  background: radial-gradient(circle, rgba(255, 152, 0, 0.2) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.template-card:active .template-glow {
  opacity: 1;
  animation: templateGlow 0.6s ease-out;
}

@keyframes templateGlow {
  0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 0.5;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0;
  }
}

/* 当前计划 */
.current-plans-section {
  position: relative;
  z-index: 10;
  margin-bottom: 40rpx;
}

.plans-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.plan-item {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  padding: 25rpx;
  backdrop-filter: blur(15rpx);
  border: 2rpx solid rgba(255, 152, 0, 0.3);
  transition: all 0.3s ease;
}

.plan-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.4);
}

.plan-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.plan-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.plan-info {
  flex: 1;
}

.plan-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #E65100;
  margin-bottom: 5rpx;
}

.plan-subtitle {
  font-size: 22rpx;
  color: #FF9800;
  opacity: 0.8;
}

.plan-status {
  padding: 6rpx 15rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: bold;
}

.plan-status.active {
  background: rgba(76, 175, 80, 0.8);
  color: white;
}

.plan-status.pending {
  background: rgba(255, 152, 0, 0.8);
  color: white;
}

.plan-status.completed {
  background: rgba(96, 125, 139, 0.8);
  color: white;
}

.plan-progress {
  margin-bottom: 20rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.progress-text {
  font-size: 22rpx;
  color: #E65100;
}

.progress-percentage {
  font-size: 20rpx;
  color: #FF6F00;
  font-weight: bold;
}

.progress-bar {
  height: 10rpx;
  background: rgba(255, 152, 0, 0.3);
  border-radius: 5rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FF9800, #FF6F00);
  border-radius: 5rpx;
  transition: width 0.5s ease;
  box-shadow: 0 0 10rpx rgba(255, 152, 0, 0.6);
}

.plan-participants {
  margin-bottom: 20rpx;
}

.participants-label {
  font-size: 20rpx;
  color: #E65100;
  margin-bottom: 10rpx;
}

.participants-list {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}

.participant {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.participant-avatar {
  font-size: 30rpx;
  margin-bottom: 5rpx;
}

.participant-name {
  font-size: 16rpx;
  color: #FF9800;
  text-align: center;
}

.plan-actions {
  display: flex;
  gap: 15rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  border-radius: 15rpx;
  font-size: 20rpx;
  transition: all 0.3s ease;
}

.update-btn {
  background: linear-gradient(135deg, #FF9800, #FF6F00);
  color: white;
}

.complete-btn {
  background: linear-gradient(135deg, #4CAF50, #66BB6A);
  color: white;
}

.action-btn:active {
  transform: scale(0.95);
}

.action-btn .btn-icon {
  font-size: 18rpx;
  margin-right: 8rpx;
}

.action-btn .btn-text {
  font-size: 18rpx;
  font-weight: bold;
}

/* 无计划状态 */
.no-plans {
  text-align: center;
  padding: 60rpx 20rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 152, 0, 0.3);
}

.no-plans-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.no-plans-text {
  font-size: 28rpx;
  color: #E65100;
  margin-bottom: 10rpx;
  font-weight: bold;
}

.no-plans-tip {
  font-size: 22rpx;
  color: #FF9800;
  opacity: 0.7;
  line-height: 1.4;
}

/* 家庭成员 */
.family-members-section {
  position: relative;
  z-index: 10;
  margin-bottom: 40rpx;
}

.members-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

.member-card {
  position: relative;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 15rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 152, 0, 0.3);
  transition: all 0.3s ease;
}

.member-card:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.4);
}

.member-avatar {
  font-size: 50rpx;
  text-align: center;
  margin-bottom: 15rpx;
  animation: memberAvatarFloat 6s infinite ease-in-out;
}

@keyframes memberAvatarFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8rpx);
  }
}

.member-info {
  text-align: center;
}

.member-name {
  font-size: 24rpx;
  font-weight: bold;
  color: #E65100;
  margin-bottom: 5rpx;
}

.member-role {
  font-size: 20rpx;
  color: #FF9800;
  opacity: 0.8;
  margin-bottom: 8rpx;
}

.member-contribution {
  font-size: 18rpx;
  color: #FF6F00;
  font-weight: bold;
}

.member-status {
  position: absolute;
  top: 15rpx;
  right: 15rpx;
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
}

.member-status.online .status-dot {
  width: 100%;
  height: 100%;
  background: #4CAF50;
  border-radius: 50%;
  animation: statusOnline 2s infinite ease-in-out;
}

.member-status.offline .status-dot {
  width: 100%;
  height: 100%;
  background: #9E9E9E;
  border-radius: 50%;
}

@keyframes statusOnline {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.2);
  }
}

/* 计划历史 */
.plan-history-section {
  position: relative;
  z-index: 10;
  margin-bottom: 40rpx;
}

.history-timeline {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  padding: 30rpx 25rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 152, 0, 0.3);
}

.timeline-item {
  position: relative;
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-marker {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: linear-gradient(135deg, #FF9800, #FF6F00);
  margin-right: 20rpx;
  flex-shrink: 0;
  animation: timelineMarkerGlow 4s infinite ease-in-out;
}

@keyframes timelineMarkerGlow {
  0%, 100% {
    box-shadow: 0 0 10rpx rgba(255, 152, 0, 0.6);
  }
  50% {
    box-shadow: 0 0 20rpx rgba(255, 152, 0, 0.8);
  }
}

.marker-icon {
  font-size: 24rpx;
  color: white;
}

.timeline-content {
  flex: 1;
}

.timeline-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #E65100;
  margin-bottom: 5rpx;
}

.timeline-description {
  font-size: 20rpx;
  color: #FF9800;
  opacity: 0.8;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.timeline-time {
  font-size: 18rpx;
  color: #FF6F00;
  margin-bottom: 5rpx;
}

.timeline-participants {
  font-size: 18rpx;
  color: #FF9800;
  opacity: 0.7;
}

.timeline-line {
  position: absolute;
  left: 24rpx;
  top: 50rpx;
  width: 2rpx;
  height: 30rpx;
  background: linear-gradient(to bottom, #FF9800, #FFB74D);
}

/* 家庭成就 */
.family-achievements-section {
  position: relative;
  z-index: 10;
  margin-bottom: 40rpx;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

.achievement-card {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 15rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 152, 0, 0.3);
  transition: all 0.3s ease;
}

.achievement-card:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.4);
}

.achievement-card.locked {
  opacity: 0.6;
  filter: grayscale(50%);
}

.achievement-card.unlocked {
  border-color: #FFC107;
  animation: achievementUnlocked 3s infinite ease-in-out;
}

@keyframes achievementUnlocked {
  0%, 100% {
    box-shadow: 0 0 15rpx rgba(255, 193, 7, 0.6);
  }
  50% {
    box-shadow: 0 0 25rpx rgba(255, 193, 7, 0.8);
  }
}

.achievement-icon {
  font-size: 40rpx;
  text-align: center;
  margin-bottom: 15rpx;
}

.achievement-info {
  text-align: center;
}

.achievement-name {
  font-size: 22rpx;
  font-weight: bold;
  color: #E65100;
  margin-bottom: 5rpx;
}

.achievement-description {
  font-size: 18rpx;
  color: #FF9800;
  opacity: 0.8;
  margin-bottom: 10rpx;
  line-height: 1.3;
}

.achievement-progress {
  margin-top: 10rpx;
}

.achievement-progress .progress-bar {
  height: 6rpx;
  margin-bottom: 5rpx;
}

.achievement-progress .progress-text {
  font-size: 16rpx;
  color: #FF6F00;
}

.achievement-date {
  font-size: 16rpx;
  color: #FFC107;
  font-weight: bold;
}

.achievement-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80%;
  height: 80%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 193, 7, 0.3) 0%, transparent 70%);
  animation: achievementGlowPulse 3s infinite ease-in-out;
}

@keyframes achievementGlowPulse {
  0%, 100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

/* 家庭统计 */
.family-stats-section {
  position: relative;
  z-index: 10;
  margin-bottom: 40rpx;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
  margin-bottom: 30rpx;
}

.stat-card {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 15rpx;
  padding: 25rpx 20rpx;
  text-align: center;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 152, 0, 0.3);
}

.stat-icon {
  font-size: 35rpx;
  margin-bottom: 10rpx;
}

.stat-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #E65100;
  margin-bottom: 5rpx;
}

.stat-label {
  font-size: 20rpx;
  color: #FF9800;
  opacity: 0.8;
}

.monthly-chart {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 15rpx;
  padding: 25rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 152, 0, 0.3);
}

.chart-title {
  font-size: 24rpx;
  color: #E65100;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-align: center;
}

.chart-bars {
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  height: 120rpx;
}

.chart-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 40rpx;
}

.bar-fill {
  width: 100%;
  background: linear-gradient(to top, #FF6F00, #FF9800);
  border-radius: 4rpx 4rpx 0 0;
  min-height: 10rpx;
  transition: height 0.5s ease;
}

.bar-label {
  font-size: 16rpx;
  color: #FF9800;
  margin-top: 8rpx;
  text-align: center;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 243, 224, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5rpx);
}

.loading-spinner {
  text-align: center;
}

.spinner-icon {
  font-size: 80rpx;
  animation: familySpinnerRotate 2s infinite ease-in-out;
}

@keyframes familySpinnerRotate {
  0% {
    transform: scale(1) rotate(0deg);
    filter: drop-shadow(0 0 10rpx rgba(255, 152, 0, 0.6));
  }
  50% {
    transform: scale(1.2) rotate(180deg);
    filter: drop-shadow(0 0 30rpx rgba(255, 152, 0, 1));
  }
  100% {
    transform: scale(1) rotate(360deg);
    filter: drop-shadow(0 0 10rpx rgba(255, 152, 0, 0.6));
  }
}

.loading-text {
  font-size: 28rpx;
  color: #E65100;
  margin-top: 20rpx;
  font-weight: bold;
}

/* 计划创建成功动画 */
.plan-success-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 243, 224, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  backdrop-filter: blur(10rpx);
}

.success-content {
  text-align: center;
  position: relative;
}

.success-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  animation: familySuccessBounce 1s ease-out;
}

@keyframes familySuccessBounce {
  0% {
    transform: scale(0) rotate(0deg);
    filter: drop-shadow(0 0 0 rgba(255, 152, 0, 0));
  }
  30% {
    transform: scale(1.5) rotate(120deg);
    filter: drop-shadow(0 0 40rpx rgba(255, 152, 0, 1));
  }
  60% {
    transform: scale(0.8) rotate(240deg);
    filter: drop-shadow(0 0 60rpx rgba(255, 193, 7, 1));
  }
  100% {
    transform: scale(1) rotate(360deg);
    filter: drop-shadow(0 0 30rpx rgba(255, 152, 0, 0.8));
  }
}

.success-message {
  font-size: 36rpx;
  font-weight: bold;
  color: #E65100;
  margin-bottom: 40rpx;
  text-shadow: 1rpx 1rpx 3rpx rgba(230, 81, 0, 0.3);
  animation: familyMessageGlow 2s infinite ease-in-out;
}

@keyframes familyMessageGlow {
  0%, 100% {
    text-shadow: 1rpx 1rpx 3rpx rgba(230, 81, 0, 0.3);
  }
  50% {
    text-shadow: 2rpx 2rpx 8rpx rgba(230, 81, 0, 0.6), 0 0 20rpx rgba(255, 152, 0, 0.4);
  }
}

.success-family {
  position: absolute;
  top: -100rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 400rpx;
  height: 400rpx;
}

.family-member {
  position: absolute;
  font-size: 50rpx;
  animation: familyMemberCelebrate 3s ease-out forwards;
}

.family-member.member-1 {
  top: 50%;
  left: 30%;
  animation-delay: 0.2s;
}

.family-member.member-2 {
  top: 30%;
  right: 30%;
  animation-delay: 0.4s;
}

.family-member.member-3 {
  bottom: 30%;
  left: 30%;
  animation-delay: 0.6s;
}

.family-member.member-4 {
  bottom: 30%;
  right: 30%;
  animation-delay: 0.8s;
}

.family-heart {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 60rpx;
  animation: familyHeartPulse 1.5s infinite ease-in-out;
  animation-delay: 1s;
}

@keyframes familyMemberCelebrate {
  0% {
    transform: translate(-50%, -50%) scale(0) rotate(0deg);
    opacity: 1;
    filter: drop-shadow(0 0 0 rgba(255, 152, 0, 0));
  }
  20% {
    transform: translate(-50%, -50%) scale(1.3) rotate(90deg);
    opacity: 1;
    filter: drop-shadow(0 0 30rpx rgba(255, 152, 0, 1));
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1) rotate(180deg);
    opacity: 0.9;
    filter: drop-shadow(0 0 40rpx rgba(255, 193, 7, 1));
  }
  80% {
    transform: translate(-50%, -200rpx) scale(0.9) rotate(270deg);
    opacity: 0.5;
    filter: drop-shadow(0 0 20rpx rgba(255, 152, 0, 0.8));
  }
  100% {
    transform: translate(-50%, -300rpx) scale(0.5) rotate(360deg);
    opacity: 0;
    filter: drop-shadow(0 0 0 rgba(255, 152, 0, 0));
  }
}

@keyframes familyHeartPulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    filter: drop-shadow(0 0 20rpx rgba(255, 20, 147, 0.8));
  }
  50% {
    transform: translate(-50%, -50%) scale(1.3);
    filter: drop-shadow(0 0 40rpx rgba(255, 20, 147, 1));
  }
}
