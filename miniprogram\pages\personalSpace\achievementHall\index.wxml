<!-- 《能量星球》成就展示馆 - 荣耀星座主题 -->
<view class="page">
  <view class="achievement-hall">
    
    <!-- 星座背景 -->
    <view class="constellation-background">
      <view class="star-field">
        <view class="star star-1"></view>
        <view class="star star-2"></view>
        <view class="star star-3"></view>
        <view class="star star-4"></view>
        <view class="star star-5"></view>
        <view class="star star-6"></view>
        <view class="star star-7"></view>
        <view class="star star-8"></view>
      </view>
      
      <!-- 星座连线 -->
      <view class="constellation-lines">
        <view class="constellation-line line-1"></view>
        <view class="constellation-line line-2"></view>
        <view class="constellation-line line-3"></view>
        <view class="constellation-line line-4"></view>
        <view class="constellation-line line-5"></view>
      </view>
    </view>

    <!-- 成就统计HUD -->
    <view class="achievement-hud">
      <view class="stats-overview">
        <view class="stat-item">
          <text class="stat-number">{{totalAchievements}}</text>
          <text class="stat-label">总成就</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{completionRate}}%</text>
          <text class="stat-label">完成度</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{recentCount}}</text>
          <text class="stat-label">本周新增</text>
        </view>
      </view>
      
      <view class="latest-achievement" wx:if="{{latestAchievement}}">
        <view class="latest-badge">
          <text class="badge-icon">{{latestAchievement.icon}}</text>
        </view>
        <view class="latest-info">
          <text class="latest-title">最新获得</text>
          <text class="latest-name">{{latestAchievement.name}}</text>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <scroll-view class="content-scroll" scroll-y="true">
      
      <!-- 成就分类 -->
      <view class="achievement-categories">
        <view class="category-tabs">
          <view class="tab-item {{selectedCategory === 'all' ? 'active' : ''}}" 
                bindtap="onSelectCategory" data-category="all">
            <text class="tab-text">全部</text>
            <view class="tab-indicator"></view>
          </view>
          <view class="tab-item {{selectedCategory === 'learning' ? 'active' : ''}}" 
                bindtap="onSelectCategory" data-category="learning">
            <text class="tab-text">学习</text>
            <view class="tab-indicator"></view>
          </view>
          <view class="tab-item {{selectedCategory === 'kindness' ? 'active' : ''}}" 
                bindtap="onSelectCategory" data-category="kindness">
            <text class="tab-text">善意</text>
            <view class="tab-indicator"></view>
          </view>
          <view class="tab-item {{selectedCategory === 'social' ? 'active' : ''}}" 
                bindtap="onSelectCategory" data-category="social">
            <text class="tab-text">社交</text>
            <view class="tab-indicator"></view>
          </view>
          <view class="tab-item {{selectedCategory === 'personal' ? 'active' : ''}}" 
                bindtap="onSelectCategory" data-category="personal">
            <text class="tab-text">个人</text>
            <view class="tab-indicator"></view>
          </view>
        </view>
      </view>

      <!-- 成就网格 -->
      <view class="achievements-grid">
        <view class="achievement-card {{item.unlocked ? 'unlocked' : 'locked'}}" 
              wx:for="{{filteredAchievements}}" wx:key="id"
              bindtap="onViewAchievement" data-achievement="{{item}}">
          
          <view class="card-glow {{item.rarity}}"></view>
          
          <view class="achievement-icon-container">
            <view class="achievement-icon {{item.unlocked ? '' : 'locked'}}">
              <text class="icon-text">{{item.icon}}</text>
            </view>
            <view class="rarity-indicator {{item.rarity}}" wx:if="{{item.unlocked}}"></view>
          </view>
          
          <view class="achievement-info">
            <text class="achievement-name">{{item.name}}</text>
            <text class="achievement-description">{{item.description}}</text>
            
            <view class="achievement-meta">
              <view class="category-tag {{item.category}}">
                <text class="tag-text">{{getCategoryName(item.category)}}</text>
              </view>
              
              <view class="unlock-date" wx:if="{{item.unlocked}}">
                <text class="date-text">{{item.unlockDate}}</text>
              </view>
              
              <view class="progress-info" wx:if="{{!item.unlocked && item.progress}}">
                <text class="progress-text">{{item.progress}}/{{item.target}}</text>
                <view class="mini-progress">
                  <view class="mini-fill" style="width: {{(item.progress/item.target)*100}}%"></view>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 稀有度光效 -->
          <view class="rarity-glow {{item.rarity}}" wx:if="{{item.unlocked}}"></view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-achievements" wx:if="{{filteredAchievements.length === 0}}">
          <view class="empty-icon">🏆</view>
          <text class="empty-title">暂无成就</text>
          <text class="empty-subtitle">继续探索，解锁更多成就！</text>
        </view>
      </view>

      <!-- 成长时间线 -->
      <view class="timeline-section" wx:if="{{selectedCategory === 'all'}}">
        <view class="section-header">
          <text class="section-title">🌟 成长时间线</text>
          <text class="section-subtitle">记录你的每一个重要时刻</text>
        </view>
        
        <view class="timeline-container">
          <view class="timeline-item" wx:for="{{timelineEvents}}" wx:key="id">
            <view class="timeline-date">
              <text class="date-month">{{item.month}}</text>
              <text class="date-day">{{item.day}}</text>
            </view>
            
            <view class="timeline-connector">
              <view class="connector-dot {{item.type}}"></view>
              <view class="connector-line" wx:if="{{index < timelineEvents.length - 1}}"></view>
            </view>
            
            <view class="timeline-content">
              <view class="event-header">
                <text class="event-icon">{{item.icon}}</text>
                <text class="event-title">{{item.title}}</text>
              </view>
              <text class="event-description">{{item.description}}</text>
              
              <view class="event-achievements" wx:if="{{item.achievements}}">
                <view class="mini-badge" wx:for="{{item.achievements}}" wx:key="id" wx:for-item="badge">
                  <text class="mini-icon">{{badge.icon}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 分享功能 -->
      <view class="share-section" wx:if="{{selectedCategory === 'all'}}">
        <view class="share-card">
          <view class="share-header">
            <text class="share-title">✨ 分享我的成就</text>
            <text class="share-subtitle">让好友和家长看到你的进步</text>
          </view>
          
          <view class="share-preview">
            <view class="preview-stats">
              <text class="preview-text">我已获得 {{totalAchievements}} 个成就</text>
              <text class="preview-text">完成度达到 {{completionRate}}%</text>
            </view>
            
            <view class="preview-badges">
              <view class="preview-badge" wx:for="{{topAchievements}}" wx:key="id">
                <text class="preview-icon">{{item.icon}}</text>
              </view>
            </view>
          </view>
          
          <view class="share-buttons">
            <view class="share-button" bindtap="onShareToFriends">
              <text class="button-text">分享给好友</text>
            </view>
            <view class="share-button" bindtap="onShareToParents">
              <text class="button-text">分享给家长</text>
            </view>
          </view>
        </view>
      </view>

    </scroll-view>

  </view>
</view>
