/* pages/exploration/gameEngine/index.wxss */
/* 游戏引擎页面样式 - 简洁设计 */

.game-container {
  width: 100%;
  height: 100vh;
  background: linear-gradient(180deg, #1A1A2E 0%, #16213E 50%, #0F3460 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

/* 添加星空背景动画 */
.game-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.8), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(255,255,255,0.6), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.4), transparent),
    radial-gradient(2px 2px at 130px 80px, rgba(255,255,255,0.7), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: starTwinkle 8s ease-in-out infinite;
  z-index: 1;
  pointer-events: none;
}

@keyframes starTwinkle {
  0%, 100% { opacity: 0.3; transform: translateY(0px); }
  50% { opacity: 0.8; transform: translateY(-5px); }
}

/* 显示控制 */
.show {
  display: flex;
  opacity: 1;
  visibility: visible;
}

.hide {
  display: none;
  opacity: 0;
  visibility: hidden;
}

/* 游戏启动界面 */
.game-start {
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
}

.start-card {
  width: 100%;
  max-width: 600rpx;
  background: linear-gradient(145deg, rgba(26, 26, 46, 0.9), rgba(22, 33, 62, 0.8));
  border-radius: 30rpx;
  padding: 50rpx;
  border: 1px solid rgba(255, 255, 255, 0.15);
  text-align: center;
  position: relative;
  z-index: 10;
  backdrop-filter: blur(20px);
  box-shadow:
    0 15rpx 50rpx rgba(0, 0, 0, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.2);
  animation: cardEntrance 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform: perspective(1000rpx) rotateX(5deg);
}

.start-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(77, 159, 255, 0.1), transparent 50%, rgba(255, 215, 0, 0.05));
  border-radius: 30rpx;
  z-index: -1;
}

@keyframes cardEntrance {
  0% {
    transform: perspective(1000rpx) rotateX(15deg) scale(0.8);
    opacity: 0;
  }
  100% {
    transform: perspective(1000rpx) rotateX(5deg) scale(1);
    opacity: 1;
  }
}

.game-header {
  margin-bottom: 30rpx;
}

.game-planet {
  color: #4D9FFF;
  font-size: 24rpx;
  display: block;
  margin-bottom: 10rpx;
}

.game-title {
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: bold;
  display: block;
}

.game-icon {
  margin: 40rpx 0;
  position: relative;
}

.icon-emoji {
  font-size: 100rpx;
  animation: iconFloat3D 4s ease-in-out infinite;
  filter:
    drop-shadow(0 0 20rpx rgba(255, 255, 255, 0.4))
    drop-shadow(0 8rpx 15rpx rgba(0, 0, 0, 0.3));
  transform: perspective(300rpx) rotateX(10deg);
}

@keyframes iconFloat3D {
  0%, 100% {
    transform: perspective(300rpx) rotateX(10deg) translateY(0rpx) scale(1);
  }
  50% {
    transform: perspective(300rpx) rotateX(10deg) translateY(-10rpx) scale(1.1);
  }
}

.game-info {
  margin: 30rpx 0;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20rpx 0;
  padding: 20rpx 25rpx;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03));
  border-radius: 15rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 4rpx 15rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.1);
  transform: perspective(500rpx) rotateX(3deg);
  transition: all 0.3s ease;
}

.info-row:hover {
  transform: perspective(500rpx) rotateX(1deg) translateY(-2rpx);
  box-shadow:
    0 6rpx 20rpx rgba(0, 0, 0, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.05);
}

.info-label {
  color: #B0BEC5;
  font-size: 26rpx;
}

.info-value {
  color: #FFD700;
  font-size: 26rpx;
  font-weight: bold;
}

.game-desc {
  color: #B0BEC5;
  font-size: 24rpx;
  line-height: 1.5;
  margin: 30rpx 0;
  display: block;
}

.start-actions {
  margin-top: 40rpx;
}

.start-btn {
  width: 100%;
  height: 90rpx;
  background: linear-gradient(145deg, #4D9FFF 0%, #2196F3 50%, #1976D2 100%);
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 20rpx;
  border: none;
  margin-bottom: 20rpx;
  box-shadow:
    0 8rpx 25rpx rgba(77, 159, 255, 0.4),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform: perspective(500rpx) rotateX(5deg);
  position: relative;
  overflow: hidden;
}

.start-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.start-btn:active {
  transform: perspective(500rpx) rotateX(8deg) translateY(2rpx);
  box-shadow:
    0 4rpx 15rpx rgba(77, 159, 255, 0.6),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.3);
}

.start-btn:active::before {
  left: 100%;
}

.back-btn {
  width: 100%;
  height: 70rpx;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(200, 200, 200, 0.05));
  color: #B0BEC5;
  font-size: 26rpx;
  border-radius: 15rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    inset 0 1rpx 0 rgba(255, 255, 255, 0.1),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.1);
  transform: perspective(500rpx) rotateX(3deg);
  transition: all 0.3s ease;
}

.back-btn:active {
  transform: perspective(500rpx) rotateX(6deg) translateY(1rpx);
  box-shadow:
    inset 0 2rpx 4rpx rgba(0, 0, 0, 0.2),
    inset 0 -1rpx 0 rgba(255, 255, 255, 0.05);
}

/* 游戏进行界面 */
.game-playing {
  width: 100%;
  height: 100%;
  flex-direction: column;
}

.game-hud {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 35rpx;
  background: linear-gradient(145deg, rgba(26, 26, 46, 0.8), rgba(22, 33, 62, 0.9));
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  border: 1px solid rgba(255, 255, 255, 0.15);
  position: relative;
  z-index: 10;
  backdrop-filter: blur(15px);
  box-shadow:
    0 6rpx 25rpx rgba(0, 0, 0, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.1);
  transform: perspective(800rpx) rotateX(3deg);
}

.game-hud::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(77, 159, 255, 0.05), transparent 50%, rgba(255, 215, 0, 0.03));
  border-radius: 20rpx;
  z-index: -1;
}

.timer {
  color: #4D9FFF;
  font-size: 26rpx;
  font-weight: bold;
}

.score {
  color: #FFD700;
  font-size: 26rpx;
  font-weight: bold;
}

.game-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.game-demo {
  text-align: center;
  background: linear-gradient(145deg, rgba(26, 26, 46, 0.7), rgba(22, 33, 62, 0.8));
  border-radius: 25rpx;
  padding: 50rpx;
  border: 1px solid rgba(255, 255, 255, 0.15);
  position: relative;
  backdrop-filter: blur(20px);
  box-shadow:
    0 10rpx 40rpx rgba(0, 0, 0, 0.25),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.2);
  transform: perspective(1000rpx) rotateX(5deg);
  animation: demoFloat 6s ease-in-out infinite;
}

.game-demo::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(77, 159, 255, 0.08), transparent 50%, rgba(255, 215, 0, 0.04));
  border-radius: 25rpx;
  z-index: -1;
}

@keyframes demoFloat {
  0%, 100% {
    transform: perspective(1000rpx) rotateX(5deg) translateY(0rpx);
  }
  50% {
    transform: perspective(1000rpx) rotateX(5deg) translateY(-5rpx);
  }
}

.demo-icon {
  font-size: 100rpx;
  display: block;
  margin-bottom: 25rpx;
  animation: demoIconSpin 8s linear infinite;
  filter:
    drop-shadow(0 0 20rpx rgba(255, 255, 255, 0.4))
    drop-shadow(0 8rpx 15rpx rgba(0, 0, 0, 0.3));
  transform: perspective(300rpx) rotateX(10deg);
}

@keyframes demoIconSpin {
  0% { transform: perspective(300rpx) rotateX(10deg) rotateZ(0deg); }
  100% { transform: perspective(300rpx) rotateX(10deg) rotateZ(360deg); }
}

.demo-title {
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}

.demo-desc {
  color: #B0BEC5;
  font-size: 24rpx;
  margin-bottom: 30rpx;
  display: block;
}

.demo-buttons {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.demo-btn {
  width: 100%;
  height: 70rpx;
  border-radius: 15rpx;
  border: none;
  font-size: 26rpx;
  font-weight: bold;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform: perspective(500rpx) rotateX(3deg);
}

.demo-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.demo-btn:active::before {
  left: 100%;
}

.demo-btn:active {
  transform: perspective(500rpx) rotateX(6deg) translateY(2rpx);
}

.demo-btn.correct {
  background: linear-gradient(145deg, rgba(76, 175, 80, 0.3), rgba(76, 175, 80, 0.15));
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.4);
  box-shadow:
    0 4rpx 15rpx rgba(76, 175, 80, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.1);
}

.demo-btn.wrong {
  background: linear-gradient(145deg, rgba(244, 67, 54, 0.3), rgba(244, 67, 54, 0.15));
  color: #F44336;
  border: 1px solid rgba(244, 67, 54, 0.4);
  box-shadow:
    0 4rpx 15rpx rgba(244, 67, 54, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.1);
}

.demo-btn.complete {
  background: linear-gradient(145deg, rgba(255, 215, 0, 0.3), rgba(255, 215, 0, 0.15));
  color: #FFD700;
  border: 1px solid rgba(255, 215, 0, 0.4);
  box-shadow:
    0 4rpx 15rpx rgba(255, 215, 0, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.1);
}

/* 故事理解游戏样式 */
.story-game {
  width: 100%;
  max-width: 700rpx;
  margin: 0 auto;
}

.story-card {
  background: linear-gradient(145deg, rgba(26, 26, 46, 0.9), rgba(22, 33, 62, 0.8));
  border-radius: 25rpx;
  padding: 40rpx;
  border: 1px solid rgba(255, 255, 255, 0.15);
  position: relative;
  backdrop-filter: blur(20px);
  box-shadow:
    0 15rpx 50rpx rgba(0, 0, 0, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.2);
  animation: storyCardEntrance 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform: perspective(1000rpx) rotateX(3deg);
}

.story-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(156, 39, 176, 0.08), transparent 50%, rgba(255, 215, 0, 0.04));
  border-radius: 25rpx;
  z-index: -1;
}

@keyframes storyCardEntrance {
  0% {
    transform: perspective(1000rpx) rotateX(15deg) scale(0.9);
    opacity: 0;
  }
  100% {
    transform: perspective(1000rpx) rotateX(3deg) scale(1);
    opacity: 1;
  }
}

.story-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  gap: 15rpx;
}

.story-icon {
  font-size: 40rpx;
  animation: storyIconFloat 3s ease-in-out infinite;
  filter: drop-shadow(0 0 15rpx rgba(156, 39, 176, 0.5));
}

@keyframes storyIconFloat {
  0%, 100% { transform: translateY(0rpx) scale(1); }
  50% { transform: translateY(-5rpx) scale(1.1); }
}

.story-title {
  color: #9C27B0;
  font-size: 28rpx;
  font-weight: bold;
}

.story-content {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03));
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border: 1px solid rgba(156, 39, 176, 0.2);
  box-shadow:
    0 6rpx 20rpx rgba(0, 0, 0, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.15),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.1);
  transform: perspective(500rpx) rotateX(2deg);
}

.story-text {
  color: #FFFFFF;
  font-size: 30rpx;
  line-height: 1.6;
  text-align: left;
  display: block;
}

.question-section {
  margin-top: 30rpx;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 15rpx 20rpx;
  background: linear-gradient(145deg, rgba(156, 39, 176, 0.15), rgba(156, 39, 176, 0.08));
  border-radius: 15rpx;
  border: 1px solid rgba(156, 39, 176, 0.3);
}

.question-number {
  color: #9C27B0;
  font-size: 26rpx;
  font-weight: bold;
}

.question-progress {
  color: #B0BEC5;
  font-size: 24rpx;
}

.question-text {
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 25rpx;
  display: block;
  text-align: center;
}

.options-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15rpx;
  margin-bottom: 30rpx;
}

.option-btn {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03));
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 15rpx;
  padding: 20rpx 15rpx;
  min-height: 100rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform: perspective(500rpx) rotateX(2deg);
  position: relative;
  overflow: hidden;
}

.option-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.option-btn:active::before {
  left: 100%;
}

.option-btn:active {
  transform: perspective(500rpx) rotateX(5deg) translateY(2rpx);
}

.option-btn.selected {
  background: linear-gradient(145deg, rgba(156, 39, 176, 0.3), rgba(156, 39, 176, 0.15));
  border: 2px solid #9C27B0;
  box-shadow:
    0 6rpx 20rpx rgba(156, 39, 176, 0.4),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.1);
  transform: perspective(500rpx) rotateX(2deg) scale(1.05);
}

.option-label {
  color: #9C27B0;
  font-size: 24rpx;
  font-weight: bold;
  background: linear-gradient(145deg, rgba(156, 39, 176, 0.2), rgba(156, 39, 176, 0.1));
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(156, 39, 176, 0.3);
}

.option-btn.selected .option-label {
  background: linear-gradient(145deg, #9C27B0, #7B1FA2);
  color: #FFFFFF;
  box-shadow: 0 4rpx 15rpx rgba(156, 39, 176, 0.5);
}

.option-text {
  color: #FFFFFF;
  font-size: 26rpx;
  text-align: center;
  line-height: 1.3;
}

.question-actions {
  text-align: center;
}

.confirm-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 20rpx;
  border: none;
  font-size: 30rpx;
  font-weight: bold;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform: perspective(500rpx) rotateX(3deg);
  position: relative;
  overflow: hidden;
}

.confirm-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.confirm-btn:active::before {
  left: 100%;
}

.confirm-btn.active {
  background: linear-gradient(145deg, #9C27B0 0%, #7B1FA2 50%, #6A1B9A 100%);
  color: #FFFFFF;
  box-shadow:
    0 8rpx 25rpx rgba(156, 39, 176, 0.5),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.2);
}

.confirm-btn.active:active {
  transform: perspective(500rpx) rotateX(6deg) translateY(2rpx);
  box-shadow:
    0 4rpx 15rpx rgba(156, 39, 176, 0.7),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.3);
}

.confirm-btn.disabled {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.05), rgba(200, 200, 200, 0.03));
  color: #666;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    inset 0 1rpx 0 rgba(255, 255, 255, 0.05),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.05);
  cursor: not-allowed;
}

/* 游戏完成界面 */
.game-complete {
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
}

.complete-card {
  width: 100%;
  max-width: 600rpx;
  background: linear-gradient(145deg, rgba(26, 26, 46, 0.9), rgba(22, 33, 62, 0.8));
  border-radius: 30rpx;
  padding: 50rpx;
  border: 1px solid rgba(255, 215, 0, 0.4);
  text-align: center;
  position: relative;
  backdrop-filter: blur(20px);
  box-shadow:
    0 20rpx 60rpx rgba(255, 215, 0, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.2);
  animation: completeEntrance 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform: perspective(1000rpx) rotateX(5deg);
}

.complete-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), transparent 50%, rgba(255, 165, 0, 0.05));
  border-radius: 30rpx;
  z-index: -1;
  animation: completeGlow 3s ease-in-out infinite;
}

@keyframes completeEntrance {
  0% {
    transform: perspective(1000rpx) rotateX(15deg) scale(0.8);
    opacity: 0;
  }
  100% {
    transform: perspective(1000rpx) rotateX(5deg) scale(1);
    opacity: 1;
  }
}

@keyframes completeGlow {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.complete-header {
  margin-bottom: 30rpx;
}

.complete-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 20rpx;
  animation: completeIconCelebrate 2s ease-in-out infinite;
  filter:
    drop-shadow(0 0 25rpx rgba(255, 215, 0, 0.8))
    drop-shadow(0 8rpx 15rpx rgba(0, 0, 0, 0.3));
  transform: perspective(300rpx) rotateX(10deg);
}

@keyframes completeIconCelebrate {
  0%, 100% {
    transform: perspective(300rpx) rotateX(10deg) scale(1) rotateZ(0deg);
  }
  25% {
    transform: perspective(300rpx) rotateX(10deg) scale(1.1) rotateZ(-5deg);
  }
  75% {
    transform: perspective(300rpx) rotateX(10deg) scale(1.1) rotateZ(5deg);
  }
}

.complete-title {
  color: #FFD700;
  font-size: 32rpx;
  font-weight: bold;
  display: block;
}

.result-stats {
  margin: 30rpx 0;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20rpx 0;
  padding: 20rpx 25rpx;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03));
  border-radius: 15rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 4rpx 15rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.1);
  transform: perspective(500rpx) rotateX(3deg);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: perspective(500rpx) rotateX(1deg) translateY(-2rpx);
}

.stat-item.energy {
  background: linear-gradient(145deg, rgba(255, 215, 0, 0.15), rgba(255, 215, 0, 0.08));
  border: 1px solid rgba(255, 215, 0, 0.4);
  box-shadow:
    0 6rpx 20rpx rgba(255, 215, 0, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3),
    inset 0 -1rpx 0 rgba(255, 165, 0, 0.2);
}

.stat-label {
  color: #B0BEC5;
  font-size: 26rpx;
}

.stat-value {
  color: #FFFFFF;
  font-size: 26rpx;
  font-weight: bold;
}

.stat-item.energy .stat-value {
  color: #FFD700;
}

.achievements {
  margin: 30rpx 0;
  padding: 20rpx;
  background: rgba(255, 215, 0, 0.1);
  border-radius: 15rpx;
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.achievement-title {
  color: #FFD700;
  font-size: 26rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
  display: block;
}

.achievement-list {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.achievement-item {
  color: #FFD700;
  font-size: 24rpx;
  padding: 8rpx 15rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8rpx;
}

.complete-actions {
  margin-top: 40rpx;
}

.continue-btn {
  width: 100%;
  height: 90rpx;
  background: linear-gradient(145deg, #4D9FFF 0%, #2196F3 50%, #1976D2 100%);
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 20rpx;
  border: none;
  margin-bottom: 20rpx;
  box-shadow:
    0 8rpx 25rpx rgba(77, 159, 255, 0.4),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform: perspective(500rpx) rotateX(5deg);
  position: relative;
  overflow: hidden;
}

.continue-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.continue-btn:active {
  transform: perspective(500rpx) rotateX(8deg) translateY(2rpx);
  box-shadow:
    0 4rpx 15rpx rgba(77, 159, 255, 0.6),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.3);
}

.continue-btn:active::before {
  left: 100%;
}

.complete-actions .back-btn {
  width: 100%;
  height: 70rpx;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(200, 200, 200, 0.05));
  color: #B0BEC5;
  font-size: 26rpx;
  border-radius: 15rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    inset 0 1rpx 0 rgba(255, 255, 255, 0.1),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.1);
  transform: perspective(500rpx) rotateX(3deg);
  transition: all 0.3s ease;
}

.complete-actions .back-btn:active {
  transform: perspective(500rpx) rotateX(6deg) translateY(1rpx);
  box-shadow:
    inset 0 2rpx 4rpx rgba(0, 0, 0, 0.2),
    inset 0 -1rpx 0 rgba(255, 255, 255, 0.05);
}
