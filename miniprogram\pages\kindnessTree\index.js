// 善意成长树页面
Page({
  data: {
    // 基础数据
    kindnessCount: 0,
    totalPoints: 0,
    badgeCount: 0,
    streakDays: 0,
    
    // 成长树等级
    treeLevel: {
      level: 1,
      name: '善意萌芽',
      icon: '🌱',
      crownClass: 'crown-sprout',
      crownIcon: '🌱',
      next: 20,
      nextName: '善意花朵'
    },
    
    // 树的大小（基于等级）
    treeSize: 1,
    
    // 等级进度
    levelProgress: 0,
    
    // 选中的标签页
    selectedTab: 'stats',
    
    // 成长树装饰
    treeDecorations: [],
    
    // 成就徽章
    achievementBadges: [],
    
    // 成长环
    growthRings: [
      { level: 1, position: 20, label: '萌芽' },
      { level: 2, position: 40, label: '花朵' },
      { level: 3, position: 60, label: '大树' },
      { level: 4, position: 80, label: '星树' }
    ],
    
    // 土壤营养
    soilNutrients: [
      { type: 'love', icon: '❤️' },
      { type: 'care', icon: '🤗' },
      { type: 'kindness', icon: '💝' },
      { type: 'hope', icon: '🌟' }
    ],
    
    // 装饰分类
    decorationCategories: [
      { id: 'flowers', name: '花朵', icon: '🌺' },
      { id: 'animals', name: '动物', icon: '🦋' },
      { id: 'nature', name: '自然', icon: '🌈' },
      { id: 'special', name: '特殊', icon: '✨' }
    ],
    
    // 当前选中的装饰分类
    selectedDecoCategory: 'flowers',
    
    // 装饰物品
    decorationItems: {
      flowers: [
        { id: 'flower_001', name: '樱花', description: '美丽的粉色樱花', icon: '🌸', cost: 10, owned: false },
        { id: 'flower_002', name: '向日葵', description: '向着阳光的向日葵', icon: '🌻', cost: 15, owned: false },
        { id: 'flower_003', name: '玫瑰', description: '象征爱心的红玫瑰', icon: '🌹', cost: 20, owned: false },
        { id: 'flower_004', name: '郁金香', description: '优雅的郁金香', icon: '🌷', cost: 18, owned: false }
      ],
      animals: [
        { id: 'animal_001', name: '蝴蝶', description: '翩翩起舞的蝴蝶', icon: '🦋', cost: 25, owned: false },
        { id: 'animal_002', name: '小鸟', description: '歌唱的小鸟', icon: '🐦', cost: 30, owned: false },
        { id: 'animal_003', name: '蜜蜂', description: '勤劳的小蜜蜂', icon: '🐝', cost: 22, owned: false },
        { id: 'animal_004', name: '瓢虫', description: '可爱的瓢虫', icon: '🐞', cost: 20, owned: false }
      ],
      nature: [
        { id: 'nature_001', name: '彩虹', description: '七彩的彩虹', icon: '🌈', cost: 35, owned: false },
        { id: 'nature_002', name: '云朵', description: '白色的云朵', icon: '☁️', cost: 15, owned: false },
        { id: 'nature_003', name: '太阳', description: '温暖的太阳', icon: '☀️', cost: 40, owned: false },
        { id: 'nature_004', name: '月亮', description: '皎洁的月亮', icon: '🌙', cost: 30, owned: false }
      ],
      special: [
        { id: 'special_001', name: '星星', description: '闪亮的星星', icon: '⭐', cost: 50, owned: false },
        { id: 'special_002', name: '魔法', description: '神奇的魔法效果', icon: '✨', cost: 60, owned: false },
        { id: 'special_003', name: '爱心', description: '温暖的爱心', icon: '💖', cost: 45, owned: false },
        { id: 'special_004', name: '皇冠', description: '荣耀的皇冠', icon: '👑', cost: 100, owned: false }
      ]
    },
    
    // 当前显示的装饰物品
    currentDecorations: [],
    
    // 所有成就
    allAchievements: [
      {
        id: 'ach_001',
        name: '善意新手',
        description: '完成第一个善意行为',
        icon: '🌱',
        target: 1,
        current: 0,
        unlocked: false,
        progressPercentage: 0,
        unlockedDate: ''
      },
      {
        id: 'ach_002',
        name: '善意达人',
        description: '完成10个善意行为',
        icon: '🌸',
        target: 10,
        current: 0,
        unlocked: false,
        progressPercentage: 0,
        unlockedDate: ''
      },
      {
        id: 'ach_003',
        name: '善意大师',
        description: '完成50个善意行为',
        icon: '🌳',
        target: 50,
        current: 0,
        unlocked: false,
        progressPercentage: 0,
        unlockedDate: ''
      },
      {
        id: 'ach_004',
        name: '善意传说',
        description: '完成100个善意行为',
        icon: '🌟',
        target: 100,
        current: 0,
        unlocked: false,
        progressPercentage: 0,
        unlockedDate: ''
      },
      {
        id: 'ach_005',
        name: '连续善意',
        description: '连续7天完成善意行为',
        icon: '🔥',
        target: 7,
        current: 0,
        unlocked: false,
        progressPercentage: 0,
        unlockedDate: ''
      }
    ],
    
    // 成长里程碑
    milestones: [
      {
        id: 'milestone_001',
        title: '种下种子',
        description: '开始你的善意之旅',
        icon: '🌱',
        achieved: true,
        reward: '善意萌芽称号'
      },
      {
        id: 'milestone_002',
        title: '初次开花',
        description: '完成20个善意行为',
        icon: '🌸',
        achieved: false,
        reward: '善意花朵称号'
      },
      {
        id: 'milestone_003',
        title: '茁壮成长',
        description: '完成50个善意行为',
        icon: '🌳',
        achieved: false,
        reward: '善意大树称号'
      },
      {
        id: 'milestone_004',
        title: '闪耀之星',
        description: '完成100个善意行为',
        icon: '🌟',
        achieved: false,
        reward: '善意之星称号'
      }
    ],
    
    // 页面状态
    loading: false,
    showDecorationSuccess: false,
    decorationSuccessMessage: ''
  },

  onLoad: function (options) {
    console.log('善意成长树加载');
    this.initializeGrowthTree();
  },

  onReady: function () {
    console.log('善意成长树渲染完成');
  },

  onShow: function () {
    // 每次显示时刷新数据
    this.loadTreeData();
  },

  onPullDownRefresh: function () {
    this.refreshTreeData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  // 初始化成长树
  initializeGrowthTree() {
    this.setData({ loading: true });

    try {
      // 加载成长树数据
      this.loadTreeData();
      
      // 加载装饰数据
      this.loadDecorationData();
      
      // 加载成就数据
      this.loadAchievementData();
      
      // 加载里程碑数据
      this.loadMilestoneData();
      
      // 计算成长树等级
      this.calculateTreeLevel();
      
    } catch (error) {
      console.error('初始化成长树失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载成长树数据
  loadTreeData() {
    const kindnessCount = wx.getStorageSync('kindnessCount') || 0;
    const totalPoints = wx.getStorageSync('kindnessPoints') || 0;
    const badgeCount = this.getBadgeCount();
    const streakDays = this.getStreakDays();
    
    this.setData({
      kindnessCount,
      totalPoints,
      badgeCount,
      streakDays
    });
  },

  // 获取徽章数量
  getBadgeCount() {
    const badges = wx.getStorageSync('kindnessBadges') || [];
    return badges.length;
  },

  // 获取连续天数
  getStreakDays() {
    const streakData = wx.getStorageSync('kindnessStreak') || { count: 0 };
    return streakData.count;
  },

  // 计算成长树等级
  calculateTreeLevel() {
    const kindnessCount = this.data.kindnessCount;
    let treeLevel, treeSize, levelProgress;
    
    if (kindnessCount >= 100) {
      treeLevel = {
        level: 4,
        name: '善意之星',
        icon: '🌟',
        crownClass: 'crown-star',
        crownIcon: '🌟',
        next: null,
        nextName: null
      };
      treeSize = 1.5;
      levelProgress = 100;
    } else if (kindnessCount >= 50) {
      treeLevel = {
        level: 3,
        name: '善意大树',
        icon: '🌳',
        crownClass: 'crown-tree',
        crownIcon: '🌳',
        next: 100,
        nextName: '善意之星'
      };
      treeSize = 1.3;
      levelProgress = ((kindnessCount - 50) / 50) * 100;
    } else if (kindnessCount >= 20) {
      treeLevel = {
        level: 2,
        name: '善意花朵',
        icon: '🌸',
        crownClass: 'crown-flower',
        crownIcon: '🌸',
        next: 50,
        nextName: '善意大树'
      };
      treeSize = 1.1;
      levelProgress = ((kindnessCount - 20) / 30) * 100;
    } else {
      treeLevel = {
        level: 1,
        name: '善意萌芽',
        icon: '🌱',
        crownClass: 'crown-sprout',
        crownIcon: '🌱',
        next: 20,
        nextName: '善意花朵'
      };
      treeSize = 1;
      levelProgress = (kindnessCount / 20) * 100;
    }
    
    this.setData({
      treeLevel,
      treeSize,
      levelProgress
    });
  },

  // 加载装饰数据
  loadDecorationData() {
    const ownedDecorations = wx.getStorageSync('ownedTreeDecorations') || [];
    const decorationItems = this.data.decorationItems;
    
    // 更新拥有状态
    Object.keys(decorationItems).forEach(category => {
      decorationItems[category].forEach(item => {
        item.owned = ownedDecorations.includes(item.id);
      });
    });
    
    // 生成树上的装饰
    const treeDecorations = this.generateTreeDecorations(ownedDecorations);
    
    this.setData({
      decorationItems,
      currentDecorations: decorationItems[this.data.selectedDecoCategory],
      treeDecorations
    });
  },

  // 生成树上的装饰
  generateTreeDecorations(ownedDecorations) {
    const decorations = [];
    const allItems = Object.values(this.data.decorationItems).flat();
    
    ownedDecorations.forEach((decorationId, index) => {
      const item = allItems.find(i => i.id === decorationId);
      if (item) {
        // 随机位置，但要在树冠范围内
        const angle = (index * 60) % 360; // 分散排列
        const radius = 30 + (index % 3) * 15; // 不同半径
        const x = 50 + Math.cos(angle * Math.PI / 180) * radius;
        const y = 50 + Math.sin(angle * Math.PI / 180) * radius;
        
        decorations.push({
          id: item.id,
          icon: item.icon,
          type: item.id.split('_')[0],
          top: Math.max(10, Math.min(90, y)),
          left: Math.max(10, Math.min(90, x))
        });
      }
    });
    
    return decorations;
  },

  // 加载成就数据
  loadAchievementData() {
    const unlockedAchievements = wx.getStorageSync('unlockedAchievements') || [];
    const achievements = this.data.allAchievements.map(achievement => {
      const unlocked = unlockedAchievements.includes(achievement.id);
      let current = 0;
      
      // 根据成就类型计算当前进度
      if (achievement.id.includes('善意')) {
        current = this.data.kindnessCount;
      } else if (achievement.id.includes('连续')) {
        current = this.data.streakDays;
      }
      
      const progressPercentage = Math.min((current / achievement.target) * 100, 100);
      
      return {
        ...achievement,
        current,
        unlocked,
        progressPercentage,
        unlockedDate: unlocked ? this.getAchievementDate(achievement.id) : ''
      };
    });
    
    // 生成成就徽章
    const achievementBadges = this.generateAchievementBadges(achievements.filter(a => a.unlocked));
    
    this.setData({
      allAchievements: achievements,
      achievementBadges
    });
  },

  // 生成成就徽章
  generateAchievementBadges(unlockedAchievements) {
    return unlockedAchievements.map((achievement, index) => {
      const angle = (index * 90) % 360;
      const radius = 40;
      const x = 50 + Math.cos(angle * Math.PI / 180) * radius;
      const y = 50 + Math.sin(angle * Math.PI / 180) * radius;
      
      return {
        id: achievement.id,
        icon: achievement.icon,
        top: Math.max(5, Math.min(95, y)),
        left: Math.max(5, Math.min(95, x))
      };
    });
  },

  // 获取成就解锁日期
  getAchievementDate(achievementId) {
    const achievementDates = wx.getStorageSync('achievementDates') || {};
    return achievementDates[achievementId] || '未知日期';
  },

  // 加载里程碑数据
  loadMilestoneData() {
    const milestones = this.data.milestones.map(milestone => {
      let achieved = false;
      
      if (milestone.id === 'milestone_001') {
        achieved = this.data.kindnessCount >= 1;
      } else if (milestone.id === 'milestone_002') {
        achieved = this.data.kindnessCount >= 20;
      } else if (milestone.id === 'milestone_003') {
        achieved = this.data.kindnessCount >= 50;
      } else if (milestone.id === 'milestone_004') {
        achieved = this.data.kindnessCount >= 100;
      }
      
      return {
        ...milestone,
        achieved
      };
    });
    
    this.setData({
      milestones
    });
  },

  // 选择标签页
  onSelectTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      selectedTab: tab
    });
  },

  // 选择装饰分类
  onSelectDecoCategory(e) {
    const categoryId = e.currentTarget.dataset.category;
    this.setData({
      selectedDecoCategory: categoryId,
      currentDecorations: this.data.decorationItems[categoryId]
    });
  },

  // 选择装饰
  onSelectDecoration(e) {
    const decoration = e.currentTarget.dataset.decoration;
    
    if (decoration.owned) {
      wx.showToast({
        title: '已拥有该装饰',
        icon: 'none'
      });
      return;
    }
    
    if (this.data.totalPoints < decoration.cost) {
      wx.showModal({
        title: '积分不足',
        content: `添加${decoration.name}需要${decoration.cost}个积分，你当前只有${this.data.totalPoints}个积分。`,
        showCancel: false
      });
      return;
    }

    wx.showModal({
      title: '添加装饰',
      content: `确定要花费${decoration.cost}个积分为成长树添加${decoration.name}吗？\n\n${decoration.description}`,
      confirmText: '确认添加',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.addDecoration(decoration);
        }
      }
    });
  },

  // 添加装饰
  addDecoration(decoration) {
    const currentPoints = wx.getStorageSync('kindnessPoints') || 0;
    const ownedDecorations = wx.getStorageSync('ownedTreeDecorations') || [];
    
    // 扣除积分
    const newPoints = currentPoints - decoration.cost;
    wx.setStorageSync('kindnessPoints', newPoints);
    
    // 添加到拥有装饰
    ownedDecorations.push(decoration.id);
    wx.setStorageSync('ownedTreeDecorations', ownedDecorations);
    
    // 更新界面
    this.setData({
      totalPoints: newPoints
    });
    
    this.loadDecorationData();
    
    // 显示成功动画
    this.showDecorationSuccessAnimation(`成功添加${decoration.name}！`);
  },

  // 显示装饰添加成功动画
  showDecorationSuccessAnimation(message) {
    this.setData({
      showDecorationSuccess: true,
      decorationSuccessMessage: message
    });
    
    setTimeout(() => {
      this.setData({
        showDecorationSuccess: false
      });
    }, 2500);
  },

  // 点击装饰
  onTapDecoration(e) {
    const decoration = e.currentTarget.dataset.decoration;
    wx.showToast({
      title: `这是${decoration.icon}`,
      icon: 'none'
    });
  },

  // 点击徽章
  onTapBadge(e) {
    const badge = e.currentTarget.dataset.badge;
    wx.showToast({
      title: `成就徽章：${badge.icon}`,
      icon: 'none'
    });
  },

  // 查看成就
  onViewAchievement(e) {
    const achievement = e.currentTarget.dataset.achievement;
    
    let content = `${achievement.description}\n\n`;
    if (achievement.unlocked) {
      content += `解锁时间：${achievement.unlockedDate}`;
    } else {
      content += `进度：${achievement.current}/${achievement.target}`;
    }
    
    wx.showModal({
      title: `${achievement.icon} ${achievement.name}`,
      content: content,
      showCancel: false
    });
  },

  // 浇水
  onWaterTree() {
    wx.showToast({
      title: '为成长树浇水！💧',
      icon: 'none'
    });
    
    // 可以添加浇水动画效果
    this.triggerWaterAnimation();
  },

  // 施肥
  onFertilizeTree() {
    wx.showToast({
      title: '为成长树施肥！🌱',
      icon: 'none'
    });
    
    // 可以添加施肥动画效果
    this.triggerFertilizeAnimation();
  },

  // 分享成长树
  onShareTree() {
    wx.showModal({
      title: '分享成长树',
      content: `我的善意成长树已经是${this.data.treeLevel.name}了！\n\n完成了${this.data.kindnessCount}个善意行为，获得了${this.data.badgeCount}个徽章！`,
      confirmText: '分享给朋友',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 实现分享功能
          wx.showToast({
            title: '分享功能开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  // 重置视图
  onResetView() {
    this.setData({
      selectedTab: 'stats'
    });
    
    wx.showToast({
      title: '视图已重置',
      icon: 'success'
    });
  },

  // 触发浇水动画
  triggerWaterAnimation() {
    // 这里可以添加浇水的视觉效果
    console.log('浇水动画');
  },

  // 触发施肥动画
  triggerFertilizeAnimation() {
    // 这里可以添加施肥的视觉效果
    console.log('施肥动画');
  },

  // 刷新数据
  refreshTreeData() {
    this.setData({ loading: true });
    
    setTimeout(() => {
      this.loadTreeData();
      this.loadDecorationData();
      this.loadAchievementData();
      this.loadMilestoneData();
      this.calculateTreeLevel();
      this.setData({ loading: false });
    }, 1000);
  }
});
