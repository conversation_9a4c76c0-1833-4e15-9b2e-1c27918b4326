// 《能量星球》善意行为数据库

/**
 * 善意行为分类和详细数据
 */
const kindnessCategories = {
  family: {
    id: 'family',
    name: '家庭善意',
    icon: '🏠',
    color: '#FF8A65',
    description: '在家庭中表现善意和关爱'
  },
  school: {
    id: 'school', 
    name: '学校善意',
    icon: '🏫',
    color: '#42A5F5',
    description: '在学校里帮助同学和老师'
  },
  community: {
    id: 'community',
    name: '社区善意', 
    icon: '🏘️',
    color: '#66BB6A',
    description: '在社区中帮助邻居和他人'
  },
  environment: {
    id: 'environment',
    name: '环保善意',
    icon: '🌱',
    color: '#4CAF50',
    description: '保护环境，爱护地球'
  },
  animal: {
    id: 'animal',
    name: '动物关爱',
    icon: '🐾',
    color: '#FFA726',
    description: '关爱和保护小动物'
  }
};

/**
 * 详细的善意行为数据
 */
const kindnessActions = [
  // 家庭善意
  {
    id: 'family_001',
    category: 'family',
    title: '帮妈妈做家务',
    subtitle: '主动承担家庭责任',
    icon: '🧹',
    difficulty: 1, // 1-简单 2-中等 3-困难
    ageRange: '6-12岁',
    points: 5,
    description: '主动帮助妈妈做一些力所能及的家务活，比如扫地、擦桌子、整理房间等。',
    steps: [
      '观察家里需要整理的地方',
      '主动询问妈妈需要什么帮助',
      '认真完成分配的家务任务',
      '完成后整理好工具'
    ],
    safety: [
      '使用清洁用品时要小心，避免接触眼睛',
      '搬重物时要量力而行，请大人帮忙',
      '使用电器前要征得大人同意'
    ],
    tips: [
      '可以和家人一起做，增进感情',
      '做完后可以和妈妈分享完成的喜悦',
      '养成定期帮忙的好习惯'
    ],
    rewards: {
      points: 5,
      badge: '家务小帮手',
      experience: '学会承担责任，体验劳动的快乐'
    }
  },
  {
    id: 'family_002',
    category: 'family',
    title: '照顾弟弟妹妹',
    subtitle: '做一个好哥哥/好姐姐',
    icon: '👶',
    difficulty: 2,
    ageRange: '8-12岁',
    points: 8,
    description: '主动照顾年幼的弟弟妹妹，陪他们玩耍，帮助他们学习。',
    steps: [
      '观察弟弟妹妹的需要',
      '陪他们一起玩游戏或读书',
      '帮助他们解决小困难',
      '保护他们的安全'
    ],
    safety: [
      '确保游戏环境安全',
      '遇到紧急情况立即找大人',
      '不要让小朋友接触危险物品'
    ],
    tips: [
      '耐心是最重要的品质',
      '可以教他们一些简单的知识',
      '一起做手工或画画很有趣'
    ],
    rewards: {
      points: 8,
      badge: '贴心哥哥/姐姐',
      experience: '培养责任感和爱心'
    }
  },
  {
    id: 'family_003',
    category: 'family',
    title: '给爷爷奶奶捶背',
    subtitle: '孝敬长辈，表达关爱',
    icon: '👴',
    difficulty: 1,
    ageRange: '5-12岁',
    points: 6,
    description: '为爷爷奶奶捶背按摩，陪他们聊天，让他们感受到关爱。',
    steps: [
      '询问爷爷奶奶是否需要按摩',
      '轻柔地为他们捶背',
      '陪他们聊天，听他们讲故事',
      '帮他们做一些小事情'
    ],
    safety: [
      '动作要轻柔，不要用力过猛',
      '注意老人的身体状况',
      '如果老人感到不适要立即停止'
    ],
    tips: [
      '可以学一些简单的按摩手法',
      '多听老人讲过去的故事',
      '定期陪伴是最好的孝心'
    ],
    rewards: {
      points: 6,
      badge: '孝心宝贝',
      experience: '传承孝道，学会感恩'
    }
  },

  // 学校善意
  {
    id: 'school_001',
    category: 'school',
    title: '帮助同学学习',
    subtitle: '分享知识，共同进步',
    icon: '📚',
    difficulty: 2,
    ageRange: '7-12岁',
    points: 7,
    description: '主动帮助学习有困难的同学，分享学习方法和知识。',
    steps: [
      '发现需要帮助的同学',
      '主动询问是否需要帮助',
      '耐心解释和教导',
      '鼓励同学继续努力'
    ],
    safety: [
      '在老师允许的情况下进行',
      '不要在上课时间影响课堂',
      '保持友善和耐心的态度'
    ],
    tips: [
      '用简单易懂的方式解释',
      '可以一起做练习题',
      '互相学习，共同进步'
    ],
    rewards: {
      points: 7,
      badge: '学习小老师',
      experience: '提升表达能力，巩固知识'
    }
  },
  {
    id: 'school_002',
    category: 'school',
    title: '借文具给同学',
    subtitle: '乐于分享，互相帮助',
    icon: '✏️',
    difficulty: 1,
    ageRange: '6-12岁',
    points: 3,
    description: '当同学忘记带文具时，主动借给他们使用。',
    steps: [
      '注意到同学需要文具',
      '主动提供帮助',
      '借出自己的文具',
      '提醒同学用完后归还'
    ],
    safety: [
      '确保自己也有足够的文具使用',
      '借出前检查文具是否安全',
      '记住借出的物品'
    ],
    tips: [
      '可以准备一些备用文具',
      '教同学如何正确使用',
      '培养分享的好习惯'
    ],
    rewards: {
      points: 3,
      badge: '分享小天使',
      experience: '学会分享，收获友谊'
    }
  },

  // 社区善意
  {
    id: 'community_001',
    category: 'community',
    title: '帮助老人过马路',
    subtitle: '关爱老人，注意安全',
    icon: '🚦',
    difficulty: 2,
    ageRange: '8-12岁',
    points: 10,
    description: '看到需要帮助的老人过马路时，在安全的前提下提供帮助。',
    steps: [
      '观察老人是否需要帮助',
      '礼貌地询问是否需要协助',
      '搀扶老人安全过马路',
      '确保老人安全到达对面'
    ],
    safety: [
      '必须在大人陪同下进行',
      '确保交通信号灯是绿灯',
      '注意来往车辆，确保安全',
      '如果情况复杂，请寻求大人帮助'
    ],
    tips: [
      '要有耐心，配合老人的步伐',
      '可以和老人简单聊天',
      '这是很有意义的善意行为'
    ],
    rewards: {
      points: 10,
      badge: '社区小雷锋',
      experience: '体验助人为乐的快乐'
    }
  },
  {
    id: 'community_002',
    category: 'community',
    title: '捡拾路边垃圾',
    subtitle: '保护环境，美化社区',
    icon: '🗑️',
    difficulty: 1,
    ageRange: '5-12岁',
    points: 4,
    description: '看到路边的垃圾主动捡起来，扔到垃圾桶里。',
    steps: [
      '发现路边的垃圾',
      '判断是否安全捡拾',
      '将垃圾捡起来',
      '扔到合适的垃圾桶里'
    ],
    safety: [
      '不要捡拾危险物品（玻璃、尖锐物等）',
      '建议戴手套或使用工具',
      '捡完后要洗手',
      '遇到不明物品要告诉大人'
    ],
    tips: [
      '可以准备小垃圾袋',
      '和家人一起进行更安全',
      '养成不乱扔垃圾的好习惯'
    ],
    rewards: {
      points: 4,
      badge: '环保小卫士',
      experience: '为社区环境贡献力量'
    }
  },

  // 环保善意
  {
    id: 'environment_001',
    category: 'environment',
    title: '节约用水用电',
    subtitle: '保护资源，从我做起',
    icon: '💧',
    difficulty: 1,
    ageRange: '5-12岁',
    points: 3,
    description: '在日常生活中注意节约用水用电，保护地球资源。',
    steps: [
      '用完水后及时关闭水龙头',
      '离开房间时关闭电灯',
      '不需要时关闭电器',
      '提醒家人一起节约'
    ],
    safety: [
      '操作电器开关时要小心',
      '湿手不要碰电器开关',
      '不明白的电器要问大人'
    ],
    tips: [
      '可以制作节约提醒标签',
      '记录每天的节约行为',
      '和家人比赛谁更节约'
    ],
    rewards: {
      points: 3,
      badge: '节约小能手',
      experience: '培养环保意识'
    }
  },

  // 动物关爱
  {
    id: 'animal_001',
    category: 'animal',
    title: '给小动物喂食',
    subtitle: '关爱生命，传递温暖',
    icon: '🐱',
    difficulty: 2,
    ageRange: '6-12岁',
    points: 6,
    description: '为流浪的小猫小狗或小鸟准备适合的食物和水。',
    steps: [
      '准备适合的动物食物',
      '找到安全的喂食地点',
      '轻柔地放置食物和水',
      '保持安全距离观察'
    ],
    safety: [
      '必须在大人陪同下进行',
      '不要直接用手喂食',
      '保持安全距离，避免被抓伤',
      '不要喂食有害的食物'
    ],
    tips: [
      '了解不同动物适合的食物',
      '定期喂食比一次大量更好',
      '可以为小动物准备小窝'
    ],
    rewards: {
      points: 6,
      badge: '动物小天使',
      experience: '学会关爱生命'
    }
  }
];

/**
 * 获取所有分类
 */
function getAllCategories() {
  return Object.values(kindnessCategories);
}

/**
 * 根据分类获取行为
 */
function getActionsByCategory(categoryId) {
  return kindnessActions.filter(action => action.category === categoryId);
}

/**
 * 根据ID获取行为详情
 */
function getActionById(actionId) {
  return kindnessActions.find(action => action.id === actionId);
}

/**
 * 搜索行为
 */
function searchActions(keyword) {
  return kindnessActions.filter(action => 
    action.title.includes(keyword) || 
    action.description.includes(keyword)
  );
}

/**
 * 根据难度筛选行为
 */
function getActionsByDifficulty(difficulty) {
  return kindnessActions.filter(action => action.difficulty === difficulty);
}

module.exports = {
  kindnessCategories,
  kindnessActions,
  getAllCategories,
  getActionsByCategory,
  getActionById,
  searchActions,
  getActionsByDifficulty
};
