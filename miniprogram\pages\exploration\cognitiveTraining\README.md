# 认知训练中心

## 功能概述

认知训练中心是能量星球小程序中的一个综合性认知能力训练模块，旨在通过游戏化的方式提升小学生的各项认知能力。

## 模块结构

```
cognitiveTraining/
├── index.js          # 训练中心主页面逻辑
├── index.json        # 页面配置
├── index.wxml        # 训练中心主页面结构
├── index.wxss        # 训练中心主页面样式
├── game/             # 游戏引擎目录
│   ├── index.js      # 游戏引擎逻辑
│   ├── index.json    # 游戏页面配置
│   ├── index.wxml    # 游戏页面结构
│   ├── index.wxss    # 游戏页面样式
│   └── questionData.js # 题目数据库
└── README.md         # 说明文档
```

## 训练类型

### 1. 语言词汇训练 📚
- **主题色**: 橙色 (#FF9800)
- **训练内容**: 词汇理解、造句、同义词反义词、句型分析
- **时间限制**: 30秒/题
- **题目数量**: 10道

### 2. 专注力训练 🎯
- **主题色**: 蓝色 (#2196F3)
- **训练内容**: 找不同、记忆序列、规律发现、注意力测试
- **时间限制**: 20秒/题
- **题目数量**: 10道

### 3. 视觉训练 👁️
- **主题色**: 绿色 (#4CAF50)
- **训练内容**: 图形识别、空间关系、对称性、立体思维
- **时间限制**: 25秒/题
- **题目数量**: 10道

### 4. 听觉训练 🎵
- **主题色**: 紫色 (#9C27B0)
- **训练内容**: 声音识别、音调感知、韵律分析、语音理解
- **时间限制**: 35秒/题
- **题目数量**: 10道

## 功能特性

### 游戏化设计
- 计时挑战增加紧迫感
- 即时反馈提供学习指导
- 进度跟踪激励持续学习
- 能量奖励系统增强动机

### 自适应难度
- 题目按难度递进排列
- 根据完成情况解锁新内容
- 个性化学习路径

### 数据统计
- 实时显示完成进度
- 准确率统计分析
- 学习成果可视化

## 技术实现

### 数据存储
```javascript
// 进度数据结构
{
  "language": { completed: 8, unlocked: true },
  "attention": { completed: 5, unlocked: true },
  "visual": { completed: 0, unlocked: false },
  "auditory": { completed: 0, unlocked: false }
}
```

### 题目格式
```javascript
{
  id: 'lang_001',
  type: 'multiple_choice', // 或 'text_input'
  question: '题目内容',
  options: ['选项A', '选项B', '选项C', '选项D'], // 选择题
  correctAnswer: 2, // 或字符串数组
  explanation: '解释说明',
  feedback: {
    correct: '正确反馈',
    wrong: '错误反馈'
  }
}
```

### 奖励机制
- **智慧能量**: 每答对1题获得2点
- **爱心能量**: 每答对2题获得1点
- **解锁条件**: 完成一定数量题目后解锁新训练类型

## 页面导航

1. **探索星球主页** → 点击"认知训练"卡片
2. **训练中心主页** → 选择训练类型
3. **游戏页面** → 开始训练
4. **结果页面** → 查看成绩和奖励

## 设计理念

### 教育价值
- 基于认知科学理论设计
- 适合小学生认知发展水平
- 全面覆盖核心认知能力

### 用户体验
- 直观的操作界面
- 清晰的视觉反馈
- 流畅的交互动画
- 一致的设计语言

### 技术架构
- 模块化设计便于扩展
- 统一的游戏引擎支持多种题型
- 灵活的数据结构适应不同需求

## 未来扩展

- 增加更多训练类型
- 实现多人竞技模式
- 添加AI智能推荐
- 集成语音识别功能
- 支持自定义题目

## 维护说明

### 添加新题目
1. 在 `questionData.js` 中对应数组添加题目对象
2. 确保题目格式符合规范
3. 测试题目显示和逻辑正确性

### 修改训练配置
1. 在训练中心主页面 `data.trainingTypes` 中修改配置
2. 在游戏页面 `data.trainingConfig` 中同步更新
3. 确保颜色主题和图标一致

### 调整难度设置
1. 修改各训练类型的时间限制
2. 调整解锁条件和奖励机制
3. 重新平衡游戏体验
