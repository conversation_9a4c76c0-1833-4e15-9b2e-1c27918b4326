// pages/exploration/index.js
const explorationSystem = require('../../utils/explorationSystem.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 探索者数据
    explorerData: {
      level: 1,
      title: '初级探索者',
      totalGames: 0
    },

    // 能量数据
    energyData: {
      wisdom: 0,
      love: 0
    },

    // 星球状态数据
    planetStates: {
      auditory: { status: 'unlocked' },
      visual: { status: 'unlocked' },
      logic: { status: 'locked' },
      language: { status: 'locked' },
      creative: { status: 'locked' }
    },

    // 当前年龄组
    currentAge: '4-5',

    // 弹窗状态
    showPlanetModal: false,

    // 选中的星球
    selectedPlanet: {},

    // 游戏名称映射
    gameNames: {
      'story_comprehension': '故事理解',
      'instruction_following': '指令跟随',
      'music_rhythm': '音乐节拍',
      'sound_recognition': '声音识别',
      'shape_matching': '图形匹配',
      'color_sorting': '颜色分类',
      'spot_difference': '找不同',
      'sequence_memory': '序列记忆',
      'simple_reasoning': '简单推理',
      'classification': '分类整理',
      'cause_effect': '因果关系',
      'pattern_recognition': '模式识别',
      'vocabulary_learning': '词汇学习',
      'sentence_building': '句子组合',
      'story_creation': '故事创作',
      'word_association': '词汇联想',
      'ar_exploration': 'AR探索',
      'space_building': '建造游戏',
      'character_design': '角色设计',
      'story_maker': '故事创作'
    },

    // 加载状态
    isLoading: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('探索星球页面加载');
    this.initializeExploration();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('探索星球页面渲染完成');
    // 启动星空动画
    this.startStarfieldAnimation();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('探索星球页面显示');
    // 静默刷新数据
    this.silentRefreshData();
  },

  /**
   * 初始化探索系统
   */
  initializeExploration: function() {
    try {
      wx.showLoading({
        title: '正在加载星图...'
      });

      // 初始化探索系统
      const explorationData = explorationSystem.initialize();

      // 获取能量数据
      const energyData = wx.getStorageSync('energyData') || { wisdom: 0, love: 0 };

      // 获取可用星球
      const availablePlanets = explorationSystem.getAvailablePlanets();

      // 更新星球状态
      const planetStates = {};
      availablePlanets.forEach(planet => {
        planetStates[planet.id] = {
          status: planet.status,
          ...planet
        };
      });

      // 更新探索者数据
      const explorerData = {
        level: explorationData.currentLevel,
        title: this.getExplorerTitle(explorationData.currentLevel),
        totalGames: explorationData.totalGamesPlayed
      };

      this.setData({
        explorerData: explorerData,
        energyData: energyData,
        planetStates: planetStates,
        currentAge: explorationData.playerAge || '4-5',
        isLoading: false
      });

      wx.hideLoading();

    } catch (error) {
      console.error('初始化探索系统失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 静默刷新数据
   */
  silentRefreshData: function() {
    try {
      // 获取最新的能量数据
      const energyData = wx.getStorageSync('energyData') || { wisdom: 0, love: 0 };

      // 获取最新的探索数据
      const explorationData = explorationSystem.getExplorationData();

      // 更新探索者数据
      const explorerData = {
        level: explorationData.currentLevel,
        title: this.getExplorerTitle(explorationData.currentLevel),
        totalGames: explorationData.totalGamesPlayed
      };

      this.setData({
        explorerData: explorerData,
        energyData: energyData
      });

    } catch (error) {
      console.error('静默刷新数据失败:', error);
    }
  },

  /**
   * 获取探索者称号
   */
  getExplorerTitle: function(level) {
    if (level >= 5) return '传奇探索者';
    if (level >= 4) return '高级探索者';
    if (level >= 3) return '中级探索者';
    if (level >= 2) return '进阶探索者';
    return '初级探索者';
  },

  /**
   * 启动星空动画
   */
  startStarfieldAnimation: function() {
    // 这里可以添加星空背景的动画逻辑
    console.log('启动星空动画');
  },

  /**
   * 星球点击事件
   */
  onPlanetTap: function(e) {
    const planetId = e.currentTarget.dataset.planet;
    const planetState = this.data.planetStates[planetId];

    console.log('点击星球:', planetId, planetState);

    if (planetState.status === 'locked') {
      wx.showModal({
        title: '星球未解锁',
        content: `需要完成${planetState.unlockLevel * 5}个游戏才能解锁${planetState.name}`,
        showCancel: false,
        confirmText: '继续探索'
      });
      return;
    }

    // 获取星球详细信息
    const planetInfo = explorationSystem.planets[planetId];
    const selectedPlanet = {
      ...planetState,
      name: planetInfo.name,
      description: planetInfo.description,
      icon: planetInfo.icon,
      games: planetInfo.games || []
    };

    // 显示星球详情
    this.setData({
      selectedPlanet: selectedPlanet,
      showPlanetModal: true
    });
  },

  /**
   * 关闭星球弹窗
   */
  onClosePlanetModal: function() {
    this.setData({
      showPlanetModal: false,
      selectedPlanet: {}
    });
  },

  /**
   * 开始探索
   */
  onStartExploration: function() {
    const planet = this.data.selectedPlanet;
    if (!planet || planet.status === 'locked') {
      return;
    }

    console.log('开始探索星球:', planet.name);

    // 获取推荐游戏
    const recommendedGame = explorationSystem.getRecommendedGame();

    if (recommendedGame) {
      // 启动游戏
      this.startGame(recommendedGame.id);
    } else {
      wx.showToast({
        title: '暂无可用游戏',
        icon: 'none'
      });
    }
  },

  /**
   * 启动游戏
   */
  startGame: function(gameId) {
    console.log('启动游戏:', gameId);

    const result = explorationSystem.startGame(gameId);

    if (result.success) {
      // 导航到游戏引擎页面
      wx.navigateTo({
        url: `/pages/exploration/gameEngine/index?gameId=${gameId}`
      });
    } else {
      wx.showToast({
        title: result.error || '启动游戏失败',
        icon: 'none'
      });
    }
  },

  /**
   * 点击认知训练中心
   */
  onCognitiveTrainingTap: function() {
    console.log('进入认知训练中心');

    wx.navigateTo({
      url: '/pages/exploration/cognitiveTraining/index'
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation: function() {
    // 阻止事件冒泡
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('下拉刷新');
    this.initializeExploration();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log('上拉触底');
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '来《能量星球》一起探索宇宙的奥秘吧！',
      path: '/pages/exploration/index',
      imageUrl: '/images/share-exploration.png'
    };
  }
})