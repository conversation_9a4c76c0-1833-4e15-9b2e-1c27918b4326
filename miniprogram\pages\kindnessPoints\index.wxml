<!--善意积分站页面-->
<view class="points-station-container">
  
  <!-- 背景装饰 -->
  <view class="station-background">
    <view class="floating-heart heart-1">💖</view>
    <view class="floating-heart heart-2">💝</view>
    <view class="floating-heart heart-3">💗</view>
    <view class="floating-heart heart-4">💕</view>
    <view class="energy-wave wave-1"></view>
    <view class="energy-wave wave-2"></view>
    <view class="energy-wave wave-3"></view>
  </view>

  <!-- 页面标题 -->
  <view class="station-header">
    <view class="header-icon">💝</view>
    <view class="header-title">善意积分站</view>
    <view class="header-subtitle">转化爱心，收获善意</view>
  </view>

  <!-- 能量和积分显示 -->
  <view class="balance-section">
    <view class="balance-card energy-card">
      <view class="balance-icon">❤️</view>
      <view class="balance-amount">{{currentLoveEnergy}}</view>
      <view class="balance-label">爱心能量</view>
      <view class="balance-glow energy-glow"></view>
    </view>
    
    <view class="exchange-arrow">
      <view class="arrow-icon">⇄</view>
      <view class="arrow-text">兑换</view>
    </view>
    
    <view class="balance-card points-card">
      <view class="balance-icon">⭐</view>
      <view class="balance-amount">{{currentKindnessPoints}}</view>
      <view class="balance-label">善意积分</view>
      <view class="balance-glow points-glow"></view>
    </view>
  </view>

  <!-- 快速兑换 -->
  <view class="quick-exchange-section">
    <view class="section-title">
      <view class="title-icon">⚡</view>
      <view class="title-text">快速兑换</view>
    </view>
    
    <view class="exchange-options">
      <view class="exchange-option" 
            wx:for="{{exchangeOptions}}" 
            wx:key="id"
            bindtap="onQuickExchange" 
            data-option="{{item}}">
        <view class="option-energy">{{item.energy}}❤️</view>
        <view class="option-arrow">→</view>
        <view class="option-points">{{item.points}}⭐</view>
        <view class="option-rate">{{item.rate}}</view>
        <view class="option-glow"></view>
      </view>
    </view>
  </view>

  <!-- 积分商店 -->
  <view class="points-shop-section">
    <view class="section-title">
      <view class="title-icon">🛍️</view>
      <view class="title-text">积分商店</view>
    </view>
    
    <view class="shop-categories">
      <view class="shop-category {{selectedShopCategory === item.id ? 'active' : ''}}"
            wx:for="{{shopCategories}}" 
            wx:key="id"
            bindtap="onSelectShopCategory" 
            data-category="{{item.id}}">
        <view class="category-icon">{{item.icon}}</view>
        <view class="category-name">{{item.name}}</view>
      </view>
    </view>
    
    <view class="shop-items">
      <view class="shop-item" 
            wx:for="{{currentShopItems}}" 
            wx:key="id"
            bindtap="onPurchaseItem" 
            data-item="{{item}}">
        <view class="item-icon">{{item.icon}}</view>
        <view class="item-info">
          <view class="item-name">{{item.name}}</view>
          <view class="item-description">{{item.description}}</view>
          <view class="item-price">{{item.price}}⭐</view>
        </view>
        <view class="item-status">
          <view class="purchase-btn" wx:if="{{!item.owned}}">购买</view>
          <view class="owned-badge" wx:else>已拥有</view>
        </view>
        <view class="item-glow"></view>
      </view>
    </view>
  </view>

  <!-- 积分任务 -->
  <view class="points-tasks-section">
    <view class="section-title">
      <view class="title-icon">📋</view>
      <view class="title-text">积分任务</view>
    </view>
    
    <view class="tasks-list">
      <view class="task-item" 
            wx:for="{{pointsTasks}}" 
            wx:key="id"
            bindtap="onViewTask" 
            data-task="{{item}}">
        <view class="task-icon">{{item.icon}}</view>
        <view class="task-info">
          <view class="task-title">{{item.title}}</view>
          <view class="task-description">{{item.description}}</view>
          <view class="task-reward">奖励：{{item.reward}}⭐</view>
        </view>
        <view class="task-progress">
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{item.progressPercentage}}%;"></view>
          </view>
          <view class="progress-text">{{item.current}}/{{item.target}}</view>
        </view>
        <view class="task-status">
          <view class="claim-btn" wx:if="{{item.completed && !item.claimed}}">领取</view>
          <view class="completed-badge" wx:elif="{{item.claimed}}">已完成</view>
          <view class="progress-badge" wx:else>进行中</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 积分历史 -->
  <view class="points-history-section">
    <view class="section-title">
      <view class="title-icon">📊</view>
      <view class="title-text">积分记录</view>
      <view class="view-all-btn" bindtap="onViewAllHistory">查看全部</view>
    </view>
    
    <view class="history-list">
      <view class="history-item" 
            wx:for="{{recentHistory}}" 
            wx:key="id">
        <view class="history-icon">{{item.icon}}</view>
        <view class="history-info">
          <view class="history-title">{{item.title}}</view>
          <view class="history-time">{{item.time}}</view>
        </view>
        <view class="history-amount {{item.type}}">
          {{item.type === 'earn' ? '+' : '-'}}{{item.amount}}⭐
        </view>
      </view>
    </view>
    
    <view class="no-history" wx:if="{{recentHistory.length === 0}}">
      <view class="no-history-icon">📝</view>
      <view class="no-history-text">暂无积分记录</view>
      <view class="no-history-tip">完成善意行为或兑换积分后会显示记录</view>
    </view>
  </view>

  <!-- 积分统计 -->
  <view class="points-stats-section">
    <view class="section-title">
      <view class="title-icon">📈</view>
      <view class="title-text">积分统计</view>
    </view>
    
    <view class="stats-grid">
      <view class="stat-card">
        <view class="stat-icon">💰</view>
        <view class="stat-number">{{pointsStats.totalEarned}}</view>
        <view class="stat-label">累计获得</view>
      </view>
      <view class="stat-card">
        <view class="stat-icon">🛒</view>
        <view class="stat-number">{{pointsStats.totalSpent}}</view>
        <view class="stat-label">累计消费</view>
      </view>
      <view class="stat-card">
        <view class="stat-icon">🎯</view>
        <view class="stat-number">{{pointsStats.tasksCompleted}}</view>
        <view class="stat-label">完成任务</view>
      </view>
      <view class="stat-card">
        <view class="stat-icon">🏆</view>
        <view class="stat-number">{{pointsStats.itemsOwned}}</view>
        <view class="stat-label">拥有物品</view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-spinner">
      <view class="spinner-icon">💝</view>
      <view class="loading-text">加载中...</view>
    </view>
  </view>

  <!-- 兑换成功动画 -->
  <view class="exchange-success-overlay" wx:if="{{showExchangeSuccess}}">
    <view class="success-content">
      <view class="success-icon">🎉</view>
      <view class="success-message">{{exchangeSuccessMessage}}</view>
      <view class="success-hearts">
        <view class="success-heart heart-1">💖</view>
        <view class="success-heart heart-2">💝</view>
        <view class="success-heart heart-3">💗</view>
        <view class="success-heart heart-4">💕</view>
        <view class="success-heart heart-5">💞</view>
      </view>
    </view>
  </view>

</view>
