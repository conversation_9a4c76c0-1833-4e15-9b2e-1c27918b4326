<!--《能量星球》个人档案中心 - 船长形象定制与个人信息管理-->
<view class="page">
  <view class="profile-center">
    <!-- 深空背景 -->
    <view class="space-background">
      <view class="star-field">
        <view class="star star-1"></view>
        <view class="star star-2"></view>
        <view class="star star-3"></view>
        <view class="star star-4"></view>
        <view class="star star-5"></view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <scroll-view class="content-scroll" scroll-y="true">
    
    <!-- 船长形象编辑区域 -->
    <view class="captain-editor-section">
      <view class="section-header">
        <text class="section-title">🧑‍🚀 船长形象定制</text>
        <text class="section-subtitle">打造专属的星际船长形象</text>
      </view>
      
      <!-- 3D船长预览 -->
      <view class="captain-preview">
        <view class="preview-platform">
          <view class="hologram-avatar" bindtap="onEditAvatar">
            <text class="avatar-display">{{captainData.avatar}}</text>
            <view class="edit-indicator">
              <text class="edit-icon">✏️</text>
            </view>
          </view>
          
          <!-- 装备展示 -->
          <view class="equipment-display">
            <view class="equipment-slot helmet" bindtap="onEditHelmet">
              <text class="equipment-icon">{{captainData.helmet}}</text>
              <text class="equipment-label">头盔</text>
            </view>
            <view class="equipment-slot suit" bindtap="onEditSuit">
              <text class="equipment-icon">{{captainData.suit}}</text>
              <text class="equipment-label">宇航服</text>
            </view>
            <view class="equipment-slot accessory" bindtap="onEditAccessory">
              <text class="equipment-icon">{{captainData.accessory}}</text>
              <text class="equipment-label">配饰</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 个人信息区域 -->
    <view class="personal-info-section">
      <view class="section-header">
        <text class="section-title">📊 成长数据</text>
        <text class="section-subtitle">记录你的星际探索历程</text>
      </view>
      
      <!-- 等级信息卡片 -->
      <view class="info-card level-card">
        <view class="card-header">
          <view class="level-badge">
            <text class="level-number">{{levelInfo.level}}</text>
          </view>
          <view class="level-info">
            <text class="level-title">{{levelInfo.title}}</text>
            <text class="level-subtitle">星际船长等级</text>
          </view>
        </view>
        
        <view class="progress-section">
          <view class="progress-info">
            <text class="progress-label">经验值</text>
            <text class="progress-value">{{levelInfo.experience}} / {{levelInfo.nextLevelExp}}</text>
          </view>
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{levelInfo.progress * 100}}%"></view>
          </view>
          <text class="progress-tip">还需 {{levelInfo.expToNext}} 经验升级</text>
        </view>
      </view>

      <!-- 统计数据网格 -->
      <view class="stats-grid">
        <view class="stat-item">
          <view class="stat-icon">⏰</view>
          <text class="stat-value">{{statsData.totalPlayTime}}</text>
          <text class="stat-label">游戏时长(小时)</text>
        </view>
        <view class="stat-item">
          <view class="stat-icon">📅</view>
          <text class="stat-value">{{statsData.joinDays}}</text>
          <text class="stat-label">加入天数</text>
        </view>
        <view class="stat-item">
          <view class="stat-icon">🏆</view>
          <text class="stat-value">{{statsData.achievementCount}}</text>
          <text class="stat-label">获得成就</text>
        </view>
        <view class="stat-item">
          <view class="stat-icon">🤝</view>
          <text class="stat-value">{{statsData.friendCount}}</text>
          <text class="stat-label">好友数量</text>
        </view>
      </view>
    </view>

    <!-- 个性化设置区域 -->
    <view class="personalization-section">
      <view class="section-header">
        <text class="section-title">🎨 个性化设置</text>
        <text class="section-subtitle">定制专属的个人空间</text>
      </view>
      
      <!-- 座右铭编辑 -->
      <view class="setting-card motto-card">
        <view class="card-title">
          <text class="title-icon">💭</text>
          <text class="title-text">个人座右铭</text>
        </view>
        <view class="motto-editor" bindtap="onEditMotto">
          <text class="motto-text">{{personalData.motto}}</text>
          <view class="edit-button">
            <text class="edit-text">编辑</text>
          </view>
        </view>
      </view>

      <!-- 个性签名编辑 -->
      <view class="setting-card signature-card">
        <view class="card-title">
          <text class="title-icon">✍️</text>
          <text class="title-text">个性签名</text>
        </view>
        <view class="signature-editor" bindtap="onEditSignature">
          <text class="signature-text">{{personalData.signature}}</text>
          <view class="edit-button">
            <text class="edit-text">编辑</text>
          </view>
        </view>
      </view>

      <!-- 兴趣标签 -->
      <view class="setting-card interests-card">
        <view class="card-title">
          <text class="title-icon">🏷️</text>
          <text class="title-text">兴趣标签</text>
        </view>
        <view class="interests-container">
          <view class="interest-tag" wx:for="{{personalData.interests}}" wx:key="index">
            <text class="tag-text">{{item}}</text>
          </view>
          <view class="add-interest-btn" bindtap="onAddInterest">
            <text class="add-text">+ 添加</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 回忆时光区域 -->
    <view class="memories-section">
      <view class="section-header">
        <text class="section-title">📸 回忆时光</text>
        <text class="section-subtitle">珍贵的成长瞬间</text>
      </view>
      
      <view class="memories-timeline">
        <view class="memory-item" wx:for="{{memoriesData}}" wx:key="id">
          <view class="memory-date">
            <text class="date-text">{{item.date}}</text>
          </view>
          <view class="memory-content">
            <view class="memory-icon">{{item.icon}}</view>
            <view class="memory-info">
              <text class="memory-title">{{item.title}}</text>
              <text class="memory-description">{{item.description}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    </scroll-view>

  </view>
</view>
