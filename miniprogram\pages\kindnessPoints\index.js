// 善意积分站页面
Page({
  data: {
    // 余额数据
    currentLoveEnergy: 0,
    currentKindnessPoints: 0,
    
    // 兑换选项
    exchangeOptions: [
      { id: 1, energy: 5, points: 10, rate: '1:2' },
      { id: 2, energy: 10, points: 25, rate: '1:2.5' },
      { id: 3, energy: 20, points: 60, rate: '1:3' },
      { id: 4, energy: 50, points: 200, rate: '1:4' }
    ],
    
    // 商店分类
    shopCategories: [
      { id: 'decorations', name: '装饰', icon: '🎨' },
      { id: 'badges', name: '徽章', icon: '🏆' },
      { id: 'tools', name: '工具', icon: '🛠️' },
      { id: 'special', name: '特殊', icon: '✨' }
    ],
    
    // 当前选中的商店分类
    selectedShopCategory: 'decorations',
    
    // 商店物品
    shopItems: {
      decorations: [
        { id: 'dec_001', name: '彩虹装饰', description: '为成长树添加美丽彩虹', icon: '🌈', price: 15, owned: false },
        { id: 'dec_002', name: '蝴蝶装饰', description: '吸引美丽蝴蝶到成长树', icon: '🦋', price: 20, owned: false },
        { id: 'dec_003', name: '花朵装饰', description: '让成长树开出美丽花朵', icon: '🌺', price: 12, owned: false },
        { id: 'dec_004', name: '星星装饰', description: '在成长树上挂满星星', icon: '⭐', price: 25, owned: false }
      ],
      badges: [
        { id: 'badge_001', name: '善意新手', description: '完成第一个善意行为', icon: '🌱', price: 30, owned: false },
        { id: 'badge_002', name: '爱心使者', description: '连续7天完成善意行为', icon: '💖', price: 50, owned: false },
        { id: 'badge_003', name: '社区英雄', description: '完成10个社区善意行为', icon: '🦸', price: 80, owned: false },
        { id: 'badge_004', name: '环保卫士', description: '完成15个环保善意行为', icon: '🌍', price: 100, owned: false }
      ],
      tools: [
        { id: 'tool_001', name: '善意提醒器', description: '每日提醒完成善意行为', icon: '⏰', price: 40, owned: false },
        { id: 'tool_002', name: '进度加速器', description: '善意行为获得双倍积分', icon: '⚡', price: 80, owned: false },
        { id: 'tool_003', name: '成长记录本', description: '详细记录善意成长历程', icon: '📖', price: 60, owned: false }
      ],
      special: [
        { id: 'special_001', name: '家庭徽章', description: '与家人一起获得的特殊徽章', icon: '👨‍👩‍👧‍👦', price: 150, owned: false },
        { id: 'special_002', name: '季节限定装饰', description: '当前季节的限定装饰', icon: '🎃', price: 200, owned: false }
      ]
    },
    
    // 当前显示的商店物品
    currentShopItems: [],
    
    // 积分任务
    pointsTasks: [
      {
        id: 'task_001',
        title: '每日善意',
        description: '连续7天完成善意行为',
        icon: '📅',
        reward: 50,
        current: 3,
        target: 7,
        progressPercentage: 42.8,
        completed: false,
        claimed: false
      },
      {
        id: 'task_002',
        title: '家庭和谐',
        description: '完成5个家庭善意行为',
        icon: '🏠',
        reward: 30,
        current: 5,
        target: 5,
        progressPercentage: 100,
        completed: true,
        claimed: false
      },
      {
        id: 'task_003',
        title: '环保先锋',
        description: '完成3个环保善意行为',
        icon: '🌱',
        reward: 25,
        current: 1,
        target: 3,
        progressPercentage: 33.3,
        completed: false,
        claimed: false
      }
    ],
    
    // 积分历史记录
    recentHistory: [
      { id: 1, title: '完成善意行为', time: '2小时前', amount: 5, type: 'earn', icon: '🌟' },
      { id: 2, title: '购买彩虹装饰', time: '昨天', amount: 15, type: 'spend', icon: '🌈' },
      { id: 3, title: '兑换积分', time: '2天前', amount: 25, type: 'earn', icon: '💝' }
    ],
    
    // 积分统计
    pointsStats: {
      totalEarned: 156,
      totalSpent: 45,
      tasksCompleted: 8,
      itemsOwned: 3
    },
    
    // 页面状态
    loading: false,
    showExchangeSuccess: false,
    exchangeSuccessMessage: ''
  },

  onLoad: function (options) {
    console.log('善意积分站加载');
    this.initializePointsStation();
  },

  onReady: function () {
    console.log('善意积分站渲染完成');
  },

  onShow: function () {
    // 每次显示时刷新余额
    this.loadUserBalance();
  },

  onPullDownRefresh: function () {
    this.refreshPointsData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  // 初始化积分站
  initializePointsStation() {
    this.setData({ loading: true });

    try {
      // 加载用户余额
      this.loadUserBalance();
      
      // 加载商店数据
      this.loadShopData();
      
      // 加载任务数据
      this.loadTasksData();
      
      // 加载历史记录
      this.loadHistoryData();
      
      // 加载统计数据
      this.loadStatsData();
      
    } catch (error) {
      console.error('初始化积分站失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载用户余额
  loadUserBalance() {
    const userData = wx.getStorageSync('userData') || {};
    const kindnessPoints = wx.getStorageSync('kindnessPoints') || 0;
    
    this.setData({
      currentLoveEnergy: userData.loveEnergy || 0,
      currentKindnessPoints: kindnessPoints
    });
  },

  // 加载商店数据
  loadShopData() {
    const ownedItems = wx.getStorageSync('ownedShopItems') || [];
    const shopItems = this.data.shopItems;
    
    // 更新物品拥有状态
    Object.keys(shopItems).forEach(category => {
      shopItems[category].forEach(item => {
        item.owned = ownedItems.includes(item.id);
      });
    });
    
    this.setData({
      shopItems,
      currentShopItems: shopItems[this.data.selectedShopCategory]
    });
  },

  // 加载任务数据
  loadTasksData() {
    const completedTasks = wx.getStorageSync('completedPointsTasks') || [];
    const claimedTasks = wx.getStorageSync('claimedPointsTasks') || [];
    
    const tasks = this.data.pointsTasks.map(task => ({
      ...task,
      completed: completedTasks.includes(task.id),
      claimed: claimedTasks.includes(task.id)
    }));
    
    this.setData({
      pointsTasks: tasks
    });
  },

  // 加载历史记录
  loadHistoryData() {
    const history = wx.getStorageSync('pointsHistory') || this.data.recentHistory;
    this.setData({
      recentHistory: history.slice(0, 5) // 只显示最近5条
    });
  },

  // 加载统计数据
  loadStatsData() {
    const stats = wx.getStorageSync('pointsStats') || this.data.pointsStats;
    this.setData({
      pointsStats: stats
    });
  },

  // 快速兑换
  onQuickExchange(e) {
    const option = e.currentTarget.dataset.option;
    
    if (this.data.currentLoveEnergy < option.energy) {
      wx.showModal({
        title: '爱心能量不足',
        content: `兑换需要${option.energy}点爱心能量，你当前只有${this.data.currentLoveEnergy}点。\n\n请先完成今日任务获取更多能量！`,
        confirmText: '去完成任务',
        cancelText: '知道了',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/dailyTasks/index'
            });
          }
        }
      });
      return;
    }

    wx.showModal({
      title: '确认兑换',
      content: `确定要用${option.energy}点爱心能量兑换${option.points}个善意积分吗？`,
      confirmText: '确认兑换',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.performExchange(option);
        }
      }
    });
  },

  // 执行兑换
  performExchange(option) {
    const userData = wx.getStorageSync('userData') || {};
    const currentPoints = wx.getStorageSync('kindnessPoints') || 0;
    
    // 扣除爱心能量
    userData.loveEnergy = (userData.loveEnergy || 0) - option.energy;
    wx.setStorageSync('userData', userData);
    
    // 增加善意积分
    const newPoints = currentPoints + option.points;
    wx.setStorageSync('kindnessPoints', newPoints);
    
    // 记录历史
    this.addHistoryRecord({
      title: '兑换积分',
      amount: option.points,
      type: 'earn',
      icon: '💝'
    });
    
    // 更新界面
    this.setData({
      currentLoveEnergy: userData.loveEnergy,
      currentKindnessPoints: newPoints
    });
    
    // 显示成功动画
    this.showExchangeSuccessAnimation(`成功兑换${option.points}个善意积分！`);
  },

  // 显示兑换成功动画
  showExchangeSuccessAnimation(message) {
    this.setData({
      showExchangeSuccess: true,
      exchangeSuccessMessage: message
    });
    
    setTimeout(() => {
      this.setData({
        showExchangeSuccess: false
      });
    }, 2000);
  },

  // 添加历史记录
  addHistoryRecord(record) {
    const history = wx.getStorageSync('pointsHistory') || [];
    const newRecord = {
      ...record,
      id: Date.now(),
      time: this.formatTime(new Date())
    };
    
    history.unshift(newRecord);
    wx.setStorageSync('pointsHistory', history.slice(0, 50)); // 保留最近50条
    
    this.loadHistoryData();
  },

  // 格式化时间
  formatTime(date) {
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return '刚刚';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
    return `${Math.floor(diff / 86400000)}天前`;
  },

  // 选择商店分类
  onSelectShopCategory(e) {
    const categoryId = e.currentTarget.dataset.category;
    this.setData({
      selectedShopCategory: categoryId,
      currentShopItems: this.data.shopItems[categoryId]
    });
  },

  // 购买物品
  onPurchaseItem(e) {
    const item = e.currentTarget.dataset.item;
    
    if (item.owned) {
      wx.showToast({
        title: '已拥有该物品',
        icon: 'none'
      });
      return;
    }
    
    if (this.data.currentKindnessPoints < item.price) {
      wx.showModal({
        title: '积分不足',
        content: `购买${item.name}需要${item.price}个积分，你当前只有${this.data.currentKindnessPoints}个积分。`,
        showCancel: false
      });
      return;
    }

    wx.showModal({
      title: '确认购买',
      content: `确定要花费${item.price}个积分购买${item.name}吗？\n\n${item.description}`,
      confirmText: '确认购买',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.performPurchase(item);
        }
      }
    });
  },

  // 执行购买
  performPurchase(item) {
    const currentPoints = wx.getStorageSync('kindnessPoints') || 0;
    const ownedItems = wx.getStorageSync('ownedShopItems') || [];
    
    // 扣除积分
    const newPoints = currentPoints - item.price;
    wx.setStorageSync('kindnessPoints', newPoints);
    
    // 添加到拥有物品
    ownedItems.push(item.id);
    wx.setStorageSync('ownedShopItems', ownedItems);
    
    // 记录历史
    this.addHistoryRecord({
      title: `购买${item.name}`,
      amount: item.price,
      type: 'spend',
      icon: item.icon
    });
    
    // 更新界面
    this.setData({
      currentKindnessPoints: newPoints
    });
    
    this.loadShopData();
    
    wx.showToast({
      title: '购买成功！',
      icon: 'success'
    });
  },

  // 查看任务
  onViewTask(e) {
    const task = e.currentTarget.dataset.task;
    
    if (task.completed && !task.claimed) {
      // 可以领取奖励
      this.claimTaskReward(task);
    } else {
      // 显示任务详情
      wx.showModal({
        title: task.title,
        content: `${task.description}\n\n进度：${task.current}/${task.target}\n奖励：${task.reward}个积分`,
        showCancel: false
      });
    }
  },

  // 领取任务奖励
  claimTaskReward(task) {
    wx.showModal({
      title: '领取奖励',
      content: `恭喜完成任务"${task.title}"！\n\n获得奖励：${task.reward}个积分`,
      confirmText: '领取',
      cancelText: '稍后',
      success: (res) => {
        if (res.confirm) {
          const currentPoints = wx.getStorageSync('kindnessPoints') || 0;
          const claimedTasks = wx.getStorageSync('claimedPointsTasks') || [];
          
          // 增加积分
          wx.setStorageSync('kindnessPoints', currentPoints + task.reward);
          
          // 标记任务已领取
          claimedTasks.push(task.id);
          wx.setStorageSync('claimedPointsTasks', claimedTasks);
          
          // 记录历史
          this.addHistoryRecord({
            title: `完成任务：${task.title}`,
            amount: task.reward,
            type: 'earn',
            icon: task.icon
          });
          
          // 更新界面
          this.setData({
            currentKindnessPoints: currentPoints + task.reward
          });
          
          this.loadTasksData();
          
          wx.showToast({
            title: '奖励已领取！',
            icon: 'success'
          });
        }
      }
    });
  },

  // 查看全部历史
  onViewAllHistory() {
    wx.showModal({
      title: '积分历史',
      content: '查看全部积分获得和消费记录',
      showCancel: false
    });
  },

  // 刷新数据
  refreshPointsData() {
    this.setData({ loading: true });
    
    setTimeout(() => {
      this.loadUserBalance();
      this.loadShopData();
      this.loadTasksData();
      this.loadHistoryData();
      this.loadStatsData();
      this.setData({ loading: false });
    }, 1000);
  }
});
